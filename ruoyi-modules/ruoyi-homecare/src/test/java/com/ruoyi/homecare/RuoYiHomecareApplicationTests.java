package com.ruoyi.homecare;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.ruoyi.homecare.goodsOrder.mapper.vo.CommonVoMapper;
import com.ruoyi.homecare.goodsOrder.vo.HomeOrderBaseInfoListProviderVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;


@SpringBootTest
class RuoYiHomecareApplicationTests {

    @Autowired
    private CommonVoMapper commonVoMapper;

    public static void main(String[] args) {


    }

    @Test
    @Transactional
    @Rollback(false)
        // 数据库修改数据
    void contextLoads() throws WxPayException {

        Page<HomeOrderBaseInfoListProviderVo> page = new Page<>(1, 2);


    }


}
