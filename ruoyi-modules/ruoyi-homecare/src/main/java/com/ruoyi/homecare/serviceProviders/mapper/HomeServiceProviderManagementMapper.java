package com.ruoyi.homecare.serviceProviders.mapper;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.domain.vo.AppServiceProviderManagementVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 服务商管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-14
 */
@Mapper
public interface HomeServiceProviderManagementMapper extends BaseMapper<HomeServiceProviderManagement> {
    /**
     * 查询服务商管理
     *
     * @param id 服务商管理主键
     * @return 服务商管理
     */
    public HomeServiceProviderManagement selectHomeServiceProviderManagementById(Long id);

    /**
     * 查询服务商管理列表
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 服务商管理集合
     */
    public List<HomeServiceProviderManagement> selectHomeServiceProviderManagementList(HomeServiceProviderManagement homeServiceProviderManagement);

    /**
     * 新增服务商管理
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 结果
     */
    public int insertHomeServiceProviderManagement(HomeServiceProviderManagement homeServiceProviderManagement);

    /**
     * 修改服务商管理
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 结果
     */
    public int updateHomeServiceProviderManagement(HomeServiceProviderManagement homeServiceProviderManagement);

    /**
     * 删除服务商管理
     *
     * @param id 服务商管理主键
     * @return 结果
     */
    public int deleteHomeServiceProviderManagementById(Long id);

    /**
     * 批量删除服务商管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeServiceProviderManagementByIds(Long[] ids);

    /**
     * 获取全量服务商管理KeyValue
     *
     * @param name
     * @return
     */
    List<JSONObject> getServiceProviderList(String name);

    /**
     * 根据系统用户id查询服务商管理
     *
     * @param userId 系统用户id
     * @return
     */
    HomeServiceProviderManagement getServiceProviderBySysUserId(Long userId);

    /**
     * app通过类别标签查询服务商列表
     *
     * @param homeServiceProviderManagement
     * @return
     */
    List<AppServiceProviderManagementVo> getServiceProviderListInfo(AppServiceProviderManagementVo homeServiceProviderManagement);
}
