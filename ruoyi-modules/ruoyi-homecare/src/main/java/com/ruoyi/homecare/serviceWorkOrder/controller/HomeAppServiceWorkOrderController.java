package com.ruoyi.homecare.serviceWorkOrder.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.log.enums.OperatorType;
import com.ruoyi.homecare.elderlyPeople.domain.vo.UserDataInfoResult;
import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import com.ruoyi.homecare.serviceWorkOrder.service.HomeServiceWorkOrderService;
import com.ruoyi.homecare.serviceWorkOrder.vo.HomeAppServiceWorkOrderInfoRequestVo;
import com.ruoyi.homecare.serviceWorkOrder.vo.HomeServiceWorkOrderRequestVo;
import com.ruoyi.homecare.utils.SysUserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_service_work_order控制器
 * @date 2022-07-18
 */
@Slf4j
@RestController
@RequestMapping("/homeAppServiceWorkOrder")
@Api(tags = "服务人员移动端服务模块")
public class HomeAppServiceWorkOrderController extends BaseController {

    @Autowired
    private HomeServiceWorkOrderService homeServiceWorkOrderService;

    @ApiOperation("服务人员移动待完成服务列表")
    @GetMapping("/getNotCompletedServiceWorkOrderListByWork")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = true, dataTypeClass = String.class),
    })
    public TableDataInfo<HomeOrderServiceWork> getNotCompletedServiceWorkOrderListByWork(@ApiIgnore HomeOrderServiceWork homeOrderServiceWork) {
        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId(null);
        Long workId = infoBySysUserId.getWorkId();
        homeOrderServiceWork.setWorkerId(workId);
        startPage();
        List<HomeOrderServiceWork> list = homeServiceWorkOrderService.getNotCompletedServiceWorkOrderListByWork(homeOrderServiceWork);
        return getDataTable(list);
    }

    @ApiOperation("服务人员移动服务详情")
    @GetMapping("/getServiceWorkOrderInfoById")
    public TAjaxResult<HomeAppServiceWorkOrderInfoRequestVo> getServiceWorkOrderInfoById(String id) {
        startPage();
        HomeAppServiceWorkOrderInfoRequestVo info = homeServiceWorkOrderService.getServiceWorkOrderInfoById(id);
        return new TAjaxResult(info);
    }

    @ApiOperation("服务人员移动已完成服务列表")
    @GetMapping("/getCompletedServiceWorkOrderListByWork")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = true, dataTypeClass = String.class),
    })
    public TableDataInfo<HomeOrderServiceWork> getCompletedServiceWorkOrderListByWork(@ApiIgnore HomeOrderServiceWork homeOrderServiceWork) {
        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId(null);
        Long workId = infoBySysUserId.getWorkId();
        homeOrderServiceWork.setWorkerId(workId);
        startPage();
        List<HomeOrderServiceWork> list = homeServiceWorkOrderService.getCompletedServiceWorkOrderListByWork(homeOrderServiceWork);
        return getDataTable(list);
    }

    /**
     * 移动端服务人员开始服务
     */
    @PostMapping("/startService")
    @ApiOperation(value = "移动端服务人员开始服务")
    @Log(platform = "2", title = "移动端服务人员开始服务", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult startService(@RequestBody HomeServiceWorkOrderRequestVo requestVo) {
        int i = homeServiceWorkOrderService.startService(requestVo);
        return toAjax(i);
    }

    /**
     * 移动端服务人员现场照片保存
     */
    @PostMapping("/servingUpload")
    @ApiOperation(value = "移动端服务人员现场照片保存")
    @Log(platform = "2", title = "移动端服务人员现场照片保存", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult servingUpload(@RequestBody HomeServiceWorkOrderRequestVo requestVo) {
        int i = homeServiceWorkOrderService.servingUpload(requestVo);
        return toAjax(i);
    }

    /**
     * 移动端服务人员服务签退
     */
    @PostMapping("/endService")
    @ApiOperation(value = "移动端服务人员服务签退")
    @Log(platform = "2", title = "移动端服务人员服务签退", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult endService(@RequestBody HomeServiceWorkOrderRequestVo requestVo) {
        int i = homeServiceWorkOrderService.endService(requestVo);
        return toAjax(i);
    }


}
