package com.ruoyi.homecare.elderlyPeople.service;

import cn.hutool.json.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.homecare.elderlyPeople.domain.vo.ElderlyPeopleInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 老人基础信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
public interface IHomeCareElderlyPeopleInfoService {
    /**
     * 查询老人基础信息
     *
     * @param id 老人基础信息主键
     * @return 老人基础信息
     */
    public ElderlyPeopleInfo selectElderlyPeopleInfoById(String id);

    /**
     * 通过系统userid查询老人基础信息
     *
     * @param sysUserId 老人基础信息主键
     * @return 老人基础信息
     */
    public ElderlyPeopleInfo getElderlyPeopleInfoBySysUserId(Long sysUserId);

    /**
     * 查询老人基础信息列表
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 老人基础信息集合
     */
    public List<ElderlyPeopleInfo> selectElderlyPeopleInfoList(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 健康管理获取老人全量列表
     *
     * @param name
     * @return
     */
    public List<JSONObject> getUserList(@Param("name") String name, @Param("state") String state);

    /**
     * 新增老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    public String insertElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 修改老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    public int updateElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 批量删除老人基础信息
     *
     * @param ids 需要删除的老人基础信息主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleInfoByIds(Long[] ids);


    public int logicalDeleteElderlyPeopleInfoByIds(String[] ids);

    /**
     * 删除老人基础信息信息
     *
     * @param id 老人基础信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleInfoById(Long id);

}
