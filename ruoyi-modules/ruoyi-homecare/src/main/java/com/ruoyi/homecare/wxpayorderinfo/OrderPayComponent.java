package com.ruoyi.homecare.wxpayorderinfo;

import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderBaseInfo;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.settlement.domain.HomeAmountChangeRecord;
import com.ruoyi.homecare.settlement.service.IHomeAmountChangeRecordService;
import com.ruoyi.homecare.wexinjavapay.components.WxPayComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * @ClassName OrderPayComm
 * @Description
 * <AUTHOR>
 * @Date 2022/8/12 10:56
 */
@Component
public class OrderPayComponent {

    /**
     * 付款成功通知回调地址
     */
    private static final String base_notify_url = "https://www.xinjuncheng.cn/yljs/homecare/wechatPay/notify";
    /**
     * 付款成功通知回调地址
     */
    private static final String pay_notify_url = base_notify_url + "/order";
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private WxPayComponent wxPayComponent;
    @Autowired
    private IHomeAmountChangeRecordService iHomeAmountChangeRecordService;


    /**
     * 余额支付
     *
     * @param elderlyPeopleId 老人id
     * @param totalFree1Old   总金额
     * @param originalNumber  原单号
     * @return true 付款成功 反之失败
     */
    public boolean balancePayment(String elderlyPeopleId, BigDecimal totalFree1Old, String originalNumber) {
        /**
         * 老人金额变动
         */
        HomeAmountChangeRecord homeAmountChangeRecord = new HomeAmountChangeRecord();
        homeAmountChangeRecord.setUserId(elderlyPeopleId);
        homeAmountChangeRecord.setType("2");
        homeAmountChangeRecord.setChangedAmount(totalFree1Old);
        homeAmountChangeRecord.setOriginalNumber(originalNumber);
        int i = iHomeAmountChangeRecordService.insertHomeAmountChangeRecord(homeAmountChangeRecord);// 新增余额变动记录并扣除用户余额
        if (i != 1) {
            return false;// 付款失败
        }
        return true;
    }

    /**
     * 余额退款
     *
     * @param elderlyPeopleId 老人id
     * @param totalFree1Old   总金额
     * @param originalNumber  原单号
     * @return true 付款成功 反之失败
     */
    public boolean balanceRefund(String elderlyPeopleId, BigDecimal totalFree1Old, String originalNumber) {
        /**
         * 老人金额变动
         */
        HomeAmountChangeRecord homeAmountChangeRecord = new HomeAmountChangeRecord();
        homeAmountChangeRecord.setUserId(elderlyPeopleId);
        homeAmountChangeRecord.setType("3");
        homeAmountChangeRecord.setChangedAmount(totalFree1Old);
        homeAmountChangeRecord.setOriginalNumber(originalNumber);
        int i = iHomeAmountChangeRecordService.insertHomeAmountChangeRecord(homeAmountChangeRecord);// 新增余额变动记录并扣除用户余额
        if (i != 1) {
            return false;// 付款失败
        }
        return true;
    }


}
