package com.ruoyi.homecare.securityguard.domain;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 厂商信息对象 t_security_guard_manufacturer_info
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
public class SecurityGuardManufacturerInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 厂商名称
     */
    @Excel(name = "厂商名称")
    private String name;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String number;

    /**
     * 联系地址
     */
    @Excel(name = "联系地址")
    private String address;

    /**
     * 设备信息信息
     */
    private List<SecurityGuardDeviceInfo> securityGuardDeviceInfoList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public List<SecurityGuardDeviceInfo> getSecurityGuardDeviceInfoList() {
        return securityGuardDeviceInfoList;
    }

    public void setSecurityGuardDeviceInfoList(List<SecurityGuardDeviceInfo> securityGuardDeviceInfoList) {
        this.securityGuardDeviceInfoList = securityGuardDeviceInfoList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("contacts", getContacts())
                .append("number", getNumber())
                .append("address", getAddress())
                .append("securityGuardDeviceInfoList", getSecurityGuardDeviceInfoList())
                .toString();
    }
}
