package com.ruoyi.homecare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthManagement;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleHealthManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家老人健康管理信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@RestController
@RequestMapping("/homeCareHealthManagement")
@Api(value = "老人管理-居家老人健康管理信息Controller", tags = "老人管理-居家老人健康管理信息")
@ApiIgnore
public class HomeCareElderlyPeopleHealthManagementController extends BaseController {
    @Autowired
    private IHomeCareElderlyPeopleHealthManagementService elderlyPeopleHealthManagementService;

    /**
     * 查询居家老人健康管理信息列表
     */
    //@RequiresPermissions("elderlyPeople:healthManagement:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询老人基础信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        startPage();
        List<ElderlyPeopleHealthManagement> list = elderlyPeopleHealthManagementService.selectElderlyPeopleHealthManagementList(elderlyPeopleHealthManagement);
        return getDataTable(list);
    }

    /**
     * 查询居家老人健康管理信息列表
     */
    //@RequiresPermissions("elderlyPeople:healthManagement:list")
    @GetMapping("/getUserHealthManagementInfo")
    @ApiOperation(value = "根据老人id查询居家老人健康管理信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = true, dataTypeClass = String.class),
    })
    public AjaxResult getUserHealthManagementInfo(@ApiIgnore ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        startPage();
        List<ElderlyPeopleHealthManagement> list = elderlyPeopleHealthManagementService.selectElderlyPeopleHealthManagementList(elderlyPeopleHealthManagement);
        if (list.size() > 0) {
            return AjaxResult.success().put("data", list.get(0));
        } else {
            return AjaxResult.success().put("data", list);
        }
    }

    /**
     * 导出居家老人健康管理信息列表
     */
    //@RequiresPermissions("elderlyPeople:healthManagement:export")
    @Log(platform = "2", title = "居家老人健康管理信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出居家老人健康管理信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        List<ElderlyPeopleHealthManagement> list = elderlyPeopleHealthManagementService.selectElderlyPeopleHealthManagementList(elderlyPeopleHealthManagement);
        ExcelUtil<ElderlyPeopleHealthManagement> util = new ExcelUtil<ElderlyPeopleHealthManagement>(ElderlyPeopleHealthManagement.class);
        util.exportExcel(response, list, "居家老人健康管理信息数据");
    }

    /**
     * 获取居家老人健康管理信息详细信息
     */
    //@RequiresPermissions("elderlyPeople:healthManagement:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取居家老人健康管理信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(elderlyPeopleHealthManagementService.selectElderlyPeopleHealthManagementById(id));
    }


    /**
     * 保存居家老人健康管理信息
     */
    //@RequiresPermissions("elderlyPeople:healthManagement:add")
    @ApiOperation(value = "保存居家老人健康管理信息")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        return toAjax(elderlyPeopleHealthManagementService.save(elderlyPeopleHealthManagement));
    }


    /**
     * 新增居家老人健康管理信息
     */
    //@RequiresPermissions("elderlyPeople:healthManagement:add")
    @Log(platform = "2", title = "居家老人健康管理信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增居家老人健康管理信息")
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        return toAjax(elderlyPeopleHealthManagementService.insertElderlyPeopleHealthManagement(elderlyPeopleHealthManagement));
    }

    /**
     * 修改居家老人健康管理信息
     */
    //@RequiresPermissions("elderlyPeople:healthManagement:edit")
    @Log(platform = "2", title = "居家老人健康管理信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改居家老人健康管理信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        return toAjax(elderlyPeopleHealthManagementService.updateElderlyPeopleHealthManagement(elderlyPeopleHealthManagement));
    }

    /**
     * 删除居家老人健康管理信息
     */
    //@RequiresPermissions("elderlyPeople:healthManagement:remove")
    @Log(platform = "2", title = "居家老人健康管理信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除居家老人健康管理信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(elderlyPeopleHealthManagementService.deleteElderlyPeopleHealthManagementByIds(ids));
    }
}
