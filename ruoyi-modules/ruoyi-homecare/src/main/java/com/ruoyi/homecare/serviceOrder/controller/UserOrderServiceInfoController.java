package com.ruoyi.homecare.serviceOrder.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboInfo;
import com.ruoyi.homecare.serviceOrder.param.ComboDetailsByOrderIdParam;
import com.ruoyi.homecare.serviceOrder.param.HomeOrderServiceComboParam01;
import com.ruoyi.homecare.serviceOrder.param.UserComboDetailsParam;
import com.ruoyi.homecare.serviceOrder.param.UserComboParam;
import com.ruoyi.homecare.serviceOrder.service.HomeOrderServiceInfoService;
import com.ruoyi.homecare.serviceOrder.vo.*;
import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description home_order_service_info控制器
 * @date 2022-07-14
 */
@Slf4j
@Api(tags = "用户服务订单")
@RestController
@RequestMapping("/userOrderServiceInfo")
public class UserOrderServiceInfoController extends BaseController {

    @Autowired
    private HomeOrderServiceInfoService homeOrderServiceInfoService;

    /**
     * 生成服务订单
     */
    @ApiOperation(value = "生成服务订单")
    @Log(platform = "2", title = "生成服务订单", businessType = BusinessType.INSERT)
    @PostMapping("saveServiceOrder")
    public TAjaxResult saveServiceOrder(HttpServletRequest request, @RequestBody HomeOrderServiceRequestVo homeOrderServiceRequestVo) {
        TAjaxResult tAjaxResult = homeOrderServiceInfoService.saveServiceOrder(request, homeOrderServiceRequestVo);
        return tAjaxResult;
    }

    /**
     * 生成服务套餐订单
     */
    @ApiOperation(value = "服务套餐下单")
    @Log(platform = "2", title = "生成服务套餐订单", businessType = BusinessType.INSERT)
    @PostMapping("ServiceComboOrder")
    public TAjaxResult saveServiceComboOrder(HttpServletRequest request, @RequestBody HomeOrderServiceComboRequestVo homeOrderServiceComboRequestVo) {
        return homeOrderServiceInfoService.saveServiceComboOrder(request, homeOrderServiceComboRequestVo);
    }

    /**
     * 使用服务套餐*(使用原来套餐中的次数并生成订单)
     */
    @ApiOperation(value = "使用服务套餐")
    @Log(platform = "2", title = "使用服务套餐", businessType = BusinessType.INSERT)
    @PostMapping("serviceComboOrderUse")
    public AjaxResult serviceComboOrderUse(@RequestBody HomeOrderServiceComboParam01 homeOrderServiceComboParam01) {
        return AjaxResult.success(homeOrderServiceInfoService.serviceComboOrderUse(homeOrderServiceComboParam01));
    }

    @ApiOperation(value = "用户-查询购买的套餐")
    @Log(platform = "2", title = "用户-查询购买的套餐", businessType = BusinessType.INSERT)
    @GetMapping("getUserCombo")
    public TableDataInfo<HomeOrderServiceComboInfo> getUserCombo(UserComboParam userComboParam) {
        return homeOrderServiceInfoService.getUserCombo(userComboParam);
    }

    @ApiOperation(value = "用户-根据套餐订单id获取套餐订单明细")
    @GetMapping("getUserComboDetails")
    public TAjaxResult<UserComboDetailsVo> getUserComboDetails(UserComboDetailsParam userComboParam) {
        return homeOrderServiceInfoService.getUserComboDetails(userComboParam);
    }

    @ApiOperation(value = "用户-根据套餐订单id获取套餐订单明细")
    @GetMapping("getComboDetailsByOrderId")
    public TAjaxResult<ComboDetailsByOrderIdVo> getComboDetailsByOrderId(ComboDetailsByOrderIdParam comboDetailsByOrderIdParam) {
        return homeOrderServiceInfoService.getComboDetailsByOrderId(comboDetailsByOrderIdParam);
    }

    @ApiOperation(value = "用户-获取工单列表")
    @GetMapping("geWorkList")
    public TableDataInfo<HomeOrderServiceWork> geWorkList(ComboUseListVo comboUseListVo) {
        return homeOrderServiceInfoService.geWorkList(comboUseListVo);
    }

    @ApiOperation(value = "用户-获取工单详情")
    @GetMapping("getWorkInfo")
    public TAjaxResult<HomeOrderServiceWork> getComboDetailsInfo(ComboDetailsInfoVo comboDetailsInfoVo) {
        return homeOrderServiceInfoService.getComboDetailsInfo(comboDetailsInfoVo);
    }


}
