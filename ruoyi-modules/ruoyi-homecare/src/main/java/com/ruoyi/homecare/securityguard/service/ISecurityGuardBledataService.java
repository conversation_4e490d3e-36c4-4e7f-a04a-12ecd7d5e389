package com.ruoyi.homecare.securityguard.service;

import cn.hutool.json.JSONObject;
import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardBledata;

import java.util.List;

/**
 * 血压信息Service接口
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
public interface ISecurityGuardBledataService extends MPJBaseService<SecurityGuardBledata> {
    /**
     * 查询血压信息
     *
     * @param id 血压信息主键
     * @return 血压信息
     */
    public SecurityGuardBledata selectSecurityGuardBledataById(Long id);

    /**
     * 查询血压信息列表
     *
     * @param securityGuardBledata 血压信息
     * @return 血压信息集合
     */
    public List<SecurityGuardBledata> selectSecurityGuardBledataList(SecurityGuardBledata securityGuardBledata);

    /**
     * 新增血压信息
     *
     * @param securityGuardBledata 血压信息
     * @return 结果
     */
    public int insertSecurityGuardBledata(SecurityGuardBledata securityGuardBledata);

    /**
     * 修改血压信息
     *
     * @param securityGuardBledata 血压信息
     * @return 结果
     */
    public int updateSecurityGuardBledata(SecurityGuardBledata securityGuardBledata);

    /**
     * 批量删除血压信息
     *
     * @param ids 需要删除的血压信息主键集合
     * @return 结果
     */
    public int deleteSecurityGuardBledataByIds(Long[] ids);

    /**
     * 删除血压信息信息
     *
     * @param id 血压信息主键
     * @return 结果
     */
    public int deleteSecurityGuardBledataById(Long id);

    int saveMsg(JSONObject argument, String mac);
}
