package com.ruoyi.homecare.serviceOrder.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description home_order_service_combo_info
 * @date 2022-07-14
 */
@Data
@ApiModel("t_home_order_service_combo_info")
@TableName("t_home_order_service_combo_info")
public class HomeOrderServiceComboInfo implements Serializable {

    // 完成状态 1:有剩余 2：已用完
    public static final int COMPLETED_STATUS_HAS = 1;
    public static final int COMPLETED_STATUS_NONE = 2;
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("服务商id")
    private Long providerId;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private String orderId;


    /**
     * 服务套餐id
     */
    @ApiModelProperty("服务套餐id")
    private Long serviceComboId;


    /**
     * 服务套餐名称
     */
    @ApiModelProperty("服务套餐名称")
    private String serviceComboName;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 老人id
     */
    @ApiModelProperty("老人id")
    private String elderlyPeopleId;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal price;

    /**
     * 图片
     */
    @ApiModelProperty("图片")
    private String img;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String describeMsg;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date updateTime;

    /**
     * 完成状态
     */
    @ApiModelProperty("完成状态 1:有剩余 2：已用完")
    private int completedStatus;

    /**
     * 过期时间
     */
    @ApiModelProperty("过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date expiredDate;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;

    /**
     * 联系人手机号
     */
    @ApiModelProperty("联系人手机号")
    private String phone;

    /**
     * 联系人名称
     */
    @ApiModelProperty("联系人名称")
    private String name;

    /**
     * 数量（几个套餐）
     */
    @ApiModelProperty("数量（几个套餐）")
    private String number;

    /**
     * 总价
     */
    @ApiModelProperty("总价")
    private BigDecimal totalPrice;

    /**
     * 状态 1:正常 2：已过期
     */
    @ApiModelProperty("状态 1:正常 2：已过期")
    private transient Integer status = 1;

    @ApiModelProperty("状态Label")
    private transient String statusLabel = "正常";

    public String getStatusLabel() {
        if (status == 2) {
            statusLabel = "已过期";
        }
        return statusLabel;
    }

    public Integer getStatus() {// 通过时间进行查询
        long expiredDateTime = expiredDate.getTime();
        long currentTimeMillis = System.currentTimeMillis();// 当前时间戳
        if (currentTimeMillis > expiredDateTime) {
            status = 2;// 2：已过期
        }
        return status;
    }
}
