package com.ruoyi.homecare.elderlyPeople.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 老人家属信息对象 t_elderly_people_family_info
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@ApiModel(value = "老人家属信息")
public class ElderlyPeopleFamilyInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 家属姓名
     */
    @ApiModelProperty(value = "家属姓名")
    @Excel(name = "家属姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @Excel(name = "手机号")
    private String phone;

    /**
     * 关系
     */
    @ApiModelProperty(value = "关系")
    @Excel(name = "关系")
    private String relation;

    /**
     * 是否与老人同住
     */
    @ApiModelProperty(value = "是否与老人同住")
    @Excel(name = "是否与老人同住")
    private String liveWithStatus;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 老人基础信息id
     */
    @ApiModelProperty(value = "老人基础信息id")
    @Excel(name = "老人基础信息id")
    private String userId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getLiveWithStatus() {
        return liveWithStatus;
    }

    public void setLiveWithStatus(String liveWithStatus) {
        this.liveWithStatus = liveWithStatus;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("phone", getPhone())
                .append("relation", getRelation())
                .append("liveWithStatus", getLiveWithStatus())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .append("userId", getUserId())
                .toString();
    }
}
