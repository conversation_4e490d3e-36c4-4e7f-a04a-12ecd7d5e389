package com.ruoyi.homecare.serviceOrder.param;

import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import com.ruoyi.homecare.utils.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName
 * @Description
 * <AUTHOR>
 * @Date 2022/8/05 14:51
 */
@Data
@ApiModel(description = "管理端PC服务列表订单参数")
public class HomeAdminOrderServiceInfoParam extends BasePageRequest {


    @ApiModelProperty("工作人员名称")
    private String workName;
    @ApiModelProperty("开始时间")
    private Date beginTime;
    @ApiModelProperty("结束时间")
    private Date endTime;

}
