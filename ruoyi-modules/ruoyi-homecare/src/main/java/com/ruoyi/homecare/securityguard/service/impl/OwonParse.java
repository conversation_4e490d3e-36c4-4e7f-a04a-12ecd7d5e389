package com.ruoyi.homecare.securityguard.service.impl;

import static java.lang.Math.pow;

public class OwonParse {

    public static void main(String[] args) {

        int parameterValue = 49229;

        double a = parseGlucose(parameterValue);

        System.out.println("原始值=" + parameterValue + "为 " + a + "mmol/L");

    }


    public static double parseGlucose(int value) {

        int c = value >> 12;///取出c
        int leftThree = (value & 0xFFF);///取出后三位数

        int num = 16 - c;
        double resultNumber = leftThree * pow(10, 3) / pow(10, num);///按公式进行运算

        return resultNumber;
    }


    public static String parseStatus(int status) {
        String str = null;
        // 判断bit0或者bit1 是否有为1的
        if (((status & 0x01) == 0x01) || ((status & 0x02) == 0x02)) {// 说明是报警状态
            System.out.println("status=" + status + " 为 报警状态");
            str = "报警";
        } else {
            System.out.println("status=" + status + " 为 正常状态");
            str = "正常";
        }
        // 判断bit2 是否为1
        if ((status & 0x04) == 0x04) {// 说明是设备被拆
            System.out.println("status=" + status + " 为 设备被拆状态");
            str = "设备被拆";
        }
        // 判断bit3 是否为1
        if ((status & 0x08) == 0x08) {// 说明是低电量
            System.out.println("status=" + status + " 为 低电量状态");
            str = "低电量";
        }
        return str;
    }
}
