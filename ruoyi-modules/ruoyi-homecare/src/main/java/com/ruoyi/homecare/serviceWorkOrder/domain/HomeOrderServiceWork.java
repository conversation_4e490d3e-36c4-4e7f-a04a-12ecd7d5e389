package com.ruoyi.homecare.serviceWorkOrder.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description home_service_work_order
 * @date 2022-07-20
 */
@Data
@ApiModel("t_home_order_service_work")
@TableName("t_home_order_service_work")
public class HomeOrderServiceWork implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    private String id;

    /**
     * 服务类型(0服务，1服务套餐)
     */
    @ApiModelProperty("服务类型(0服务，1服务套餐)")
    private Integer serviceType;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderId;

    /**
     * 下单方式
     */
    @ApiModelProperty("下单方式")
    private Integer placeOrderWay;

    /**
     * 老人id
     */
    @ApiModelProperty("老人id")
    private String elderlyPeopleId;

    /**
     * 老人姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 老人联系方式
     */
    @ApiModelProperty("联系方式")
    private String phone;

    /**
     * 工作人员id
     */
    @ApiModelProperty("工作人员id")
    private Long workerId;

    /**
     * 服务人员手机号
     */
    @ApiModelProperty("服务人员手机号")
    private String workerPhone;

    /**
     * 工单状态
     */
    @ApiModelProperty("工单状态 1待指派人员，2未开始，3服务中，4已完成 ")
    private Integer status;

    /**
     * 预约时间
     */
    @ApiModelProperty("预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date reserveTime;

    /**
     * 商家id
     */
    @ApiModelProperty("商家id")
    private Long serviceProviderId;

    /**
     * 服务id
     */
    @ApiModelProperty("服务id")
    private Long serviceId;

    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    private String serviceName;

    /**
     * 套餐id
     */
    @ApiModelProperty("套餐id")
    private Long serviceComboId;

    /**
     * 服务地址
     */
    @ApiModelProperty("服务地址")
    private String serviceAddress;

    /**
     * 服务开始时间
     */
    @ApiModelProperty("服务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date startTime;

    /**
     * 开始照片
     */
    @ApiModelProperty("开始照片")
    private String startImg;

    /**
     * 服务结束时间
     */
    @ApiModelProperty("服务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date endTime;


    /**
     * 现场图片
     */
    @ApiModelProperty("现场图片")
    private String liveImg;

    /**
     * 结束照片
     */
    @ApiModelProperty("结束照片")
    private String endImg;

    /**
     * 服务时长
     */
    @ApiModelProperty("服务时长")
    private Integer serviceTime;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;

    /**
     * 创建人员
     */
    @ApiModelProperty("创建人员")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date updateTime;

    /**
     * 修改人员
     */
    @ApiModelProperty("修改人员")
    private String updateBy;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty("逻辑删除标记（0：显示；1：隐藏")
    private String delFlag;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 备注
     */
    @ApiModelProperty("数量")
    private Integer number;

    @ApiModelProperty("计费方式（字典） 0：次 1：件 2：小时")
    private Integer chargeMode;

    @ApiModelProperty("套餐信息id")
    private long orderServiceComboId;

    @ApiModelProperty("套餐信息-细则id")
    private long orderServiceComboDetailsId;

}
