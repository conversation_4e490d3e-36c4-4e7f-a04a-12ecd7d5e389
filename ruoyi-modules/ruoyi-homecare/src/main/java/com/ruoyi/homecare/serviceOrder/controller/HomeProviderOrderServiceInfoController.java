package com.ruoyi.homecare.serviceOrder.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboDetailsInfo;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboInfo;
import com.ruoyi.homecare.serviceOrder.mapper.HomeOrderServiceComboInfoMapper;
import com.ruoyi.homecare.serviceOrder.param.EtSoldComboListParam;
import com.ruoyi.homecare.serviceOrder.param.HomeAdminOrderServiceInfoParam;
import com.ruoyi.homecare.serviceOrder.param.HomeProviderOrderServiceInfoParam;
import com.ruoyi.homecare.serviceOrder.service.HomeOrderServiceInfoService;
import com.ruoyi.homecare.serviceOrder.vo.*;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @description home_order_service_info控制器
 * @date 2022-07-14
 */
@Slf4j
@Api(tags = "服务商PC端-服务订单")
@RestController
@RequestMapping("/providerOrderServiceInfo")
public class HomeProviderOrderServiceInfoController extends BaseController {

    @Autowired
    private HomeOrderServiceInfoService homeOrderServiceInfoService;


    @GetMapping("/getProviderOrderServiceInfoAll")
    @ApiOperation(value = "服务商PC分页查询服务订单列表")
    public TableDataInfo<HomeProviderOrderServiceListVo> getOrderServiceInfoAll(@Valid HomeProviderOrderServiceInfoParam homeAdminOrderServiceInfoParam) {
        TableDataInfo tableDataInfo = homeOrderServiceInfoService.getProviderOrderServiceInfoAll(homeAdminOrderServiceInfoParam);
        return tableDataInfo;
    }

    @GetMapping("/getOrderServiceDataInfo")
    @ApiOperation(value = "查询服务订单详情")
    public AjaxResult getOrderServiceDataInfo(String id) {
        HomeAdminOrderServiceInfoVo vo = homeOrderServiceInfoService.getOrderServiceDataInfo(id);
        return AjaxResult.success().put("data", vo);
    }

    @ApiOperation(value = "商家接单")
    @Log(platform = "2", title = "商家接单", businessType = BusinessType.UPDATE)
    @PostMapping("/merchantOrders")
    public AjaxResult merchantOrders(@RequestBody MerchantOrdersRequestVo requestVo) {
        return AjaxResult.success(homeOrderServiceInfoService.merchantOrders(requestVo));
    }

    @ApiOperation(value = "商家-查询已售套餐")
    @Log(platform = "2", title = "查询已售套餐", businessType = BusinessType.INSERT)
    @GetMapping("/getSoldComboList")
    public TableDataInfo<SoldComboListVo> getSoldComboList(EtSoldComboListParam etSoldComboListParam) {

        TableDataInfo<SoldComboListVo> list = homeOrderServiceInfoService.getSoldComboList(etSoldComboListParam);

        return list;
    }

    @GetMapping("/getOrderServiceComboInfoList")
    @ApiOperation(value = "查询套餐订单，详情列表")
    public TAjaxResult<HomeOrderServiceComboDetailsInfo> getOrderServiceComboInfoList(String comboId, String serviceName) {
        List list = homeOrderServiceInfoService.getOrderServiceComboInfoList(comboId, serviceName);
        return new TAjaxResult().success(list);
    }

    @ApiOperation(value = "商家-已售套餐下单")
    @Log(platform = "2", title = "商家接单", businessType = BusinessType.UPDATE)
    @PostMapping("/sellComboToOrder")
    public TAjaxResult sellComboToOrder(@RequestBody @Valid SellComboToOrdertVo sellComboToOrdertVo) {
        return new TAjaxResult<>().success(homeOrderServiceInfoService.sellComboToOrder(sellComboToOrdertVo));
    }

    @GetMapping("/getUserInfo")
    @ApiOperation(value = "商家获取用户信息")
    public TAjaxResult getUserInfo(
            @RequestParam(value = "comboDetailsId", required = true)
            @ApiParam(name = "comboDetailsId", value = "订单id")
            @NotBlank(message = "套餐单项详情id！")
            String comboDetailsId
    ) {

        TAjaxResult tAjaxResult = homeOrderServiceInfoService.getUserInfo(comboDetailsId);

        return tAjaxResult;
    }


}
