package com.ruoyi.homecare.elderlyPeople.mapper;

import com.ruoyi.homecare.elderlyPeople.domain.HomeMedicationRemind;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用药提醒Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-15
 */
@Mapper
public interface HomeMedicationRemindMapper {
    /**
     * 查询用药提醒
     *
     * @param id 用药提醒主键
     * @return 用药提醒
     */
    public HomeMedicationRemind selectHomeMedicationRemindById(Long id);

    /**
     * 查询用药提醒列表
     *
     * @param homeMedicationRemind 用药提醒
     * @return 用药提醒集合
     */
    public List<HomeMedicationRemind> selectHomeMedicationRemindList(HomeMedicationRemind homeMedicationRemind);

    /**
     * 新增用药提醒
     *
     * @param homeMedicationRemind 用药提醒
     * @return 结果
     */
    public int insertHomeMedicationRemind(HomeMedicationRemind homeMedicationRemind);

    /**
     * 修改用药提醒
     *
     * @param homeMedicationRemind 用药提醒
     * @return 结果
     */
    public int updateHomeMedicationRemind(HomeMedicationRemind homeMedicationRemind);

    /**
     * 删除用药提醒
     *
     * @param id 用药提醒主键
     * @return 结果
     */
    public int deleteHomeMedicationRemindById(Long id);

    /**
     * 批量删除用药提醒
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeMedicationRemindByIds(Long[] ids);
}
