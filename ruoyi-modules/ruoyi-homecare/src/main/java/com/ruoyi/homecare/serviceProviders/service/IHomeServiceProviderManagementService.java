package com.ruoyi.homecare.serviceProviders.service;

import cn.hutool.json.JSONObject;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.domain.vo.AppServiceProviderManagementVo;

import java.util.List;

/**
 * 服务商管理Service接口
 *
 * <AUTHOR>
 * @date 2022-06-14
 */
public interface IHomeServiceProviderManagementService {
    /**
     * 查询服务商管理
     *
     * @param id 服务商管理主键
     * @return 服务商管理
     */
    public HomeServiceProviderManagement selectHomeServiceProviderManagementById(Long id);

    /**
     * 根据系统用户id查询已审核成功服务商管理
     *
     * @param userId 系统用户id
     * @return 服务商管理
     */
    public HomeServiceProviderManagement getServiceProviderBySysUserId(Long userId);

    /**
     * 查询服务商管理列表
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 服务商管理集合
     */
    public List<HomeServiceProviderManagement> selectHomeServiceProviderManagementList(HomeServiceProviderManagement homeServiceProviderManagement);

    /**
     * 新增服务商管理
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 结果
     */
    public int insertHomeServiceProviderManagement(HomeServiceProviderManagement homeServiceProviderManagement);

    /**
     * 修改服务商管理
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 结果
     */
    public int updateHomeServiceProviderManagement(HomeServiceProviderManagement homeServiceProviderManagement);

    /**
     * 批量删除服务商管理
     *
     * @param ids 需要删除的服务商管理主键集合
     * @return 结果
     */
    public int deleteHomeServiceProviderManagementByIds(Long[] ids);

    /**
     * 删除服务商管理信息
     *
     * @param id 服务商管理主键
     * @return 结果
     */
    public int deleteHomeServiceProviderManagementById(Long id);

    /**
     * 获取全量服务商管理KeyValue
     *
     * @param name
     * @return
     */
    List<JSONObject> getServiceProviderList(String name);

    /**
     * 管理员审核服务商
     *
     * @param
     * @return
     */
    int audit(JSONObject data);

    /**
     * 开启、关闭状态
     *
     * @param state
     * @param id
     * @return
     */
    int serviceProviderOpenClose(String state, Long id);

    /**
     * app通过类别标签查询服务商列表
     *
     * @param homeServiceProviderManagement
     * @return
     */
    List<AppServiceProviderManagementVo> getServiceProviderListInfo(AppServiceProviderManagementVo homeServiceProviderManagement);


    /**
     * 服务商的总销量增加
     *
     * @param id
     * @param
     * @return
     */
    int salesSave(Long id);

    /**
     * 服务商的好评数增加
     *
     * @param id
     * @param score 评分分数
     * @return
     */
    int praiseSave(Long id, Integer score);

    int updateBanFlag(Long id, Integer banFlag);

    /**
     * 商家评价信息
     *
     * @return
     */
    JSONObject getServiceProviderEvaluateInfo();

    /**
     * 管理端服务商管理-商家评价信息
     *
     * @param id
     * @return
     */
    JSONObject getAdminServiceProviderEvaluateInfo(Long id);
}
