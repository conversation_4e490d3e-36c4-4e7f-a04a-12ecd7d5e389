package com.ruoyi.homecare.serviceOrder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description t_home_order_service_info
 * @date 2022-07-14
 */
@Data
public class HomeOrderServiceComboRequestVo implements Serializable {

    private static final long serialVersionUID = 123123123123123121L;


    @ApiModelProperty("openId,支付方式为微信付款时必填")
    private String openId;

    /**
     * 服务套餐id
     */
    @ApiModelProperty("服务套餐id")
    private Long serviceComboId;


    @ApiModelProperty("服务商id")
    private Long serviceProviderId;


    /**
     * 服务套餐名称
     */
    @ApiModelProperty("服务套餐名称")
    private String serviceComboName;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal price;


    @ApiModelProperty("总价")
    private BigDecimal totalFee;

    @ApiModelProperty("支付方式")
    private Integer payWay;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Integer number;

    private String address;
    private String phone;
    private String name;

    public HomeOrderServiceComboRequestVo() {
    }
}
