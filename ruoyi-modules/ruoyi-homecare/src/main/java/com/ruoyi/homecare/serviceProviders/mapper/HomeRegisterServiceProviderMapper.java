package com.ruoyi.homecare.serviceProviders.mapper;

import com.ruoyi.homecare.serviceProviders.domain.HomeRegisterServiceProvider;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 注册服务商记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@Mapper
public interface HomeRegisterServiceProviderMapper {
    /**
     * 查询注册服务商记录
     *
     * @param id 注册服务商记录主键
     * @return 注册服务商记录
     */
    public HomeRegisterServiceProvider selectHomeRegisterServiceProviderById(Long id);

    /**
     * 查询注册服务商记录列表
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 注册服务商记录集合
     */
    public List<HomeRegisterServiceProvider> selectHomeRegisterServiceProviderList(HomeRegisterServiceProvider homeRegisterServiceProvider);

    /**
     * 新增注册服务商记录
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 结果
     */
    public int insertHomeRegisterServiceProvider(HomeRegisterServiceProvider homeRegisterServiceProvider);

    /**
     * 修改注册服务商记录
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 结果
     */
    public int updateHomeRegisterServiceProvider(HomeRegisterServiceProvider homeRegisterServiceProvider);

    /**
     * 删除注册服务商记录
     *
     * @param id 注册服务商记录主键
     * @return 结果
     */
    public int deleteHomeRegisterServiceProviderById(Long id);

    /**
     * 批量删除注册服务商记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeRegisterServiceProviderByIds(Long[] ids);

    /**
     * 根据系统id修改成不可看
     *
     * @param sysUserId
     */
    void updateBySysUserId(Long sysUserId);

    /**
     * 根据系统id获取商家信息
     *
     * @param userid
     * @return
     */
    HomeRegisterServiceProvider getServiceProvidersInfoBySysUserId(Long userid);
}
