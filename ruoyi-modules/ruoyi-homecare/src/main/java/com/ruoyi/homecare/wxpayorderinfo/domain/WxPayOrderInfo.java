package com.ruoyi.homecare.wxpayorderinfo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName WxPayOrderInfo
 * @Description 微信支付订单表
 * <AUTHOR>
 * @Date 2022/8/11 15:53
 */
@Data
@ApiModel("微信支付订单表")
public class WxPayOrderInfo implements Serializable {

    public static final Integer TYPE_GOODS = 1;// 普通商品
    public static final Integer TYPE_TOPUP = 2;// 充值
    public static final Integer STATUS_WAIT = 1;// 待付款
    public static final Integer STATUS_ALREADY = 2;// 已付款
    public static final Integer STATUS_PAY = 3;// 付款失败
    private static final long serialVersionUID = -4662588385181913167L;
    /**
     * id
     */
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("open_id")
    private String openId;

    @ApiModelProperty("user_id")
    private String userId;

    /**
     * origin_id
     */
    @ApiModelProperty("origin_id")
    private String originId;

    /**
     * 微信支付的单号
     */
    @ApiModelProperty("微信支付的单号")
    private String transactionId;

    /**
     * 1:商品订单 2：充值订单
     */
    @ApiModelProperty("1:商品订单 2：充值订单")
    private int type;

    /**
     * 1:待付款 2：已付款 3：付款失败
     */
    @ApiModelProperty("1:待付款 2：已付款 3：付款失败")
    private int status;

    @ApiModelProperty("付款金额")
    private BigDecimal amount;

    /**
     * create_time
     */
    @ApiModelProperty("create_time")
    private Date createTime;

    /**
     * update_time
     */
    @ApiModelProperty("update_time")
    private Date updateTime;

    @ApiModelProperty("付款银行")
    private String bankType;

    @ApiModelProperty("交易类型")
    private String tradeType;

    @ApiModelProperty("支付完成时间")
    private Date timeEnd;

}
