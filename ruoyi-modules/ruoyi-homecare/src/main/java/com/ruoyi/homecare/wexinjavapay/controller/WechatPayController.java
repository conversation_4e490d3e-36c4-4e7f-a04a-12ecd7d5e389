package com.ruoyi.homecare.wexinjavapay.controller;

import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.homecare.goodsOrder.request.ApplyForRefundRaram;
import com.ruoyi.homecare.goodsOrder.request.UserGoodsOrderTopUpRequest;
import com.ruoyi.homecare.wexinjavapay.service.WechatPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @ClassName WechatPayController
 * @Description
 * <AUTHOR>
 * @Date 2022/8/11 13:32
 */
@Api("订单-微信支付")
@RestController
@RequestMapping("/wechatPay/order")
public class WechatPayController {

    @Autowired
    private WechatPayService wechatPayService;


}
