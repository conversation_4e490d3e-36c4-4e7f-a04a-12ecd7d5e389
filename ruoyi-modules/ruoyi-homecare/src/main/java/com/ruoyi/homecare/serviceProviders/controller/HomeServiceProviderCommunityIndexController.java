package com.ruoyi.homecare.serviceProviders.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderCommunityIndexService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 服务商和社区关联Controller
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@RestController
@RequestMapping("/homeServiceProviderCommunityIndex")
public class HomeServiceProviderCommunityIndexController extends BaseController {
    @Autowired
    private IHomeServiceProviderCommunityIndexService homeServiceProviderCommunityIndexService;

    /**
     * 查询服务商和社区关联列表
     */
    //@RequiresPermissions("serviceProviders:homeServiceProviderCommunityIndex:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex) {
        startPage();
        List<HomeServiceProviderCommunityIndex> list = homeServiceProviderCommunityIndexService.selectHomeServiceProviderCommunityIndexList(homeServiceProviderCommunityIndex);
        return getDataTable(list);
    }

    /**
     * 导出服务商和社区关联列表
     */
    //@RequiresPermissions("serviceProviders:homeServiceProviderCommunityIndex:export")
    @Log(platform = "2", title = "服务商和社区关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex) {
        List<HomeServiceProviderCommunityIndex> list = homeServiceProviderCommunityIndexService.selectHomeServiceProviderCommunityIndexList(homeServiceProviderCommunityIndex);
        ExcelUtil<HomeServiceProviderCommunityIndex> util = new ExcelUtil<HomeServiceProviderCommunityIndex>(HomeServiceProviderCommunityIndex.class);
        util.exportExcel(response, list, "服务商和社区关联数据");
    }

    /**
     * 获取服务商和社区关联详细信息
     */
    //@RequiresPermissions("serviceProviders:homeServiceProviderCommunityIndex:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(homeServiceProviderCommunityIndexService.selectHomeServiceProviderCommunityIndexById(id));
    }

    /**
     * 新增服务商和社区关联
     */
    //@RequiresPermissions("serviceProviders:homeServiceProviderCommunityIndex:add")
    @Log(platform = "2", title = "服务商和社区关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex) {
        return toAjax(homeServiceProviderCommunityIndexService.insertHomeServiceProviderCommunityIndex(homeServiceProviderCommunityIndex));
    }

    /**
     * 修改服务商和社区关联
     */
    //@RequiresPermissions("serviceProviders:homeServiceProviderCommunityIndex:edit")
    @Log(platform = "2", title = "服务商和社区关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex) {
        return toAjax(homeServiceProviderCommunityIndexService.updateHomeServiceProviderCommunityIndex(homeServiceProviderCommunityIndex));
    }

    /**
     * 删除服务商和社区关联
     */
    //@RequiresPermissions("serviceProviders:homeServiceProviderCommunityIndex:remove")
    @Log(platform = "2", title = "服务商和社区关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeServiceProviderCommunityIndexService.deleteHomeServiceProviderCommunityIndexByIds(ids));
    }
}
