package com.ruoyi.homecare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.CapabilityAssessmentInfo;
import com.ruoyi.homecare.elderlyPeople.domain.vo.CapabilityAssessmentInfoVo;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareCapabilityAssessmentInfoService;
import com.ruoyi.homecare.utils.DictUtils;
import com.ruoyi.homecare.utils.ReflectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家能力评估报告Controller
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@RestController
@RequestMapping("/homeCareAssessmentInfo")
@Api(value = "健康管理-居家能力评估报告", tags = "健康管理-居家能力评估报告")
public class HomeCareCapabilityAssessmentInfoController extends BaseController {
    @Autowired
    private IHomeCareCapabilityAssessmentInfoService homeCareCapabilityAssessmentInfoService;

    /**
     * 查询居家能力评估报告列表
     */
    //@RequiresPermissions("elderlyPeople:capabilityAssessmentInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "居家能力评估报告列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人id", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo<CapabilityAssessmentInfo> list(@ApiIgnore CapabilityAssessmentInfo capabilityAssessmentInfo) {
        startPage();
        List<CapabilityAssessmentInfo> list = homeCareCapabilityAssessmentInfoService.selectCapabilityAssessmentInfoList(capabilityAssessmentInfo);
        return getDataTable(list);
    }

    /**
     * 导出居家能力评估报告列表
     */
    //@RequiresPermissions("elderlyPeople:capabilityAssessmentInfo:export")
    @Log(platform = "2", title = "居家能力评估报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, CapabilityAssessmentInfo capabilityAssessmentInfo) {
        List<CapabilityAssessmentInfo> list = homeCareCapabilityAssessmentInfoService.selectCapabilityAssessmentInfoList(capabilityAssessmentInfo);
        ExcelUtil<CapabilityAssessmentInfo> util = new ExcelUtil<CapabilityAssessmentInfo>(CapabilityAssessmentInfo.class);
        util.exportExcel(response, list, "居家能力评估报告数据");
    }

    /**
     * 获取居家能力评估报告详细信息
     */
    //@RequiresPermissions("elderlyPeople:capabilityAssessmentInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "查看评估报告")
    public TAjaxResult<CapabilityAssessmentInfo> getInfo(@PathVariable("id") Long id) {
        CapabilityAssessmentInfo capabilityAssessmentInfo = (CapabilityAssessmentInfo) ReflectionUtils.nullifyStrings(homeCareCapabilityAssessmentInfoService.selectCapabilityAssessmentInfoById(id));
        return new TAjaxResult<CapabilityAssessmentInfo>(capabilityAssessmentInfo);
    }

    /**
     * 保存居家能力评估报告
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存居家能力评估报告")
    public AjaxResult save(@RequestBody CapabilityAssessmentInfoVo vo) {
        return toAjax(homeCareCapabilityAssessmentInfoService.saveCapabilityAssessmentInfo(vo));
    }

    /**
     * 新增居家能力评估报告
     */
    //@RequiresPermissions("elderlyPeople:capabilityAssessmentInfo:add")
    @Log(platform = "2", title = "居家能力评估报告", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody CapabilityAssessmentInfo capabilityAssessmentInfo) {
        return toAjax(homeCareCapabilityAssessmentInfoService.insertCapabilityAssessmentInfo(capabilityAssessmentInfo));
    }

    /**
     * 修改居家能力评估报告
     */
    //@RequiresPermissions("elderlyPeople:capabilityAssessmentInfo:edit")
    @Log(platform = "2", title = "居家能力评估报告", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody CapabilityAssessmentInfo capabilityAssessmentInfo) {
        return toAjax(homeCareCapabilityAssessmentInfoService.updateCapabilityAssessmentInfo(capabilityAssessmentInfo));
    }

    /**
     * 删除居家能力评估报告
     */
    //@RequiresPermissions("elderlyPeople:capabilityAssessmentInfo:remove")
    @Log(platform = "2", title = "居家能力评估报告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除居家能力评估报告")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeCareCapabilityAssessmentInfoService.deleteCapabilityAssessmentInfoByIds(ids));
    }

    /**
     * 老年人评估报告逻辑运算
     *
     * @param selfCareAbilityAssessmentValue            日常生活活动级别值
     * @param athleticAbilityAssessmentValue            精神状态级别值
     * @param mentalStateAssessmentValue                感知觉与沟通级别值
     * @param perceptionSocialEngagementAssessmentValue 社会参与级别值
     * @return
     */
    @GetMapping("/getEvaluateInfo")
    @ApiOperation(value = "重新生成老年人居家能力评估等级")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "selfCareAbilityAssessmentValue", value = "日常生活活动级别值", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "athleticAbilityAssessmentValue", value = "精神状态级别值", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "mentalStateAssessmentValue", value = "感知觉与沟通级别值", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "perceptionSocialEngagementAssessmentValue", value = "社会参与级别值", required = false, dataType = "int"),
    })
    public AjaxResult getEvaluateInfo(@ApiIgnore int selfCareAbilityAssessmentValue, int athleticAbilityAssessmentValue, int mentalStateAssessmentValue, int perceptionSocialEngagementAssessmentValue) {
        int value = homeCareCapabilityAssessmentInfoService.getPreliminaryAssessment(selfCareAbilityAssessmentValue, athleticAbilityAssessmentValue, mentalStateAssessmentValue, perceptionSocialEngagementAssessmentValue);
        String label = DictUtils.selectDictLabel("assessment_type", String.valueOf(value));
        return AjaxResult.success().put("value", value).put("label", label);
    }

    /**
     * 查看调查问卷历史
     *
     * @param id
     * @return
     */
    @GetMapping("/getAssessmentInfoVo")
    @ApiOperation("查看调查问卷历史")
    public TAjaxResult<CapabilityAssessmentInfoVo> getAssessmentInfoVo(Long id) {
        return new TAjaxResult<CapabilityAssessmentInfoVo>(homeCareCapabilityAssessmentInfoService.getAssessmentInfoVo(id));
    }


}
