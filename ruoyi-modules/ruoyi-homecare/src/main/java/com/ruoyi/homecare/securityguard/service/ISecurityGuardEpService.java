package com.ruoyi.homecare.securityguard.service;

import com.ruoyi.homecare.securityguard.domain.SecurityGuardEp;

import java.util.List;

/**
 * ep设备Service接口
 *
 * <AUTHOR>
 * @date 2022-12-01
 */
public interface ISecurityGuardEpService {
    /**
     * 查询ep设备
     *
     * @param id ep设备主键
     * @return ep设备
     */
    public SecurityGuardEp selectSecurityGuardEpById(Long id);

    /**
     * 查询ep设备列表
     *
     * @param securityGuardEp ep设备
     * @return ep设备集合
     */
    public List<SecurityGuardEp> selectSecurityGuardEpList(SecurityGuardEp securityGuardEp);

    /**
     * 新增ep设备
     *
     * @param securityGuardEp ep设备
     * @return 结果
     */
    public int insertSecurityGuardEp(SecurityGuardEp securityGuardEp);

    /**
     * 修改ep设备
     *
     * @param securityGuardEp ep设备
     * @return 结果
     */
    public int updateSecurityGuardEp(SecurityGuardEp securityGuardEp);

    /**
     * 批量删除ep设备
     *
     * @param ids 需要删除的ep设备主键集合
     * @return 结果
     */
    public int deleteSecurityGuardEpByIds(Long[] ids);

    /**
     * 删除ep设备信息
     *
     * @param id ep设备主键
     * @return 结果
     */
    public int deleteSecurityGuardEpById(Long id);

    SecurityGuardEp selectSecurityGuardByEPIEEE(String ep, String ieee);
}
