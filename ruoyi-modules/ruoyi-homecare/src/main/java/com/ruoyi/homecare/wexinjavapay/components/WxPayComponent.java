package com.ruoyi.homecare.wexinjavapay.components;

import cn.hutool.core.util.StrUtil;
import com.github.binarywang.wxpay.bean.request.WxPayRefundQueryRequest;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderCloseResult;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryResult;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.homecare.utils.OrderUtils;
import com.ruoyi.homecare.wexinjavapay.request.RefundQueryRequest;
import com.ruoyi.homecare.wexinjavapay.request.RefundRequeest;
import com.ruoyi.homecare.wexinjavapay.request.UnifiedorderRequest;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;

/**
 * @ClassName WxPayComponent
 * @Description
 * <AUTHOR>
 * @Date 2022/8/11 16:15
 */
@Component
@Slf4j
public class WxPayComponent {

    /**
     * 付款成功通知回调地址
     */
//    private static final String base_notify_url = "https://www.xinjuncheng.cn/yljs/homecare/wechatPay/notify";//线下
    private static final String base_notify_url = "https://www.xinjuncheng.cn/yl/homecare/wechatPay/notify";// 线上
    /**
     * 付款成功通知回调地址
     */
    private static final String pay_notify_url = base_notify_url + "/order";
    /**
     * 退款成功通知回调地址
     */
    private static final String refund_notify_url = base_notify_url + "/refund";
    @Autowired
    private WxPayService wxPayService;

    /**
     * 统一下单
     *
     * @param unifiedorderRequest
     * @return 返回null则代表交易失败
     */
    public Object unifiedorder(UnifiedorderRequest unifiedorderRequest) {

        WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new WxPayUnifiedOrderRequest();
        wxPayUnifiedOrderRequest.setBody(unifiedorderRequest.getDescription());
        wxPayUnifiedOrderRequest.setOutTradeNo(unifiedorderRequest.getOutTradeNo());
        wxPayUnifiedOrderRequest.setTotalFee(unifiedorderRequest.getTotal());
        wxPayUnifiedOrderRequest.setSpbillCreateIp(unifiedorderRequest.getSpbillCreateIp());
        wxPayUnifiedOrderRequest.setNotifyUrl(pay_notify_url);
        wxPayUnifiedOrderRequest.setTradeType("JSAPI");
        wxPayUnifiedOrderRequest.setOpenid(unifiedorderRequest.getOpenid());

        Object order = null;
        try {
            order = wxPayService.createOrder(wxPayUnifiedOrderRequest);
        } catch (WxPayException e) {
            log.debug(e.getMessage());
            throw new ServiceException("微信充值统一下单异常！" + e.getErrCodeDes());
        }

        return order;
    }

    /**
     * <AUTHOR>
     * @Description 退款
     * @Date 2022/7/7
     **/
    public WxPayRefundResult refund(@Valid WxPayRefundRequest refundRequeest) {

        refundRequeest.setNotifyUrl(refund_notify_url);

        WxPayRefundResult refund = null;
        try {
            refund = wxPayService.refund(refundRequeest);
        } catch (WxPayException e) {
            throw new ServiceException(e.getErrCodeDes());
        }

        return refund;
    }

    /**
     * <AUTHOR>
     * @Description 查询订单
     * https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_2
     * @Date 2022/7/8
     **/
    public WxPayOrderQueryResult queryOrder(String transactionId, String outTradeNo) {

        if (StringUtils.isBlank(transactionId) && StringUtils.isBlank(outTradeNo)) {
            throw new ServiceException("参数不能同时为空！");
        }

        WxPayOrderQueryResult wxPayOrderQueryResult = null;

        try {
            wxPayOrderQueryResult = wxPayService.queryOrder(transactionId, outTradeNo);
        } catch (WxPayException e) {
            log.debug("查询微信支付订单异常:" + e.getReturnMsg());
            throw new ServiceException("查询微信支付订单异常");
        }

        return wxPayOrderQueryResult;

    }

    /**
     * <AUTHOR>
     * @Description 关闭订单
     * @Date 2022/7/8
     **/
    public WxPayOrderCloseResult closeOrder(String outTradeNo) {

        WxPayOrderCloseResult wxPayOrderCloseResult = null;
        try {
            wxPayOrderCloseResult = wxPayService.closeOrder(outTradeNo);
        } catch (WxPayException e) {
            throw new ServiceException(e.getErrCodeDes());
        }


        return wxPayOrderCloseResult;
    }

    /**
     * <AUTHOR>
     * @Description 退款查询
     * @Date 2022/7/8
     **/
    public WxPayRefundQueryResult refundQuery(RefundQueryRequest refundQueryRequest) {

        String refundId = refundQueryRequest.getRefundId();
        String outRefundNo = refundQueryRequest.getOutRefundNo();
        String transactionId = refundQueryRequest.getTransactionId();
        String outTradeNo = refundQueryRequest.getOutTradeNo();

        if (
                StrUtil.isBlank(refundId) &&
                        StrUtil.isBlank(outRefundNo) &&
                        StrUtil.isBlank(transactionId) &&
                        StrUtil.isBlank(outTradeNo)

        ) {
            throw new ServiceException("参数必须四选一");
        }

        WxPayRefundQueryRequest wxPayRefundQueryRequest = new WxPayRefundQueryRequest();
        wxPayRefundQueryRequest.setRefundId(refundId);
        wxPayRefundQueryRequest.setOutRefundNo(outRefundNo);
        wxPayRefundQueryRequest.setTransactionId(transactionId);
        wxPayRefundQueryRequest.setOutTradeNo(outTradeNo);

        WxPayRefundQueryResult wxPayRefundQueryResult = null;
        try {
            wxPayRefundQueryResult = wxPayService.refundQuery(wxPayRefundQueryRequest);
        } catch (WxPayException e) {
            log.error(e.getErrCodeDes());
            throw new ServiceException(e.getErrCodeDes());
        }

        return wxPayRefundQueryResult;
    }

}
