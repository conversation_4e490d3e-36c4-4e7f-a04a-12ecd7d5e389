package com.ruoyi.homecare.serviceProviders.service;

import java.util.List;

import com.ruoyi.homecare.serviceProviders.domain.HomeGoodsServiceProviderIndex;

/**
 * 商品和服务商关联Service接口
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
public interface IHomeGoodsServiceProviderIndexService {
    /**
     * 查询商品和服务商关联
     *
     * @param id 商品和服务商关联主键
     * @return 商品和服务商关联
     */
    public HomeGoodsServiceProviderIndex selectHomeGoodsServiceProviderIndexById(Long id);

    /**
     * 查询商品和服务商关联列表
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 商品和服务商关联集合
     */
    public List<HomeGoodsServiceProviderIndex> selectHomeGoodsServiceProviderIndexList(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex);

    /**
     * 新增商品和服务商关联
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 结果
     */
    public int insertHomeGoodsServiceProviderIndex(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex);

    /**
     * 修改商品和服务商关联
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 结果
     */
    public int updateHomeGoodsServiceProviderIndex(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex);

    /**
     * 批量删除商品和服务商关联
     *
     * @param ids 需要删除的商品和服务商关联主键集合
     * @return 结果
     */
    public int deleteHomeGoodsServiceProviderIndexByIds(Long[] ids);

    /**
     * 删除商品和服务商关联信息
     *
     * @param id 商品和服务商关联主键
     * @return 结果
     */
    public int deleteHomeGoodsServiceProviderIndexById(Long id);
}
