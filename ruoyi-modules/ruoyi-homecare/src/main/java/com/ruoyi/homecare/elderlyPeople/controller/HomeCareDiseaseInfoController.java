package com.ruoyi.homecare.elderlyPeople.controller;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.DiseaseInfo;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareDiseaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家疾病类型Controller
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@RestController
@RequestMapping("/homeCareDiseaseInfo")
@Api(value = "健康管理-居家能力评估居家疾病类型和分值", tags = "健康管理-居家能力评估居家疾病类型和分值")
public class HomeCareDiseaseInfoController extends BaseController {
    @Autowired
    private IHomeCareDiseaseInfoService diseaseInfoService;

    /**
     * 查询居家疾病类型列表
     */
    //@RequiresPermissions("elderlyPeople:diseaseInfo:list")
    @GetMapping("/list")
    @ApiIgnore
    public TableDataInfo list(DiseaseInfo diseaseInfo) {
        startPage();
        List<DiseaseInfo> list = diseaseInfoService.selectDiseaseInfoList(diseaseInfo);
        return getDataTable(list);
    }

    /**
     * 导出居家疾病类型列表
     */
    //@RequiresPermissions("elderlyPeople:diseaseInfo:export")
    @Log(platform = "2", title = "居家疾病类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, DiseaseInfo diseaseInfo) {
        List<DiseaseInfo> list = diseaseInfoService.selectDiseaseInfoList(diseaseInfo);
        ExcelUtil<DiseaseInfo> util = new ExcelUtil<DiseaseInfo>(DiseaseInfo.class);
        util.exportExcel(response, list, "居家疾病类型数据");
    }

    /**
     * 获取居家疾病类型详细信息
     */
    //@RequiresPermissions("elderlyPeople:diseaseInfo:query")
    @GetMapping(value = "/{id}")
    @ApiIgnore
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(diseaseInfoService.selectDiseaseInfoById(id));
    }

    /**
     * 新增居家疾病类型
     */
    //@RequiresPermissions("elderlyPeople:diseaseInfo:add")
    @Log(platform = "2", title = "居家疾病类型", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody DiseaseInfo diseaseInfo) {
        return toAjax(diseaseInfoService.insertDiseaseInfo(diseaseInfo));
    }

    /**
     * 修改居家疾病类型
     */
    //@RequiresPermissions("elderlyPeople:diseaseInfo:edit")
    @Log(platform = "2", title = "居家疾病类型", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody DiseaseInfo diseaseInfo) {
        return toAjax(diseaseInfoService.updateDiseaseInfo(diseaseInfo));
    }

    /**
     * 删除居家疾病类型
     */
    //@RequiresPermissions("elderlyPeople:diseaseInfo:remove")
    @Log(platform = "2", title = "居家疾病类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiIgnore
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(diseaseInfoService.deleteDiseaseInfoByIds(ids));
    }


    /**
     * 健康评估全部选项
     *
     * @return
     */
    @GetMapping("/getDiseaseInfoJson")
    @ApiOperation(value = "健康评估全部选项")
    public AjaxResult getDiseaseInfoJson() {
        List<JSONObject> diseaseInfoJson = diseaseInfoService.getDiseaseInfoJson();
        return AjaxResult.success(diseaseInfoJson);
    }

}
