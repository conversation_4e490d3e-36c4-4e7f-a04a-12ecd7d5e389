package com.ruoyi.homecare.securityguard.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardDevciePeopleInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 老人与设备关联信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-06
 */
@Mapper
public interface SecurityGuardDevciePeopleInfoMapper extends MPJBaseMapper<SecurityGuardDevciePeopleInfo> {
    /**
     * 查询老人与设备关联信息
     *
     * @param id 老人与设备关联信息主键
     * @return 老人与设备关联信息
     */
    public SecurityGuardDevciePeopleInfo selectSecurityGuardDevciePeopleInfoById(Long id);

    /**
     * 查询老人与设备关联信息列表
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 老人与设备关联信息集合
     */
    public List<SecurityGuardDevciePeopleInfo> selectSecurityGuardDevciePeopleInfoList(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo);

    /**
     * 新增老人与设备关联信息
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 结果
     */
    public int insertSecurityGuardDevciePeopleInfo(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo);

    /**
     * 修改老人与设备关联信息
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 结果
     */
    public int updateSecurityGuardDevciePeopleInfo(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo);

    /**
     * 删除老人与设备关联信息
     *
     * @param id 老人与设备关联信息主键
     * @return 结果
     */
    public int deleteSecurityGuardDevciePeopleInfoById(Long id);

    /**
     * 批量删除老人与设备关联信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityGuardDevciePeopleInfoByIds(Long[] ids);
}
