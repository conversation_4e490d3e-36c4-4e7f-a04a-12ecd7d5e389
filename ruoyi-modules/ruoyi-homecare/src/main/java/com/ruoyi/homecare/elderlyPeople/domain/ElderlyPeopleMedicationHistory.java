package com.ruoyi.homecare.elderlyPeople.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 老人用药史信息对象 t_elderly_people_medication_history
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@ApiModel(value = "老人用药史信息")
public class ElderlyPeopleMedicationHistory extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 疾病名称
     */
    @ApiModelProperty(value = "疾病名称")
    @Excel(name = "疾病名称")
    private String diseaseName;

    /**
     * 药品名称
     */
    @ApiModelProperty(value = "药品名称")
    @Excel(name = "药品名称")
    private String medicineName;

    /**
     * 计量用法
     */
    @ApiModelProperty(value = "计量用法")
    @Excel(name = "计量用法")
    private String meteringUsage;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String status;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 老人基础信息id
     */
    @ApiModelProperty(value = "老人基础信息id")
    @Excel(name = "老人基础信息id")
    private String userId;
    @ApiModelProperty(value = "用药开始时间")
    private Date beginTime;
    @ApiModelProperty(value = "用药结束时间")
    private Date endTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDiseaseName() {
        return diseaseName;
    }

    public void setDiseaseName(String diseaseName) {
        this.diseaseName = diseaseName;
    }

    public String getMedicineName() {
        return medicineName;
    }

    public void setMedicineName(String medicineName) {
        this.medicineName = medicineName;
    }

    public String getMeteringUsage() {
        return meteringUsage;
    }

    public void setMeteringUsage(String meteringUsage) {
        this.meteringUsage = meteringUsage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("diseaseName", getDiseaseName())
                .append("medicineName", getMedicineName())
                .append("meteringUsage", getMeteringUsage())
                .append("status", getStatus())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .append("userId", getUserId())
                .toString();
    }
}
