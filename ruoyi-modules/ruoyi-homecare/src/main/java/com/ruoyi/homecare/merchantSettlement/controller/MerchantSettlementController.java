package com.ruoyi.homecare.merchantSettlement.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.homecare.merchantSettlement.domain.MerchantSettlement;
import com.ruoyi.homecare.merchantSettlement.service.MerchantSettlementService;
import com.ruoyi.homecare.merchantSettlement.vo.MSOrderInfoResVo;
import com.ruoyi.homecare.merchantSettlement.vo.MerchantSettlementListReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description home_service_provider_worker
 * @date 2022-07-12
 */
@RestController
@Api(tags = "商家结算controller", value = "商家结算controller")
@RequestMapping(value = "/merchantSettlement")
public class MerchantSettlementController extends BaseController {

    @Autowired
    private MerchantSettlementService merchantSettlementService;

    @GetMapping("/list")
    @ApiOperation(value = "查询结算信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "serviceProviderName", value = "商家名称", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "serviceProviderId", value = "商家id", required = false, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "startMonth", value = "开始月份", required = false, dataType = "Date"),
            @ApiImplicitParam(paramType = "query", name = "endMonth", value = "结束月份", required = false, dataType = "Date")
    })
    public TableDataInfo<MerchantSettlement> list(@ApiIgnore MerchantSettlementListReqVo reqVo) {
        startPage();
        List<MerchantSettlement> list = merchantSettlementService.selectList(reqVo);
        return getDataTable(list);
    }


    @GetMapping("/getOrderList")
    @ApiOperation(value = "根据商家id以及月份查询订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "serviceProviderId", value = "商家id", required = false, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "queryMonth", value = "查询月份", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo<MSOrderInfoResVo> getOrderList(@ApiIgnore MerchantSettlementListReqVo reqVo) {
        startPage();
        List<MSOrderInfoResVo> list = merchantSettlementService.getOrderList(reqVo);
        return getDataTable(list);
    }


    @GetMapping("/generateMonthBill")
    @ApiOperation(value = "生成账单")
    public AjaxResult generateMonthBill() {
        merchantSettlementService.generateMonthBill();
        return AjaxResult.success();
    }


    @GetMapping("/settlementBill")
    @ApiOperation(value = "结算账单")
    public AjaxResult settlementBill(String id, BigDecimal settlementAmount) {
        return AjaxResult.success(merchantSettlementService.settlementBill(id, settlementAmount));
    }
}
