package com.ruoyi.homecare.serviceOrder.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.homecare.serviceOrder.param.HomeAdminOrderServiceInfoParam;
import com.ruoyi.homecare.serviceOrder.service.HomeOrderServiceInfoService;
import com.ruoyi.homecare.serviceOrder.vo.HomeAdminOrderServiceInfoVo;
import com.ruoyi.homecare.serviceOrder.vo.HomeAdminOrderServiceListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description home_order_service_info控制器
 * @date 2022-07-14
 */
@Slf4j
@Api(tags = "管理员PC端-服务订单")
@RestController
@RequestMapping("/adminOrderServiceInfo")
public class HomeAdminOrderServiceInfoController extends BaseController {

    @Autowired
    private HomeOrderServiceInfoService homeOrderServiceInfoService;

    @GetMapping("/getOrderServiceInfoAll")
    @ApiOperation(value = "分页查询服务订单列表")
    public TableDataInfo<HomeAdminOrderServiceListVo> getOrderServiceInfoAll(@Valid HomeAdminOrderServiceInfoParam homeAdminOrderServiceInfoParam) {
        TableDataInfo tableDataInfo = homeOrderServiceInfoService.getOrderServiceInfoAll(homeAdminOrderServiceInfoParam);
        return tableDataInfo;
    }

    @GetMapping("/getOrderServiceDataInfo")
    @ApiOperation(value = "查询服务订单详情")
    public AjaxResult getOrderServiceDataInfo(String id) {
        HomeAdminOrderServiceInfoVo vo = homeOrderServiceInfoService.getOrderServiceDataInfo(id);
        return AjaxResult.success().put("data", vo);
    }


}
