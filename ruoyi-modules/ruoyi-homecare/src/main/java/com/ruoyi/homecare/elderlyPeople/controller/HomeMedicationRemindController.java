package com.ruoyi.homecare.elderlyPeople.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.HomeMedicationRemind;
import com.ruoyi.homecare.elderlyPeople.service.IHomeMedicationRemindService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 用药提醒Controller
 *
 * <AUTHOR>
 * @date 2022-06-15
 */
@RestController
@RequestMapping("/homeMedicationRemind")
@Api(tags = "健康管理-用药提醒", value = "健康管理-用药提醒")
public class HomeMedicationRemindController extends BaseController {
    @Autowired
    private IHomeMedicationRemindService homeMedicationRemindService;

    /**
     * 查询用药提醒列表
     */
//    //@RequiresPermissions("elderlyPeople:homeMedicationRemind:list")
    @GetMapping("/list")
    @ApiOperation("查询用药提醒列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "userName", value = "每页条数", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo<HomeMedicationRemind> list(@ApiIgnore HomeMedicationRemind homeMedicationRemind) {
        startPage();
        List<HomeMedicationRemind> list = homeMedicationRemindService.selectHomeMedicationRemindList(homeMedicationRemind);
        return getDataTable(list);
    }

    /**
     * 导出用药提醒列表
     */
//    //@RequiresPermissions("elderlyPeople:homeMedicationRemind:export")
    @Log(platform = "2", title = "用药提醒", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出用药提醒列表")
    public void export(HttpServletResponse response, HomeMedicationRemind homeMedicationRemind) {
        List<HomeMedicationRemind> list = homeMedicationRemindService.selectHomeMedicationRemindList(homeMedicationRemind);
        ExcelUtil<HomeMedicationRemind> util = new ExcelUtil<HomeMedicationRemind>(HomeMedicationRemind.class);
        util.exportExcel(response, list, "用药提醒数据");
    }

    /**
     * 获取用药提醒详细信息
     */
//    //@RequiresPermissions("elderlyPeople:homeMedicationRemind:query")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取用药提醒详细信息")
    public TAjaxResult<HomeMedicationRemind> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult(homeMedicationRemindService.selectHomeMedicationRemindById(id));
    }


    /**
     * 保存用药提醒
     */
    @ApiOperation("保存用药提醒")
    @PostMapping("save")
    public AjaxResult save(@RequestBody HomeMedicationRemind homeMedicationRemind) {
        if (null == homeMedicationRemind.getId() || 0 == homeMedicationRemind.getId()) {
            return add(homeMedicationRemind);
        } else {
            return edit(homeMedicationRemind);
        }
    }


    /**
     * 新增用药提醒
     */
//    //@RequiresPermissions("elderlyPeople:homeMedicationRemind:add")
    @Log(platform = "2", title = "用药提醒", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody HomeMedicationRemind homeMedicationRemind) {
        return toAjax(homeMedicationRemindService.insertHomeMedicationRemind(homeMedicationRemind));
    }

    /**
     * 修改用药提醒
     */
//    //@RequiresPermissions("elderlyPeople:homeMedicationRemind:edit")
    @Log(platform = "2", title = "用药提醒", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody HomeMedicationRemind homeMedicationRemind) {
        return toAjax(homeMedicationRemindService.updateHomeMedicationRemind(homeMedicationRemind));
    }

    /**
     * 删除用药提醒
     */
//    //@RequiresPermissions("elderlyPeople:homeMedicationRemind:remove")
    @Log(platform = "2", title = "用药提醒", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除用药提醒")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeMedicationRemindService.deleteHomeMedicationRemindByIds(ids));
    }
}
