package com.ruoyi.homecare.serviceWorkOrder.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description home_service_work_order
 * @date 2022-07-18
 */
@Data
@ApiModel("home_service_work_order")
public class HomeAppServiceWorkOrderInfoRequestVo extends HomeOrderServiceWork {

    @ApiModelProperty("支付方式")
    private Integer payType;

    @ApiModelProperty("支付方式")
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private String payTypeLabel;

    @ApiModelProperty("支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("服务状态")
    private String serviceStatusLabel;

    @ApiModelProperty("评论内容")
    private String commentContent;

    public String getPayTypeLabel() {
        if (null != this.payType) {
            if (super.getServiceType() == 0) {
                if (this.payType == 1) {
                    return "余额付款";
                } else {
                    return "微信在线支付";
                }
            } else {
                return "套餐结算";
            }

        }
        return "--";
    }

    public void setPayTypeLabel(String payTypeLabel) {
        this.payTypeLabel = payTypeLabel;
    }

    public String getServiceStatusLabel() {
        if (null != super.getStatus()) {
            if (super.getStatus() == 1) {
                return "待指派人员";
            } else if (super.getStatus() == 2) {
                return "未开始";
            } else if (super.getStatus() == 3) {
                return "服务中";
            } else if (super.getStatus() == 3) {
                return "服务中";
            } else if (super.getStatus() == 4) {
                return "已完成";
            }
        }
        return "--";
    }

    public void setServiceStatusLabel(String serviceStatusLabel) {
        this.serviceStatusLabel = serviceStatusLabel;
    }
}
