package com.ruoyi.homecare.elderlyPeople.controller;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.homecare.elderlyPeople.domain.vo.ElderlyPeopleInfoVo;
import com.ruoyi.homecare.elderlyPeople.domain.vo.UserDataInfoResult;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleInfoService;
import com.ruoyi.homecare.utils.SysUserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家老人基础信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@RestController
@RequestMapping("/homeCareElderlyPeopleInfo")
@Api(value = "老人管理-居家老人基础信息Controller", tags = "老人管理-居家老人基础信息")
public class HomeCareElderlyPeopleInfoController extends BaseController {
    @Autowired
    private IHomeCareElderlyPeopleInfoService elderlyPeopleInfoService;

    /**
     * 查询居家老人基础信息列表
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询居家老人基础信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "sex", value = "性别", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "idCardNum", value = "身份证号", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "params[beginAge]", value = "开始年龄", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "params[endAge]", value = "结束年龄", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "phone", value = "手机号", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "volunteerFlag", value = "是否是志愿者 0不是，1是", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore ElderlyPeopleInfo elderlyPeopleInfo) {
        startPage();
        List<ElderlyPeopleInfo> list = elderlyPeopleInfoService.selectElderlyPeopleInfoList(elderlyPeopleInfo);
        return getDataTable(list);
    }


    /**
     * 导出居家老人基础信息列表
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:export")
    @Log(platform = "2", title = "居家老人基础信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出居家老人基础信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeopleInfo elderlyPeopleInfo) {
        List<ElderlyPeopleInfo> list = elderlyPeopleInfoService.selectElderlyPeopleInfoList(elderlyPeopleInfo);
        ExcelUtil<ElderlyPeopleInfo> util = new ExcelUtil<ElderlyPeopleInfo>(ElderlyPeopleInfo.class);
        util.exportExcel(response, list, "居家老人基础信息数据");
    }

    /**
     * 获取居家老人基础信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取居家老人基础信息详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "数据id", required = true, dataTypeClass = String.class)
    })
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(elderlyPeopleInfoService.selectElderlyPeopleInfoById(id));
    }

    /**
     * 保存居家老人基础信息
     */
    @ApiOperation(value = "保存居家老人基础信息")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody ElderlyPeopleInfo elderlyPeopleInfo) {
        if (StringUtils.isEmpty(elderlyPeopleInfo.getId())) {
            return add(elderlyPeopleInfo);
        } else {
            return edit(elderlyPeopleInfo);
        }
    }

    /**
     * 新增居家老人基础信息
     */
    @Log(platform = "2", title = "居家老人基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyPeopleInfo elderlyPeopleInfo) {
        return AjaxResult.success().put("id", elderlyPeopleInfoService.insertElderlyPeopleInfo(elderlyPeopleInfo));
    }

    /**
     * 修改居家老人基础信息
     */
    @Log(platform = "2", title = "居家老人基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyPeopleInfo elderlyPeopleInfo) {
        elderlyPeopleInfoService.updateElderlyPeopleInfo(elderlyPeopleInfo);
        return AjaxResult.success().put("id", elderlyPeopleInfo.getId());
    }

    /**
     * 删除居家老人基础信息
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:remove")
    @Log(platform = "2", title = "居家老人基础信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除居家老人基础信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(elderlyPeopleInfoService.logicalDeleteElderlyPeopleInfoByIds(ids));
    }

    @GetMapping("/getUserList")
    @ApiOperation(value = "获取居家老人全量列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", required = false, dataTypeClass = String.class),
    })
    public AjaxResult getUserList(@ApiIgnore String name, String state) {
        List<JSONObject> userList = elderlyPeopleInfoService.getUserList(name, state);
        return AjaxResult.success().put("data", userList);
    }


}
