package com.ruoyi.homecare.wexinjavapay.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ClassName RefundRequeest
 * @Description 微信支付退款接口参数
 * <AUTHOR>
 * @Date 2022/7/8 10:53
 */
@Data
public class RefundRequeest {

    @NotNull(message = "userId不能为空！")
    private Long userId;

    /**
     * 微信生成的订单号
     */
    @NotBlank(message = "transactionId不能为空！")
    private String transactionId;

    /**
     * 商户系统内部订单号，要求32个字符内（最少6个字符）
     * transaction_id、out_trade_no二选一，如果同时存在优先级：transaction_id> out_trade_no
     */
    private String outTradeNo;

    /**
     * 订单总金额(单位：分)
     */
    @NotNull(message = "totalFee不能为空！")
    private Integer totalFee;

    /**
     * 退款金额(单位：分)
     */
    @NotNull(message = "退款金额不能为空！")
    private int refundFee;

    /**
     * 退款原因
     * 若商户传入，会在下发给用户的退款消息中体现退款原因
     * 注意：若订单退款金额≤1元，且属于部分退款，则不会在退款消息中体现退款原因
     */
    private String refundDesc;

}
