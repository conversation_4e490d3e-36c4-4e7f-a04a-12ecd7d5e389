package com.ruoyi.homecare.elderlyPeople.service;

import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthFileInfo;

import java.util.List;

/**
 * 老人健康档案Service接口
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
public interface IHomeCareElderlyPeopleHealthFileInfoService {
    /**
     * 查询老人健康档案
     *
     * @param id 老人健康档案主键
     * @return 老人健康档案
     */
    public ElderlyPeopleHealthFileInfo selectElderlyPeopleHealthFileInfoById(Long id);


    /**
     * 健康管理模块中的健康信息
     *
     * @param userId
     * @return
     */
    public ElderlyPeopleHealthFileInfo getDataInfoByUserId(String userId);

    /**
     * 查询老人健康档案列表
     *
     * @param elderlyPeopleHealthFileInfo 老人健康档案
     * @return 老人健康档案集合
     */
    public List<ElderlyPeopleHealthFileInfo> selectElderlyPeopleHealthFileInfoList(ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo);

    /**
     * 新增老人健康档案
     *
     * @param elderlyPeopleHealthFileInfo 老人健康档案
     * @return 结果
     */
    public int insertElderlyPeopleHealthFileInfo(ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo);

    /**
     * 修改老人健康档案
     *
     * @param elderlyPeopleHealthFileInfo 老人健康档案
     * @return 结果
     */
    public int updateElderlyPeopleHealthFileInfo(ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo);

    /**
     * 批量删除老人健康档案
     *
     * @param ids 需要删除的老人健康档案主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleHealthFileInfoByIds(Long[] ids);

    /**
     * 删除老人健康档案信息
     *
     * @param id 老人健康档案主键
     * @return 结果
     */
    public int deleteElderlyPeopleHealthFileInfoById(Long id);
}
