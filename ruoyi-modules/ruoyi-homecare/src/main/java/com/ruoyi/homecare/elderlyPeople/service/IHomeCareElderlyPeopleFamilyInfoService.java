package com.ruoyi.homecare.elderlyPeople.service;

import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleFamilyInfo;

import java.util.List;

/**
 * 老人家属信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
public interface IHomeCareElderlyPeopleFamilyInfoService {
    /**
     * 查询老人家属信息
     *
     * @param id 老人家属信息主键
     * @return 老人家属信息
     */
    public ElderlyPeopleFamilyInfo selectElderlyPeopleFamilyInfoById(String id);

    /**
     * 查询老人家属信息列表
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 老人家属信息集合
     */
    public List<ElderlyPeopleFamilyInfo> selectElderlyPeopleFamilyInfoList(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 新增老人家属信息
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 结果
     */
    public int insertElderlyPeopleFamilyInfo(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 修改老人家属信息
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 结果
     */
    public int updateElderlyPeopleFamilyInfo(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 批量删除老人家属信息
     *
     * @param ids 需要删除的老人家属信息主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleFamilyInfoByIds(String[] ids);

    /**
     * 删除老人家属信息信息
     *
     * @param id 老人家属信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleFamilyInfoById(String id);
}
