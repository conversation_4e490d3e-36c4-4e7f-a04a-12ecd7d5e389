package com.ruoyi.homecare.serviceProviders.mapper;

import cn.hutool.json.JSONArray;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProjectProviderIndex;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 服务项目和服务商关联Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
@Mapper
public interface HomeServiceProjectProviderIndexMapper {
    /**
     * 查询服务项目和服务商关联
     *
     * @param id 服务项目和服务商关联主键
     * @return 服务项目和服务商关联
     */
    public HomeServiceProjectProviderIndex selectHomeServiceProjectProviderIndexById(Long id);

    /**
     * 查询服务项目和服务商关联列表
     *
     * @param homeServiceProjectProviderIndex 服务项目和服务商关联
     * @return 服务项目和服务商关联集合
     */
    public List<HomeServiceProjectProviderIndex> selectHomeServiceProjectProviderIndexList(HomeServiceProjectProviderIndex homeServiceProjectProviderIndex);

    /**
     * 新增服务项目和服务商关联
     *
     * @param homeServiceProjectProviderIndex 服务项目和服务商关联
     * @return 结果
     */
    public int insertHomeServiceProjectProviderIndex(HomeServiceProjectProviderIndex homeServiceProjectProviderIndex);

    /**
     * 修改服务项目和服务商关联
     *
     * @param homeServiceProjectProviderIndex 服务项目和服务商关联
     * @return 结果
     */
    public int updateHomeServiceProjectProviderIndex(HomeServiceProjectProviderIndex homeServiceProjectProviderIndex);

    /**
     * 删除服务项目和服务商关联
     *
     * @param id 服务项目和服务商关联主键
     * @return 结果
     */
    public int deleteHomeServiceProjectProviderIndexById(Long id);

    /**
     * 批量删除服务项目和服务商关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeServiceProjectProviderIndexByIds(Long[] ids);

    /**
     * 根据服务项目id获取服务商id
     *
     * @param projectId
     * @return
     */
    JSONArray getByProjectIdList(Long projectId);
}
