package com.ruoyi.homecare.elderlyPeople.service;

import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleMedicationHistory;

import java.util.List;

/**
 * 老人用药史信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
public interface IHomeCareElderlyPeopleMedicationHistoryService {
    /**
     * 查询老人用药史信息
     *
     * @param id 老人用药史信息主键
     * @return 老人用药史信息
     */
    public ElderlyPeopleMedicationHistory selectElderlyPeopleMedicationHistoryById(String id);

    /**
     * 查询老人用药史信息列表
     *
     * @param elderlyPeopleMedicationHistory 老人用药史信息
     * @return 老人用药史信息集合
     */
    public List<ElderlyPeopleMedicationHistory> selectElderlyPeopleMedicationHistoryList(ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory);

    /**
     * 新增老人用药史信息
     *
     * @param elderlyPeopleMedicationHistory 老人用药史信息
     * @return 结果
     */
    public int insertElderlyPeopleMedicationHistory(ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory);

    /**
     * 修改老人用药史信息
     *
     * @param elderlyPeopleMedicationHistory 老人用药史信息
     * @return 结果
     */
    public int updateElderlyPeopleMedicationHistory(ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory);

    /**
     * 批量删除老人用药史信息
     *
     * @param ids 需要删除的老人用药史信息主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleMedicationHistoryByIds(String[] ids);

    /**
     * 删除老人用药史信息信息
     *
     * @param id 老人用药史信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleMedicationHistoryById(String id);
}
