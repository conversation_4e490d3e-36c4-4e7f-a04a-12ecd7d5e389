package com.ruoyi.homecare.wexinjavapay.controller;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.ruoyi.homecare.wexinjavapay.service.WechatPayService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName WechatPayNotify
 * @Description 微信支付中的回调函数处理
 * <AUTHOR>
 * @Date 2022/7/7 16:00
 */
@Api("微信支付")
@RestController
@RequestMapping("/wechatPay/notify")
public class WechatPayNotify {

    @Autowired
    private WechatPayService wechatPayService;


    /**
     * <AUTHOR>
     * @Description 支付回调通知处理
     * 通知频率为15s/15s/30s/3m/10m/20m/30m/30m/30m/60m/3h/3h/3h/6h/6h - 总计 24h4ms
     * @Date 2022/7/7
     **/
    @PostMapping("/order")
    public String parseOrderNotifyResult(@RequestBody String xmlData) {
        return wechatPayService.parseOrderNotifyResult(xmlData);
    }

    /**
     * <AUTHOR>
     * @Description 退款回调通知处理
     * @Date 2022/7/7
     **/
    @PostMapping("/refund")
    public String parseRefundNotifyResult(@RequestBody String xmlData) {
        String a = wechatPayService.parseRefundNotifyResult(xmlData);
        // TODO 根据自己业务场景需要构造返回对象
        return WxPayNotifyResponse.success(a);
    }


}
