package com.ruoyi.homecare.serviceProviders.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.homecare.serviceProviders.domain.HomeRegisterServiceProvider;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.service.IHomeRegisterServiceProviderService;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderManagementService;
import com.ruoyi.homecare.settings.service.IHomeLabelBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 服务商管理Controller
 *
 * <AUTHOR>
 * @date 2022-06-14
 */
@RestController
@RequestMapping("/homeServiceProviderManagement")
@Api(tags = "服务商管理-服务商管理", value = "服务商管理-服务商管理")
public class HomeServiceProviderManagementController extends BaseController {
    @Autowired
    private IHomeServiceProviderManagementService homeServiceProviderManagementService;

    @Autowired
    private IHomeRegisterServiceProviderService homeRegisterServiceProviderService;

    @Autowired
    private IHomeLabelBaseService homeLabelBaseService;

    /**
     * 查询服务商管理列表
     */
    ////@RequiresPermissions("elderlyPeople:homeServiceProviderManagement:list")
    @GetMapping("/list")
    @ApiOperation(value = "服务商管理-服务商管理列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "name", value = "名称", dataTypeClass = String.class)
    })
    public TableDataInfo<HomeServiceProviderManagement> list(@ApiIgnore HomeServiceProviderManagement homeServiceProviderManagement) {
        startPage();
        List<HomeServiceProviderManagement> list = homeServiceProviderManagementService.selectHomeServiceProviderManagementList(homeServiceProviderManagement);
        return getDataTable(list);
    }

    /**
     * 更新商家禁用状态
     */
    @GetMapping("/updateBanFlag")
    @ApiOperation("服务商审核-管理员审核服务商")
    public AjaxResult updateBanFlag(Long id, Integer banFlag) {
        return toAjax(homeServiceProviderManagementService.updateBanFlag(id, banFlag));
    }


    @GetMapping("/auditList")
    @ApiOperation(value = "服务商审核-服务商审核列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "state", value = "状态：0未审核，2拒绝", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "name", value = "名称", dataTypeClass = String.class)
    })
    public TableDataInfo<HomeRegisterServiceProvider> auditList(@ApiIgnore HomeRegisterServiceProvider homeRegisterServiceProvider) {
        startPage();
        homeRegisterServiceProvider.setSelectState("0");
        List<HomeRegisterServiceProvider> list = homeRegisterServiceProviderService.selectHomeRegisterServiceProviderList(homeRegisterServiceProvider);
        return getDataTable(list);
    }

    @GetMapping("/getAuditLogList")
    @ApiOperation(value = "服务商审核-查询服务商审核记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "sysUserId", value = "系统用户id", dataTypeClass = String.class),
    })
    public TableDataInfo<HomeRegisterServiceProvider> getAuditLogList(@ApiIgnore HomeRegisterServiceProvider homeRegisterServiceProvider) {
        homeRegisterServiceProvider.setState("2");
        startPage();
        List<HomeRegisterServiceProvider> list = homeRegisterServiceProviderService.selectHomeRegisterServiceProviderList(homeRegisterServiceProvider);
        return getDataTable(list);
    }


    /**
     * 导出服务商管理列表
     */
    ////@RequiresPermissions("elderlyPeople:homeServiceProviderManagement:export")
    @Log(platform = "2", title = "服务商管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出服务商管理列表")
    @ApiIgnore
    public void export(HttpServletResponse response, HomeServiceProviderManagement homeServiceProviderManagement) {
        List<HomeServiceProviderManagement> list = homeServiceProviderManagementService.selectHomeServiceProviderManagementList(homeServiceProviderManagement);
        ExcelUtil<HomeServiceProviderManagement> util = new ExcelUtil<HomeServiceProviderManagement>(HomeServiceProviderManagement.class);
        util.exportExcel(response, list, "服务商管理数据");
    }

    /**
     * 管理员审核服务商
     */
    @PostMapping("/audit")
    @ApiOperation("服务商审核-管理员审核服务商")
    @Log(platform = "2", title = "服务商审核", businessType = BusinessType.DELETE)
    public AjaxResult audit(@RequestBody JSONObject data) {
        return toAjax(homeServiceProviderManagementService.audit(data));
    }


    /**
     * 获取服务商管理详细信息
     */
    @ApiOperation("服务商管理-服务商管理详细信息")
    ////@RequiresPermissions("elderlyPeople:homeServiceProviderManagement:query")
    @GetMapping(value = "/{id}")
    public TAjaxResult<HomeServiceProviderManagement> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult(homeServiceProviderManagementService.selectHomeServiceProviderManagementById(id));
    }

    /**
     * 获取服务商管理详细信息
     */
    @ApiOperation("服务商审核-服务商审核详细信息")
    ////@RequiresPermissions("elderlyPeople:homeServiceProviderManagement:query")
    @GetMapping("getAuditInfo")
    public TAjaxResult<HomeRegisterServiceProvider> getAuditInfo(Long id) {
        return new TAjaxResult(homeRegisterServiceProviderService.selectHomeRegisterServiceProviderById(id));
    }

    @ApiOperation("根据系统用户id查询已审核成功服务商管理")
    @GetMapping("/getSysUserId")
    @ApiIgnore
    public TAjaxResult<HomeServiceProviderManagement> getSysUserId(Long sysUserId) {
        return new TAjaxResult(homeServiceProviderManagementService.getServiceProviderBySysUserId(sysUserId));
    }

    @ApiOperation("获取全量服务商管理KeyValue")
    @GetMapping("/getServiceProviderList")
    public AjaxResult getServiceProviderList(String name) {
        List<JSONObject> objects = homeServiceProviderManagementService.getServiceProviderList(name);
        return AjaxResult.success().put("data", objects);
    }

    /**
     * 保存服务商管理
     */
    ////@RequiresPermissions("elderlyPeople:homeServiceProviderManagement:add")
    @PostMapping("/save")
    @ApiOperation("服务商审核-保存服务商管理")
    public AjaxResult save(@RequestBody HomeServiceProviderManagement homeServiceProviderManagement) {
        if (null == homeServiceProviderManagement.getId() || 0 == homeServiceProviderManagement.getId()) {
            if ("0".equals(homeServiceProviderManagement.getCreateClient())) {// 是否为pc端来进行新增
                homeServiceProviderManagement.setAuditTime(new Date());
                homeServiceProviderManagement.setState("1");
            }
            return add(homeServiceProviderManagement);
        } else {
            return edit(homeServiceProviderManagement);
        }
    }


    /**
     * 新增服务商管理
     */
    ////@RequiresPermissions("elderlyPeople:homeServiceProviderManagement:add")
    @Log(platform = "2", title = "服务商管理", businessType = BusinessType.INSERT)
    @ApiIgnore
    @PostMapping
    public AjaxResult add(@RequestBody HomeServiceProviderManagement homeServiceProviderManagement) {
        return toAjax(homeServiceProviderManagementService.insertHomeServiceProviderManagement(homeServiceProviderManagement));
    }

    /**
     * 修改服务商管理
     */
    ////@RequiresPermissions("elderlyPeople:homeServiceProviderManagement:edit")
    @Log(platform = "2", title = "服务商管理", businessType = BusinessType.UPDATE)
    @ApiIgnore
    @PutMapping
    public AjaxResult edit(@RequestBody HomeServiceProviderManagement homeServiceProviderManagement) {
        return toAjax(homeServiceProviderManagementService.updateHomeServiceProviderManagement(homeServiceProviderManagement));
    }

    /**
     * 删除服务商管理
     */
    ////@RequiresPermissions("elderlyPeople:homeServiceProviderManagement:remove")
    @Log(platform = "2", title = "服务商管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除服务商管理")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeServiceProviderManagementService.deleteHomeServiceProviderManagementByIds(ids));
    }

    @ApiOperation("开启、关闭状态")
    @GetMapping("/serviceProviderOpenClose")
    @Log(platform = "2", title = "服务商营业状态", businessType = BusinessType.UPDATE)
    public AjaxResult serviceProviderOpenClose(String state, Long id) {
        int i = homeServiceProviderManagementService.serviceProviderOpenClose(state, id);
        return toAjax(i);
    }

    @ApiOperation("服务商-商家评价信息")
    @GetMapping("/getServiceProviderEvaluateInfo")
    public AjaxResult getServiceProviderEvaluateInfo() {
        JSONObject json = homeServiceProviderManagementService.getServiceProviderEvaluateInfo();
        return AjaxResult.success().put("data", json);
    }

    @ApiOperation("管理端服务商管理-商家评价信息")
    @GetMapping("/getAdminServiceProviderEvaluateInfo")
    public AjaxResult getAdminServiceProviderEvaluateInfo(Long id) {
        JSONObject json = homeServiceProviderManagementService.getAdminServiceProviderEvaluateInfo(id);
        return AjaxResult.success().put("data", json);
    }

    @ApiOperation("服务商管理-商家标签列表")
    @GetMapping("/getServiceProviderLabel")
    public AjaxResult getServiceProviderLabel(String types) {
        JSONArray json = homeLabelBaseService.getServiceProviderLabel(types);
        return AjaxResult.success().put("data", json);
    }

    @ApiOperation("服务商管理-白名单不需要登录获取商家标签列表(用户注册时使用)")
    @GetMapping("/getIgnoreServiceProviderLabel")
    public AjaxResult getIgnoreServiceProviderLabel(String types) {
        JSONArray json = homeLabelBaseService.getServiceProviderLabel(types);
        return AjaxResult.success().put("data", json);
    }

}
