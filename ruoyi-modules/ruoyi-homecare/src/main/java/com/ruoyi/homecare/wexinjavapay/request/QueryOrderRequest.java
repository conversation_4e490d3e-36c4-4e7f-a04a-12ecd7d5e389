package com.ruoyi.homecare.wexinjavapay.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * @ClassName QueryOrderRequest
 * @Description
 * <AUTHOR>
 * @Date 2022/7/8 13:49
 */
@Data
public class QueryOrderRequest {

    /**
     * 微信订单号
     * transaction_id
     * 二选一
     * 微信的订单号，优先使用
     */
    private String transactionId;

    /**
     * 商户订单号
     * out_trade_no
     * 二选一
     * 商户系统内部的订单号，当没提供transaction_id时需要传这个。
     */
    @XStreamAlias("out_trade_no")
    private String outTradeNo;

}
