package com.ruoyi.homecare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleMedicationHistory;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleMedicationHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家老人用药史信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@RestController
@RequestMapping("/homeCareHistory")
@Api(value = "老人管理-居家老人用药史信息Controller", tags = "老人管理-居家老人用药史信息")
public class HomeCareElderlyPeopleMedicationHistoryController extends BaseController {
    @Autowired
    private IHomeCareElderlyPeopleMedicationHistoryService elderlyPeopleMedicationHistoryService;

    /**
     * 查询居家老人用药史信息列表
     */
    //@RequiresPermissions("elderlyPeople:history:list")
    @ApiOperation(value = "查询居家老人用药史信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = false, dataTypeClass = String.class),
    })
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory) {
        startPage();
        List<ElderlyPeopleMedicationHistory> list = elderlyPeopleMedicationHistoryService.selectElderlyPeopleMedicationHistoryList(elderlyPeopleMedicationHistory);
        return getDataTable(list);
    }

    /**
     * 导出居家老人用药史信息列表
     */
    //@RequiresPermissions("elderlyPeople:history:export")
    @Log(platform = "2", title = "居家老人用药史信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出居家老人用药史信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory) {
        List<ElderlyPeopleMedicationHistory> list = elderlyPeopleMedicationHistoryService.selectElderlyPeopleMedicationHistoryList(elderlyPeopleMedicationHistory);
        ExcelUtil<ElderlyPeopleMedicationHistory> util = new ExcelUtil<ElderlyPeopleMedicationHistory>(ElderlyPeopleMedicationHistory.class);
        util.exportExcel(response, list, "居家老人用药史信息数据");
    }

    /**
     * 获取居家老人用药史信息详细信息
     */
    //@RequiresPermissions("elderlyPeople:history:query")
    @ApiOperation(value = "获取居家老人用药史信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(elderlyPeopleMedicationHistoryService.selectElderlyPeopleMedicationHistoryById(id));
    }

    /**
     * 新增居家老人用药史信息
     */
    //@RequiresPermissions("elderlyPeople:history:add")
    @Log(platform = "2", title = "居家老人用药史信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增居家老人用药史信息")
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory) {
        return toAjax(elderlyPeopleMedicationHistoryService.insertElderlyPeopleMedicationHistory(elderlyPeopleMedicationHistory));
    }

    /**
     * 修改居家老人用药史信息
     */
    //@RequiresPermissions("elderlyPeople:history:edit")
    @Log(platform = "2", title = "居家老人用药史信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改居家老人用药史信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory) {
        return toAjax(elderlyPeopleMedicationHistoryService.updateElderlyPeopleMedicationHistory(elderlyPeopleMedicationHistory));
    }

    /**
     * 删除居家老人用药史信息
     */
    //@RequiresPermissions("elderlyPeople:history:remove")
    @Log(platform = "2", title = "居家老人用药史信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除居家老人用药史信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(elderlyPeopleMedicationHistoryService.deleteElderlyPeopleMedicationHistoryByIds(ids));
    }
}
