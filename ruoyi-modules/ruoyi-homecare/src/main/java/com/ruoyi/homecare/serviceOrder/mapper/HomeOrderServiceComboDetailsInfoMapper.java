package com.ruoyi.homecare.serviceOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboDetailsInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_order_service_combo_details_infoMapper
 * @date 2022-07-15
 */
@Mapper
public interface HomeOrderServiceComboDetailsInfoMapper extends BaseMapper<HomeOrderServiceComboDetailsInfo> {

    @Select(
            "<script>select t0.* from home_order_service_combo_details_info t0 " +
                    // add here if need left join
                    "where 1=1" +
                    "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
                    "<when test='orderId!=null and orderId!=&apos;&apos; '> and t0.order_id=#{orderId}</when> " +
                    "<when test='orderComboId!=null and orderComboId!=&apos;&apos; '> and t0.order_combo_id=#{orderComboId}</when> " +
                    "<when test='serviceId!=null and serviceId!=&apos;&apos; '> and t0.service_id=#{serviceId}</when> " +
                    "<when test='serviceName!=null and serviceName!=&apos;&apos; '> and t0.service_name=#{serviceName}</when> " +
                    "<when test='userId!=null and userId!=&apos;&apos; '> and t0.user_id=#{userId}</when> " +
                    "<when test='elderlyPeopleId!=null and elderlyPeopleId!=&apos;&apos; '> and t0.elderly_people_id=#{elderlyPeopleId}</when> " +
                    "<when test='price!=null and price!=&apos;&apos; '> and t0.price=#{price}</when> " +
                    "<when test='img!=null and img!=&apos;&apos; '> and t0.img=#{img}</when> " +
                    "<when test='describe!=null and describe!=&apos;&apos; '> and t0.describe=#{describe}</when> " +
                    "<when test='number!=null and number!=&apos;&apos; '> and t0.number=#{number}</when> " +
                    "<when test='completedNumber!=null and completedNumber!=&apos;&apos; '> and t0.completed_number=#{completedNumber}</when> " +
                    "<when test='compltedStauts!=null and compltedStauts!=&apos;&apos; '> and t0.complted_stauts=#{compltedStauts}</when> " +
                    "<when test='workOrderIds!=null and workOrderIds!=&apos;&apos; '> and t0.work_order_ids=#{workOrderIds}</when> " +
                    "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
                    "<when test='updateTime!=null and updateTime!=&apos;&apos; '> and t0.update_time=#{updateTime}</when> " +
                    "<when test='status!=null and status!=&apos;&apos; '> and t0.status=#{status}</when> " +
                    // add here if need page limit
                    //" limit ${page},${limit} " +
                    " </script>")
    List<HomeOrderServiceComboDetailsInfo> pageAll(HomeOrderServiceComboDetailsInfo queryParamDTO, int page, int limit);

    @Select("<script>select count(1) from home_order_service_combo_details_info t0 " +
            // add here if need left join
            "where 1=1" +
            "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
            "<when test='orderId!=null and orderId!=&apos;&apos; '> and t0.order_id=#{orderId}</when> " +
            "<when test='orderComboId!=null and orderComboId!=&apos;&apos; '> and t0.order_combo_id=#{orderComboId}</when> " +
            "<when test='serviceId!=null and serviceId!=&apos;&apos; '> and t0.service_id=#{serviceId}</when> " +
            "<when test='serviceName!=null and serviceName!=&apos;&apos; '> and t0.service_name=#{serviceName}</when> " +
            "<when test='userId!=null and userId!=&apos;&apos; '> and t0.user_id=#{userId}</when> " +
            "<when test='elderlyPeopleId!=null and elderlyPeopleId!=&apos;&apos; '> and t0.elderly_people_id=#{elderlyPeopleId}</when> " +
            "<when test='price!=null and price!=&apos;&apos; '> and t0.price=#{price}</when> " +
            "<when test='img!=null and img!=&apos;&apos; '> and t0.img=#{img}</when> " +
            "<when test='describe!=null and describe!=&apos;&apos; '> and t0.describe=#{describe}</when> " +
            "<when test='number!=null and number!=&apos;&apos; '> and t0.number=#{number}</when> " +
            "<when test='completedNumber!=null and completedNumber!=&apos;&apos; '> and t0.completed_number=#{completedNumber}</when> " +
            "<when test='compltedStauts!=null and compltedStauts!=&apos;&apos; '> and t0.complted_stauts=#{compltedStauts}</when> " +
            "<when test='workOrderIds!=null and workOrderIds!=&apos;&apos; '> and t0.work_order_ids=#{workOrderIds}</when> " +
            "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
            "<when test='updateTime!=null and updateTime!=&apos;&apos; '> and t0.update_time=#{updateTime}</when> " +
            "<when test='status!=null and status!=&apos;&apos; '> and t0.status=#{status}</when> " +
            " </script>")
    int countAll(HomeOrderServiceComboDetailsInfo queryParamDTO);

}
