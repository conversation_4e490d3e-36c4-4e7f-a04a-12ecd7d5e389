package com.ruoyi.homecare.elderlyPeople.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeMedicationRemindMapper;
import com.ruoyi.homecare.elderlyPeople.domain.HomeMedicationRemind;
import com.ruoyi.homecare.elderlyPeople.service.IHomeMedicationRemindService;

/**
 * 用药提醒Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-15
 */
@Service
public class HomeMedicationRemindServiceImpl implements IHomeMedicationRemindService {
    @Autowired
    private HomeMedicationRemindMapper homeMedicationRemindMapper;

    /**
     * 查询用药提醒
     *
     * @param id 用药提醒主键
     * @return 用药提醒
     */
    @Override
    public HomeMedicationRemind selectHomeMedicationRemindById(Long id) {
        return homeMedicationRemindMapper.selectHomeMedicationRemindById(id);
    }

    /**
     * 查询用药提醒列表
     *
     * @param homeMedicationRemind 用药提醒
     * @return 用药提醒
     */
    @Override
    public List<HomeMedicationRemind> selectHomeMedicationRemindList(HomeMedicationRemind homeMedicationRemind) {
        return homeMedicationRemindMapper.selectHomeMedicationRemindList(homeMedicationRemind);
    }

    /**
     * 新增用药提醒
     *
     * @param homeMedicationRemind 用药提醒
     * @return 结果
     */
    @Override
    public int insertHomeMedicationRemind(HomeMedicationRemind homeMedicationRemind) {
        homeMedicationRemind.setCreateTime(DateUtils.getNowDate());
        return homeMedicationRemindMapper.insertHomeMedicationRemind(homeMedicationRemind);
    }

    /**
     * 修改用药提醒
     *
     * @param homeMedicationRemind 用药提醒
     * @return 结果
     */
    @Override
    public int updateHomeMedicationRemind(HomeMedicationRemind homeMedicationRemind) {
        homeMedicationRemind.setUpdateTime(DateUtils.getNowDate());
        return homeMedicationRemindMapper.updateHomeMedicationRemind(homeMedicationRemind);
    }

    /**
     * 批量删除用药提醒
     *
     * @param ids 需要删除的用药提醒主键
     * @return 结果
     */
    @Override
    public int deleteHomeMedicationRemindByIds(Long[] ids) {
        return homeMedicationRemindMapper.deleteHomeMedicationRemindByIds(ids);
    }

    /**
     * 删除用药提醒信息
     *
     * @param id 用药提醒主键
     * @return 结果
     */
    @Override
    public int deleteHomeMedicationRemindById(Long id) {
        return homeMedicationRemindMapper.deleteHomeMedicationRemindById(id);
    }
}
