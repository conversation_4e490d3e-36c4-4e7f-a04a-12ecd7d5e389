package com.ruoyi.homecare.elderlyPeople.service;

import com.ruoyi.homecare.elderlyPeople.domain.CapabilityAssessmentInfo;
import com.ruoyi.homecare.elderlyPeople.domain.vo.CapabilityAssessmentInfoVo;

import java.util.List;

/**
 * 能力评估报告Service接口
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
public interface IHomeCareCapabilityAssessmentInfoService {
    /**
     * 查询能力评估报告
     *
     * @param id 能力评估报告主键
     * @return 能力评估报告
     */
    public CapabilityAssessmentInfo selectCapabilityAssessmentInfoById(Long id);

    /**
     * 查询能力评估报告列表
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 能力评估报告集合
     */
    public List<CapabilityAssessmentInfo> selectCapabilityAssessmentInfoList(CapabilityAssessmentInfo capabilityAssessmentInfo);

    /**
     * 新增能力评估报告
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 结果
     */
    public int insertCapabilityAssessmentInfo(CapabilityAssessmentInfo capabilityAssessmentInfo);

    /**
     * 修改能力评估报告
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 结果
     */
    public int updateCapabilityAssessmentInfo(CapabilityAssessmentInfo capabilityAssessmentInfo);

    /**
     * 批量删除能力评估报告
     *
     * @param ids 需要删除的能力评估报告主键集合
     * @return 结果
     */
    public int deleteCapabilityAssessmentInfoByIds(Long[] ids);

    /**
     * 删除能力评估报告信息
     *
     * @param id 能力评估报告主键
     * @return 结果
     */
    public int deleteCapabilityAssessmentInfoById(Long id);

    /**
     * 保存能力评估报告信息
     *
     * @param
     * @return
     */
    int saveCapabilityAssessmentInfo(CapabilityAssessmentInfoVo vo);

    /**
     * 老年人评估报告逻辑运算
     *
     * @param rchdValue 日常生活活动级别值
     * @param jsztValue 精神状态级别值
     * @param gzValue   感知觉与沟通级别值
     * @param shcyValue 社会参与级别值
     * @return
     */
    int getPreliminaryAssessment(int rchdValue, int jsztValue, int gzValue, int shcyValue);

    /**
     * 查看调查问卷历史
     *
     * @param id
     * @return
     */
    CapabilityAssessmentInfoVo getAssessmentInfoVo(Long id);
}
