package com.ruoyi.homecare.securityguard.service.impl;

import com.ruoyi.homecare.securityguard.domain.SecurityGuardDeviceInfo;
import com.ruoyi.homecare.securityguard.mapper.SecurityGuardDeviceInfoMapper;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardDeviceInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
@Service
public class SecurityGuardDeviceInfoServiceImpl implements ISecurityGuardDeviceInfoService {
    @Autowired
    private SecurityGuardDeviceInfoMapper securityGuardDeviceInfoMapper;

    /**
     * 查询设备信息
     *
     * @param id 设备信息主键
     * @return 设备信息
     */
    @Override
    public SecurityGuardDeviceInfo selectSecurityGuardDeviceInfoById(Long id) {
        return securityGuardDeviceInfoMapper.selectSecurityGuardDeviceInfoById(id);
    }

    /**
     * 查询设备信息列表
     *
     * @param securityGuardDeviceInfo 设备信息
     * @return 设备信息
     */
    @Override
    public List<SecurityGuardDeviceInfo> selectSecurityGuardDeviceInfoList(SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        return securityGuardDeviceInfoMapper.selectSecurityGuardDeviceInfoList(securityGuardDeviceInfo);
    }

    /**
     * 新增设备信息
     *
     * @param securityGuardDeviceInfo 设备信息
     * @return 结果
     */
    @Override
    public int insertSecurityGuardDeviceInfo(SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        return securityGuardDeviceInfoMapper.insertSecurityGuardDeviceInfo(securityGuardDeviceInfo);
    }

    /**
     * 修改设备信息
     *
     * @param securityGuardDeviceInfo 设备信息
     * @return 结果
     */
    @Override
    public int updateSecurityGuardDeviceInfo(SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        return securityGuardDeviceInfoMapper.updateSecurityGuardDeviceInfo(securityGuardDeviceInfo);
    }

    /**
     * 批量删除设备信息
     *
     * @param ids 需要删除的设备信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardDeviceInfoByIds(Long[] ids) {
        return securityGuardDeviceInfoMapper.deleteSecurityGuardDeviceInfoByIds(ids);
    }

    /**
     * 删除设备信息信息
     *
     * @param id 设备信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardDeviceInfoById(Long id) {
        return securityGuardDeviceInfoMapper.deleteSecurityGuardDeviceInfoById(id);
    }
}
