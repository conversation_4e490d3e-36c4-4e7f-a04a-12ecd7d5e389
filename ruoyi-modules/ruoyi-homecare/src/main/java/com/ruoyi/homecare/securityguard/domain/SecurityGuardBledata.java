package com.ruoyi.homecare.securityguard.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 血压信息对象 t_security_guard_bledata
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Data
@ApiModel("血压信息对象")
@TableName("t_security_guard_bledata")
public class SecurityGuardBledata implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备名称
     */
//    @Excel(name = "设备名称")
    @ApiModelProperty("id")
    private String name;

    /**
     * 设备分区类型
     */
//    @Excel(name = "设备分区类型")
    private String zoneType;

    /**
     * 设备分区号
     */
//    @Excel(name = "设备分区号")
    private Long zoneId;

    /**
     * ep设备的 ep 值
     */
//    @Excel(name = "ep设备的 ep 值")
    private String ep;

    /**
     * ep设备的 MAC 值
     */
//    @Excel(name = "ep设备的 MAC 值")
    private String ieee;

    /**
     * mac
     */
//    @Excel(name = "mac")
    private String mac;

    @Excel(name = "老人姓名")
    @TableField(exist = false)
    private String peopleName;

    @Excel(name = "身份证号")
    @TableField(exist = false)
    private String idCardNum;

    @Excel(name = "手机号")
    @TableField(exist = false)
    private String phone;

    @Excel(name = "设备名称")
    @TableField(exist = false)
    private String deviceName;

    @Excel(name = "设备型号")
    @TableField(exist = false)
    private String modelNumber;

    /**
     * 高压
     */
    @Excel(name = "高压")
    private Long sbp;

    /**
     * 低压
     */
    @Excel(name = "低压")
    private Long dbp;

    /**
     * 脉搏
     */
    @Excel(name = "脉搏")
    private Long sphygmus;

    @Excel(name = "血糖")
    private Integer glucose;

    @Excel(name = "血糖")
    private Double glucoseParse;

    private Integer devType;

    @Excel(name = "关联标识")
    private String contextid;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
