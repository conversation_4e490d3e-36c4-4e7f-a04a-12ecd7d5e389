package com.ruoyi.homecare.elderlyPeople.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.homecare.utils.DictUtils;
import io.seata.common.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 老人健康档案对象 t_elderly_people_health_file_info
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@ApiModel(value = "老人健康档案")
public class ElderlyPeopleHealthFileInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 老人id
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id")
    private String userId;

    /**
     * 血压
     */
    @Excel(name = "血压")
    @ApiModelProperty(value = "血压")
    private String bloodPressure;

    /**
     * 测量血压时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "测量血压时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "测量血压时间")
    private Date bloodPressureMeasurementTime;

    /**
     * 血糖
     */
    @Excel(name = "血糖")
    @ApiModelProperty(value = "血糖")
    private String bloodSugar;

    /**
     * 测量血糖时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "测量血糖时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "测量血糖时间")
    private Date bloodSugarMeasurementTime;

    /**
     * 裸眼左视力
     */
    @Excel(name = "裸眼左视力")
    @ApiModelProperty(value = "裸眼左视力")
    private String leftVision;

    /**
     * 裸眼右视力
     */
    @Excel(name = "裸眼右视力")
    @ApiModelProperty(value = "裸眼右视力")
    private String rightVision;

    /**
     * 矫正左视力
     */
    @Excel(name = "矫正左视力")
    @ApiModelProperty(value = "矫正左视力")
    private String correctedLeftVision;

    /**
     * 矫正右视力
     */
    @Excel(name = "矫正右视力")
    @ApiModelProperty(value = "矫正右视力")
    private String correctedRightVision;

    /**
     * 色觉
     */
    @Excel(name = "色觉")
    @ApiModelProperty(value = "色觉")
    private String colorVision;
    @Excel(name = "色觉文字")
    @ApiModelProperty(value = "色觉文字")
    private String colorVisionStr;

    /**
     * 眼部多选项
     */
    @Excel(name = "眼部多选项")
    @ApiModelProperty(value = "眼部多选项")
    private String eyeComplication;
    @Excel(name = "眼部多选项文字")
    @ApiModelProperty(value = "眼部多选项文字")
    private String eyeComplicationStr;

    /**
     * 眼部其他症状
     */
    @Excel(name = "眼部其他症状")
    @ApiModelProperty(value = "眼部其他症状")
    private String eyeOther;

    /**
     * 听力
     */
    @Excel(name = "听力")
    @ApiModelProperty(value = "听力")
    private String hearingSituation;

    /**
     * 耳部多选项
     */
    @Excel(name = "耳部多选项")
    @ApiModelProperty(value = "耳部多选项")
    private String earComplication;
    @Excel(name = "耳部多选项文字")
    @ApiModelProperty(value = "耳部多选项文字")
    private String earComplicationStr;

    /**
     * 耳部其他症状
     */
    @Excel(name = "耳部其他症状")
    @ApiModelProperty(value = "耳部其他症状")
    private String earOther;

    /**
     * 发声
     */
    @Excel(name = "嗅觉")
    @ApiModelProperty(value = "嗅觉")
    private String olfactorySituation;

    /**
     * 鼻部多选项
     */
    @Excel(name = "鼻部多选项")
    @ApiModelProperty(value = "鼻部多选项")
    private String noseComplication;
    @Excel(name = "鼻部多选项文字")
    @ApiModelProperty(value = "鼻部多选项文字")
    private String noseComplicationStr;

    /**
     * 发声
     */
    @Excel(name = "发声")
    @ApiModelProperty(value = "发声")
    private String voiceSituation;

    /**
     * 咽喉多选项
     */
    @Excel(name = "咽喉多选项")
    @ApiModelProperty(value = "咽喉多选项")
    private String throatComplication;
    @Excel(name = "咽喉多选项文字")
    @ApiModelProperty(value = "咽喉多选项文字")
    private String throatComplicationStr;

    /**
     * 咽喉其他症状
     */
    @Excel(name = "咽喉其他症状")
    @ApiModelProperty(value = "咽喉其他症状")
    private String throatOther;

    /**
     * 内科多选项
     */
    @Excel(name = "内科多选项")
    @ApiModelProperty(value = "内科多选项")
    private String internalMedicineComplication;
    @Excel(name = "内科多选项文字")
    @ApiModelProperty(value = "内科多选项文字")
    private String internalMedicineComplicationStr;

    /**
     * 内科其他症状
     */
    @Excel(name = "内科其他症状")
    @ApiModelProperty(value = "内科其他症状")
    private String internalMedicineOther;

    /**
     * 外科多选项
     */
    @Excel(name = "外科多选项")
    @ApiModelProperty(value = "外科多选项")
    private String surgicalComplication;

    @Excel(name = "外科多选项文字")
    @ApiModelProperty(value = "外科多选项文字")
    private String surgicalComplicationStr;

    /**
     * 外科其他症状
     */
    @Excel(name = "外科其他症状")
    @ApiModelProperty(value = "外科其他症状")
    private String surgicalOther;

    //    @ApiModelProperty(value = "userId")
//    private String uId;
    @ApiModelProperty(value = "老人名称")
    private String username;
    @ApiModelProperty(value = "老人性别")
    private String sex;
    @ApiModelProperty(value = "老人身份证号")
    private String idCardNum;
    @ApiModelProperty(value = "老人出生年月日")
    private String dateBirth;
    @ApiModelProperty(value = "老人性别")
    private String sexStr;
    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public String getSexStr() {
        if (StringUtils.isBlank(this.sex)) {
            return this.sex;
        }
        return DictUtils.selectDictLabel("sys_user_sex", this.sex);
    }

    public void setSexStr(String sexStr) {
        this.sexStr = sexStr;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBloodPressure() {
        return bloodPressure;
    }

    public void setBloodPressure(String bloodPressure) {
        this.bloodPressure = bloodPressure;
    }

    public String getColorVisionStr() {
        if (this.colorVision.isEmpty()) {
            return "";
        }
        return getLabel(this.colorVision, "color_vision");
    }

    public void setColorVisionStr(String colorVisionStr) {
        this.colorVisionStr = colorVisionStr;
    }

    public Date getBloodPressureMeasurementTime() {
        return bloodPressureMeasurementTime;
    }

    public void setBloodPressureMeasurementTime(Date bloodPressureMeasurementTime) {
        this.bloodPressureMeasurementTime = bloodPressureMeasurementTime;
    }

    public String getBloodSugar() {
        return bloodSugar;
    }

    public void setBloodSugar(String bloodSugar) {
        this.bloodSugar = bloodSugar;
    }

    public Date getBloodSugarMeasurementTime() {
        return bloodSugarMeasurementTime;
    }

    public void setBloodSugarMeasurementTime(Date bloodSugarMeasurementTime) {
        this.bloodSugarMeasurementTime = bloodSugarMeasurementTime;
    }

    public String getLeftVision() {
        return leftVision;
    }

    public void setLeftVision(String leftVision) {
        this.leftVision = leftVision;
    }

    public String getRightVision() {
        return rightVision;
    }

    public void setRightVision(String rightVision) {
        this.rightVision = rightVision;
    }

    public String getCorrectedLeftVision() {
        return correctedLeftVision;
    }

    public void setCorrectedLeftVision(String correctedLeftVision) {
        this.correctedLeftVision = correctedLeftVision;
    }

    public String getCorrectedRightVision() {
        return correctedRightVision;
    }

    public void setCorrectedRightVision(String correctedRightVision) {
        this.correctedRightVision = correctedRightVision;
    }

    public String getColorVision() {
        return colorVision;
    }

    public void setColorVision(String colorVision) {
        this.colorVision = colorVision;
    }

    public String getEyeComplication() {
        return eyeComplication;
    }

    public void setEyeComplication(String eyeComplication) {
        this.eyeComplication = eyeComplication;
    }

    public String getEyeOther() {
        return eyeOther;
    }

    public void setEyeOther(String eyeOther) {
        this.eyeOther = eyeOther;
    }

    public String getHearingSituation() {
        return hearingSituation;
    }

    public void setHearingSituation(String hearingSituation) {
        this.hearingSituation = hearingSituation;
    }

    public String getEarComplication() {
        return earComplication;
    }

    public void setEarComplication(String earComplication) {
        this.earComplication = earComplication;
    }

    public String getEarOther() {
        return earOther;
    }

    public void setEarOther(String earOther) {
        this.earOther = earOther;
    }

    public String getOlfactorySituation() {
        return olfactorySituation;
    }

    public void setOlfactorySituation(String olfactorySituation) {
        this.olfactorySituation = olfactorySituation;
    }

    public String getNoseComplication() {
        return noseComplication;
    }

    public void setNoseComplication(String noseComplication) {
        this.noseComplication = noseComplication;
    }

    public String getVoiceSituation() {
        return voiceSituation;
    }

    public void setVoiceSituation(String voiceSituation) {
        this.voiceSituation = voiceSituation;
    }

    public String getThroatComplication() {
        return throatComplication;
    }

    public void setThroatComplication(String throatComplication) {
        this.throatComplication = throatComplication;
    }

    public String getThroatOther() {
        return throatOther;
    }

    public void setThroatOther(String throatOther) {
        this.throatOther = throatOther;
    }

    public String getInternalMedicineComplication() {
        return internalMedicineComplication;
    }

    public void setInternalMedicineComplication(String internalMedicineComplication) {
        this.internalMedicineComplication = internalMedicineComplication;
    }

    public String getInternalMedicineOther() {
        return internalMedicineOther;
    }

    public void setInternalMedicineOther(String internalMedicineOther) {
        this.internalMedicineOther = internalMedicineOther;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getIdCardNum() {
        return idCardNum;
    }

    public void setIdCardNum(String idCardNum) {
        this.idCardNum = idCardNum;
    }

    public String getDateBirth() {
        return dateBirth;
    }

    public void setDateBirth(String dateBirth) {
        this.dateBirth = dateBirth;
    }

    public String getSurgicalComplication() {
        return surgicalComplication;
    }

    public void setSurgicalComplication(String surgicalComplication) {
        this.surgicalComplication = surgicalComplication;
    }

    public String getEarComplicationStr() {
        if (null == this.earComplication || this.earComplication.isEmpty()) {
            return "";
        }
        return getLabel(this.earComplication, "ear_complication");
    }

    public void setEarComplicationStr(String earComplicationStr) {
        this.earComplicationStr = earComplicationStr;
    }

    public String getNoseComplicationStr() {
        if (null == this.noseComplication || this.noseComplication.isEmpty()) {
            return "";
        }
        return getLabel(this.noseComplication, "nose_complication");
    }

    public void setNoseComplicationStr(String noseComplicationStr) {
        this.noseComplicationStr = noseComplicationStr;
    }

    public String getThroatComplicationStr() {
        if (null == this.throatComplication || this.throatComplication.isEmpty()) {
            return "";
        }
        return getLabel(this.throatComplication, "throat_complication");
    }

    public void setThroatComplicationStr(String throatComplicationStr) {
        this.throatComplicationStr = throatComplicationStr;
    }

    public String getInternalMedicineComplicationStr() {
        if (null == this.internalMedicineComplication || this.internalMedicineComplication.isEmpty()) {
            return "";
        }
        return getLabel(this.internalMedicineComplication, "internal_medicine_complication");
    }

    public void setInternalMedicineComplicationStr(String internalMedicineComplicationStr) {
        this.internalMedicineComplicationStr = internalMedicineComplicationStr;
    }

    public String getSurgicalComplicationStr() {
        if (null == this.surgicalComplication || this.surgicalComplication.isEmpty()) {
            return "";
        }
        return getLabel(this.surgicalComplication, "surgical_complication");
    }

    public void setSurgicalComplicationStr(String surgicalComplicationStr) {
        this.surgicalComplicationStr = surgicalComplicationStr;
    }

    public String getEyeComplicationStr() {
        if (null == this.eyeComplication || this.eyeComplication.isEmpty()) {
            return "";
        }
        return getLabel(this.eyeComplication, "eye_complication");
    }

    public void setEyeComplicationStr(String eyeComplicationStr) {
        this.eyeComplicationStr = eyeComplicationStr;
    }

    public String getSurgicalOther() {
        return surgicalOther;
    }

    public void setSurgicalOther(String surgicalOther) {
        this.surgicalOther = surgicalOther;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("bloodPressure", getBloodPressure())
                .append("bloodPressureMeasurementTime", getBloodPressureMeasurementTime())
                .append("bloodSugar", getBloodSugar())
                .append("bloodSugarMeasurementTime", getBloodSugarMeasurementTime())
                .append("leftVision", getLeftVision())
                .append("rightVision", getRightVision())
                .append("correctedLeftVision", getCorrectedLeftVision())
                .append("correctedRightVision", getCorrectedRightVision())
                .append("colorVision", getColorVision())
                .append("eyeComplication", getEyeComplication())
                .append("eyeOther", getEyeOther())
                .append("hearingSituation", getHearingSituation())
                .append("earComplication", getEarComplication())
                .append("earOther", getEarOther())
                .append("olfactorySituation", getOlfactorySituation())
                .append("noseComplication", getNoseComplication())
                .append("voiceSituation", getVoiceSituation())
                .append("throatComplication", getThroatComplication())
                .append("throatOther", getThroatOther())
                .append("internalMedicineComplication", getInternalMedicineComplication())
                .append("internalMedicineOther", getInternalMedicineOther())
                .append("surgicalComplication", getSurgicalComplication())
                .append("surgicalOther", getSurgicalOther())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }


    private String getLabel(String value, String type) {
        String[] split = value.split(",");
        String data = "";
        for (int i = 0; i < split.length; i++) {
            if (i == 0) {
                data += DictUtils.selectDictLabel(type, split[i]);
            } else {
                data += "、" + DictUtils.selectDictLabel(type, split[i]);
            }
        }
        return data;
    }


}
