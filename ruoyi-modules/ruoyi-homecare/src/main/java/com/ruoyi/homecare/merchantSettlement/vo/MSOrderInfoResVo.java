package com.ruoyi.homecare.merchantSettlement.vo;

import com.ruoyi.homecare.goodsOrder.domain.HomeOrderBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022-07-13
 */
@Data
@ApiModel("商家结算帐单详情列表")
public class MSOrderInfoResVo extends HomeOrderBaseInfo {

    @ApiModelProperty("退款状态")
    private Integer refundStatus;
    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

}
