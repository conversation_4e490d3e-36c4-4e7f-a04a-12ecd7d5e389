package com.ruoyi.homecare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.ReviewRecordsInfo;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareReviewRecordsInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家能力评估复核Controller
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@RestController
@RequestMapping("/homeCareReviewRecordsInfo")
@Api(value = "健康管理-居家能力评估复核", tags = "健康管理-居家能力评估复核")
public class HomeCareReviewRecordsInfoController extends BaseController {
    @Autowired
    private IHomeCareReviewRecordsInfoService reviewRecordsInfoService;

    /**
     * 查询居家能力评估复核列表
     */
    //@RequiresPermissions("elderlyPeople:reviewRecordsInfo:list")
    @GetMapping("/list")
    @ApiIgnore
    public TableDataInfo list(ReviewRecordsInfo reviewRecordsInfo) {
        startPage();
        List<ReviewRecordsInfo> list = reviewRecordsInfoService.selectReviewRecordsInfoList(reviewRecordsInfo);
        return getDataTable(list);
    }

    /**
     * 导出居家能力评估复核列表
     */
    //@RequiresPermissions("elderlyPeople:reviewRecordsInfo:export")
    @Log(platform = "2", title = "居家能力评估复核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, ReviewRecordsInfo reviewRecordsInfo) {
        List<ReviewRecordsInfo> list = reviewRecordsInfoService.selectReviewRecordsInfoList(reviewRecordsInfo);
        ExcelUtil<ReviewRecordsInfo> util = new ExcelUtil<ReviewRecordsInfo>(ReviewRecordsInfo.class);
        util.exportExcel(response, list, "居家能力评估复核数据");
    }

    /**
     * 获取居家能力评估复核详细信息
     */
    //@RequiresPermissions("elderlyPeople:reviewRecordsInfo:query")
    @GetMapping(value = "/{id}")
    @ApiIgnore
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(reviewRecordsInfoService.selectReviewRecordsInfoById(id));
    }

    /**
     * 新增居家能力评估复核
     */
    //@RequiresPermissions("elderlyPeople:reviewRecordsInfo:add")
    @Log(platform = "2", title = "居家能力评估复核", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody ReviewRecordsInfo reviewRecordsInfo) {
        return toAjax(reviewRecordsInfoService.insertReviewRecordsInfo(reviewRecordsInfo));
    }

    /**
     * 修改居家能力评估复核
     */
    //@RequiresPermissions("elderlyPeople:reviewRecordsInfo:edit")
    @Log(platform = "2", title = "居家能力评估复核", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody ReviewRecordsInfo reviewRecordsInfo) {
        return toAjax(reviewRecordsInfoService.updateReviewRecordsInfo(reviewRecordsInfo));
    }

    /**
     * 删除居家能力评估复核
     */
    //@RequiresPermissions("elderlyPeople:reviewRecordsInfo:remove")
    @Log(platform = "2", title = "居家能力评估复核", businessType = BusinessType.DELETE)
    @ApiIgnore
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(reviewRecordsInfoService.deleteReviewRecordsInfoByIds(ids));
    }

    /**
     * 复核保存接口
     *
     * @param reviewRecordsInfo
     * @return
     */
    @PostMapping("/review")
    @ApiOperation(value = "复核保存接口")
    public AjaxResult review(@RequestBody ReviewRecordsInfo reviewRecordsInfo) {
        return AjaxResult.success(reviewRecordsInfoService.save(reviewRecordsInfo));
    }
}
