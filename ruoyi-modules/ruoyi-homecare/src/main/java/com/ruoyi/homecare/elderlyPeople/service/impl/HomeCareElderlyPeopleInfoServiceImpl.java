package com.ruoyi.homecare.elderlyPeople.service.impl;

import cn.hutool.json.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.combo.service.IHomeServiceRemindService;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.homecare.elderlyPeople.domain.vo.ElderlyPeopleInfoVo;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleInfoMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleInfoService;
import com.ruoyi.homecare.housekeeper.domain.HomeCommunityBaseInfo;
import com.ruoyi.homecare.housekeeper.domain.HomeStreetInfo;
import com.ruoyi.homecare.housekeeper.mapper.HomeStreetInfoMapper;
import com.ruoyi.homecare.housekeeper.service.IHomeCommunityBaseInfoService;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.utils.Base64ToMultipartUtils;
import com.ruoyi.homecare.utils.SysUserUtils;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 老人基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@Service
public class HomeCareElderlyPeopleInfoServiceImpl implements IHomeCareElderlyPeopleInfoService {
    @Autowired
    protected RemoteUserService remoteUserService;
    @Autowired
    private HomeCareElderlyPeopleInfoMapper elderlyPeopleInfoMapper;
    @Autowired
    private RemoteFileService remoteFileService;
    @Autowired
    private IHomeServiceRemindService homeServiceRemindService;
    @Autowired
    private IHomeCommunityBaseInfoService homeCommunityBaseInfoService;
    @Autowired
    private HomeStreetInfoMapper homeStreetInfoMapper;

    public static void main(String[] args) {
        BigDecimal zero = new BigDecimal(10);
        BigDecimal bigDecimal = new BigDecimal("10");
        zero.add(bigDecimal);
        System.out.println(zero);
    }

    /**
     * 查询老人基础信息
     *
     * @param id 老人基础信息主键
     * @return 老人基础信息
     */
    @Override
    public ElderlyPeopleInfo selectElderlyPeopleInfoById(String id) {
        ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoMapper.selectElderlyPeopleInfoById(id);
        if (!StringUtils.isEmpty(elderlyPeopleInfo.getServiceStationId())) {// 获取所属社区
            HomeStreetInfo info = homeStreetInfoMapper.selectHomeStreetInfoById(Long.parseLong(elderlyPeopleInfo.getServiceStationId()));
            if (null != info) {
                elderlyPeopleInfo.setServiceStationLabel(info.getName());
            }
        }
        return elderlyPeopleInfo;
    }

    /**
     * 通过系统userid查询老人基础信息
     *
     * @param sysUserId 老人基础信息主键
     * @return
     */
    @Override
    public ElderlyPeopleInfo getElderlyPeopleInfoBySysUserId(Long sysUserId) {
        return elderlyPeopleInfoMapper.getElderlyPeopleInfoBySysUserId(sysUserId);
    }

    /**
     * 查询老人基础信息列表
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 老人基础信息
     */
    @Override
    public List<ElderlyPeopleInfo> selectElderlyPeopleInfoList(ElderlyPeopleInfo elderlyPeopleInfo) {
        return elderlyPeopleInfoMapper.selectElderlyPeopleInfoList(elderlyPeopleInfo);
    }

    @Override
    public List<JSONObject> getUserList(String name, String state) {
        return elderlyPeopleInfoMapper.getUserList(name, state);
    }

    /**
     * 新增老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo) {
        elderlyPeopleInfo.setCreateTime(DateUtils.getNowDate());
        String id = IdUtils.fastSimpleUUID();
        elderlyPeopleInfo.setId(id);
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleInfo.setCreateBy(String.valueOf(userId));
        if (elderlyPeopleInfo.getImg().indexOf(";base64,") != -1) {
            MultipartFile file = Base64ToMultipartUtils.base64ToMultipart(elderlyPeopleInfo.getImg());
            if (!file.isEmpty()) {
                R<SysFile> fileResult = remoteFileService.upload(file);
                if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData())) {
                    return "文件服务异常，请联系管理员";
                }
                String url = fileResult.getData().getUrl();
                elderlyPeopleInfo.setImg(url);
            }
        }
        if (!StringUtils.isEmpty(elderlyPeopleInfo.getServiceComboId())) {
            homeServiceRemindService.saveList(null, elderlyPeopleInfo.getServiceComboId(), elderlyPeopleInfo.getId());
        }
        // 添加系统老人用户
        R<SysUser> sysUserR = SysUserUtils.addSysUser(elderlyPeopleInfo.getName(), elderlyPeopleInfo.getPhone(), null, "0", null, elderlyPeopleInfo.getPhone());
        if (sysUserR.getCode() != 200) {
            throw new ServiceException(sysUserR.getMsg());
        }
        elderlyPeopleInfo.setSysUserId(sysUserR.getData().getUserId());
        elderlyPeopleInfoMapper.insertElderlyPeopleInfo(elderlyPeopleInfo);

        return id;
    }

    /**
     * 修改老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo) {
        elderlyPeopleInfo.setUpdateTime(DateUtils.getNowDate());
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleInfo.setUpdateBy(String.valueOf(userId));
        if (elderlyPeopleInfo.getImg().indexOf(";base64,") != -1) {
            MultipartFile file = Base64ToMultipartUtils.base64ToMultipart(elderlyPeopleInfo.getImg());
            if (!file.isEmpty()) {
                R<SysFile> fileResult = remoteFileService.upload(file);
                if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData())) {
                    return 2;
                }
                String url = fileResult.getData().getUrl();
                elderlyPeopleInfo.setImg(url);
            }
        }
        if (!StringUtils.isEmpty(elderlyPeopleInfo.getServiceComboId())) {
            homeServiceRemindService.saveList(null, elderlyPeopleInfo.getServiceComboId(), elderlyPeopleInfo.getId());
        }
        return elderlyPeopleInfoMapper.updateElderlyPeopleInfo(elderlyPeopleInfo);
    }

    /**
     * 批量删除老人基础信息
     *
     * @param ids 需要删除的老人基础信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleInfoByIds(Long[] ids) {
        return elderlyPeopleInfoMapper.deleteElderlyPeopleInfoByIds(ids);
    }

    @Override
    public int logicalDeleteElderlyPeopleInfoByIds(String[] ids) {
        for (String id : ids) {
            ElderlyPeopleInfo elderlyPeopleInfo = selectElderlyPeopleInfoById(id);
            Long sysUserId = elderlyPeopleInfo.getSysUserId();
            if (null == sysUserId) {
                throw new SecurityException("此信息有问题，请联系管理员");
            }
            if ("1".equals(elderlyPeopleInfo.getVolunteerFlag())) {// 当前用户如果也是志愿者的话把当前系统用户的老人角色删除

                R<SysUser> infoByUserId = remoteUserService.getInfoByUserId(elderlyPeopleInfo.getSysUserId());
                if (infoByUserId.getCode() != 200) {
                    throw new ServiceException(String.valueOf(infoByUserId.getMsg()));
                }
                SysUser data = infoByUserId.getData();
                Long[] newRole = {8L};
                data.setRoleIds(newRole);
                AjaxResult ajaxResult = remoteUserService.editUser(data);
                if ((Integer) (ajaxResult.get("code")) != 200) {
                    throw new ServiceException(String.valueOf(ajaxResult.get("msg")));
                }

            } else {// 如果就一个单独的老人角色让其停用
                SysUser sysUser = new SysUser();
                sysUser.setUserId(sysUserId);
                sysUser.setStatus("1");
                AjaxResult ajaxResult = remoteUserService.changeStatus(sysUser);
                if ((Integer) ajaxResult.get("code") != 200) {
                    throw new SecurityException(String.valueOf(ajaxResult.get("msg")));
                }
            }

        }
        return elderlyPeopleInfoMapper.logicalDeleteElderlyPeopleInfoByIds(ids);
    }

    /**
     * 删除老人基础信息信息
     *
     * @param id 老人基础信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleInfoById(Long id) {
        return elderlyPeopleInfoMapper.deleteElderlyPeopleInfoById(id);
    }


}
