package com.ruoyi.homecare.serviceProviders.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProviderCommunityIndexMapper;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderCommunityIndexService;

/**
 * 服务商和社区关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Service
public class HomeServiceProviderCommunityIndexServiceImpl implements IHomeServiceProviderCommunityIndexService {
    @Autowired
    private HomeServiceProviderCommunityIndexMapper homeServiceProviderCommunityIndexMapper;

    /**
     * 查询服务商和社区关联
     *
     * @param id 服务商和社区关联主键
     * @return 服务商和社区关联
     */
    @Override
    public HomeServiceProviderCommunityIndex selectHomeServiceProviderCommunityIndexById(Long id) {
        return homeServiceProviderCommunityIndexMapper.selectHomeServiceProviderCommunityIndexById(id);
    }

    /**
     * 查询服务商和社区关联列表
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 服务商和社区关联
     */
    @Override
    public List<HomeServiceProviderCommunityIndex> selectHomeServiceProviderCommunityIndexList(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex) {
        return homeServiceProviderCommunityIndexMapper.selectHomeServiceProviderCommunityIndexList(homeServiceProviderCommunityIndex);
    }

    /**
     * 新增服务商和社区关联
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 结果
     */
    @Override
    public int insertHomeServiceProviderCommunityIndex(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex) {
        homeServiceProviderCommunityIndex.setCreateTime(DateUtils.getNowDate());
        return homeServiceProviderCommunityIndexMapper.insertHomeServiceProviderCommunityIndex(homeServiceProviderCommunityIndex);
    }

    /**
     * 修改服务商和社区关联
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 结果
     */
    @Override
    public int updateHomeServiceProviderCommunityIndex(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex) {
        homeServiceProviderCommunityIndex.setUpdateTime(DateUtils.getNowDate());
        return homeServiceProviderCommunityIndexMapper.updateHomeServiceProviderCommunityIndex(homeServiceProviderCommunityIndex);
    }

    /**
     * 批量删除服务商和社区关联
     *
     * @param ids 需要删除的服务商和社区关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeServiceProviderCommunityIndexByIds(Long[] ids) {
        return homeServiceProviderCommunityIndexMapper.deleteHomeServiceProviderCommunityIndexByIds(ids);
    }

    /**
     * 删除服务商和社区关联信息
     *
     * @param id 服务商和社区关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeServiceProviderCommunityIndexById(Long id) {
        return homeServiceProviderCommunityIndexMapper.deleteHomeServiceProviderCommunityIndexById(id);
    }

    /**
     * 通过服务商Id删除数据
     *
     * @param serviceProviderId
     * @return
     */
    @Override
    public int deleteByServiceId(Long serviceProviderId) {
        return homeServiceProviderCommunityIndexMapper.deleteByServiceId(serviceProviderId);
    }

    @Override
    public String getListIdByServiceId(Long baseId) {
        return homeServiceProviderCommunityIndexMapper.getListIdByServiceId(baseId);
    }
}
