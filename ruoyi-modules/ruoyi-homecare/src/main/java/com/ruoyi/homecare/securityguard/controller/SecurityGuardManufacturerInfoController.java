package com.ruoyi.homecare.securityguard.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardManufacturerInfo;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardManufacturerInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 厂商信息Controller
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
@RestController
@RequestMapping("/manufacturer")
public class SecurityGuardManufacturerInfoController extends BaseController {
    @Autowired
    private ISecurityGuardManufacturerInfoService securityGuardManufacturerInfoService;

    /**
     * 查询厂商信息列表
     */
    @RequiresPermissions("securityguard:manufacturer:list")
    @GetMapping("/list")
    public TableDataInfo list(SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        startPage();
        List<SecurityGuardManufacturerInfo> list = securityGuardManufacturerInfoService.selectSecurityGuardManufacturerInfoList(securityGuardManufacturerInfo);
        return getDataTable(list);
    }

    /**
     * 导出厂商信息列表
     */
    @RequiresPermissions("securityguard:manufacturer:export")
    @Log(title = "厂商信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        List<SecurityGuardManufacturerInfo> list = securityGuardManufacturerInfoService.selectSecurityGuardManufacturerInfoList(securityGuardManufacturerInfo);
        ExcelUtil<SecurityGuardManufacturerInfo> util = new ExcelUtil<SecurityGuardManufacturerInfo>(SecurityGuardManufacturerInfo.class);
        util.exportExcel(response, list, "厂商信息数据");
    }

    /**
     * 获取厂商信息详细信息
     */
    @RequiresPermissions("securityguard:manufacturer:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardManufacturerInfoService.selectSecurityGuardManufacturerInfoById(id));
    }

    /**
     * 新增厂商信息
     */
    @RequiresPermissions("securityguard:manufacturer:add")
    @Log(title = "厂商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        return toAjax(securityGuardManufacturerInfoService.insertSecurityGuardManufacturerInfo(securityGuardManufacturerInfo));
    }

    /**
     * 修改厂商信息
     */
    @RequiresPermissions("securityguard:manufacturer:edit")
    @Log(title = "厂商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        return toAjax(securityGuardManufacturerInfoService.updateSecurityGuardManufacturerInfo(securityGuardManufacturerInfo));
    }

    /**
     * 删除厂商信息
     */
    @RequiresPermissions("securityguard:manufacturer:remove")
    @Log(title = "厂商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardManufacturerInfoService.deleteSecurityGuardManufacturerInfoByIds(ids));
    }
}
