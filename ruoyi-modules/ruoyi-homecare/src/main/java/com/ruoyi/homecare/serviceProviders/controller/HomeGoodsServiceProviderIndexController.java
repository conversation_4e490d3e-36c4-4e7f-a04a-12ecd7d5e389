package com.ruoyi.homecare.serviceProviders.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.serviceProviders.domain.HomeGoodsServiceProviderIndex;
import com.ruoyi.homecare.serviceProviders.service.IHomeGoodsServiceProviderIndexService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 商品和服务商关联Controller
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
@RestController
@RequestMapping("/homeGoodsServiceProviderIndex")
@Api(tags = "商品和服务商关联", value = "商品和服务商关联")
public class HomeGoodsServiceProviderIndexController extends BaseController {
    @Autowired
    private IHomeGoodsServiceProviderIndexService homeGoodsServiceProviderIndexService;

    /**
     * 查询商品和服务商关联列表
     */
    //@RequiresPermissions("serviceProviders:homeGoodsServiceProviderIndex:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex) {
        startPage();
        List<HomeGoodsServiceProviderIndex> list = homeGoodsServiceProviderIndexService.selectHomeGoodsServiceProviderIndexList(homeGoodsServiceProviderIndex);
        return getDataTable(list);
    }

    /**
     * 导出商品和服务商关联列表
     */
    //@RequiresPermissions("serviceProviders:homeGoodsServiceProviderIndex:export")
    @Log(platform = "2", title = "商品和服务商关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex) {
        List<HomeGoodsServiceProviderIndex> list = homeGoodsServiceProviderIndexService.selectHomeGoodsServiceProviderIndexList(homeGoodsServiceProviderIndex);
        ExcelUtil<HomeGoodsServiceProviderIndex> util = new ExcelUtil<HomeGoodsServiceProviderIndex>(HomeGoodsServiceProviderIndex.class);
        util.exportExcel(response, list, "商品和服务商关联数据");
    }

    /**
     * 获取商品和服务商关联详细信息
     */
    //@RequiresPermissions("serviceProviders:homeGoodsServiceProviderIndex:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(homeGoodsServiceProviderIndexService.selectHomeGoodsServiceProviderIndexById(id));
    }

    /**
     * 新增商品和服务商关联
     */
    //@RequiresPermissions("serviceProviders:homeGoodsServiceProviderIndex:add")
    @Log(platform = "2", title = "商品和服务商关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex) {
        return toAjax(homeGoodsServiceProviderIndexService.insertHomeGoodsServiceProviderIndex(homeGoodsServiceProviderIndex));
    }

    /**
     * 修改商品和服务商关联
     */
    //@RequiresPermissions("serviceProviders:homeGoodsServiceProviderIndex:edit")
    @Log(platform = "2", title = "商品和服务商关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex) {
        return toAjax(homeGoodsServiceProviderIndexService.updateHomeGoodsServiceProviderIndex(homeGoodsServiceProviderIndex));
    }

    /**
     * 删除商品和服务商关联
     */
    //@RequiresPermissions("serviceProviders:homeGoodsServiceProviderIndex:remove")
    @Log(platform = "2", title = "商品和服务商关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeGoodsServiceProviderIndexService.deleteHomeGoodsServiceProviderIndexByIds(ids));
    }
}
