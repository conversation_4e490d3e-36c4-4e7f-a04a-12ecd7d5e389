package com.ruoyi.homecare.elderlyPeople.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleLifeStyle;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleLifeStyleMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleLifeStyleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 老人生活方式信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Service
public class HomeCareElderlyPeopleLifeStyleServiceImpl implements IHomeCareElderlyPeopleLifeStyleService {
    @Autowired
    private HomeCareElderlyPeopleLifeStyleMapper elderlyPeopleLifeStyleMapper;

    /**
     * 查询老人生活方式信息
     *
     * @param id 老人生活方式信息主键
     * @return 老人生活方式信息
     */
    @Override
    public ElderlyPeopleLifeStyle selectElderlyPeopleLifeStyleById(String id) {
        return elderlyPeopleLifeStyleMapper.selectElderlyPeopleLifeStyleById(id);
    }

    /**
     * 查询老人生活方式信息列表
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 老人生活方式信息
     */
    @Override
    public List<ElderlyPeopleLifeStyle> selectElderlyPeopleLifeStyleList(ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        return elderlyPeopleLifeStyleMapper.selectElderlyPeopleLifeStyleList(elderlyPeopleLifeStyle);
    }

    /**
     * 新增老人生活方式信息
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 结果
     */
    @Override
    public int insertElderlyPeopleLifeStyle(ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        elderlyPeopleLifeStyle.setCreateTime(DateUtils.getNowDate());
        String id = IdUtils.fastSimpleUUID();
        elderlyPeopleLifeStyle.setId(id);
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleLifeStyle.setCreateBy(String.valueOf(userId));
        return elderlyPeopleLifeStyleMapper.insertElderlyPeopleLifeStyle(elderlyPeopleLifeStyle);
    }

    /**
     * 修改老人生活方式信息
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 结果
     */
    @Override
    public int updateElderlyPeopleLifeStyle(ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        elderlyPeopleLifeStyle.setUpdateTime(DateUtils.getNowDate());
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleLifeStyle.setUpdateBy(String.valueOf(userId));
        return elderlyPeopleLifeStyleMapper.updateElderlyPeopleLifeStyle(elderlyPeopleLifeStyle);
    }

    /**
     * 批量删除老人生活方式信息
     *
     * @param ids 需要删除的老人生活方式信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleLifeStyleByIds(String[] ids) {
        return elderlyPeopleLifeStyleMapper.deleteElderlyPeopleLifeStyleByIds(ids);
    }

    /**
     * 删除老人生活方式信息信息
     *
     * @param id 老人生活方式信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleLifeStyleById(String id) {
        return elderlyPeopleLifeStyleMapper.deleteElderlyPeopleLifeStyleById(id);
    }
}
