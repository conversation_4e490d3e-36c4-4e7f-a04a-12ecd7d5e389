package com.ruoyi.homecare.serviceProviders.service;

import java.util.List;

import com.ruoyi.homecare.serviceProviders.domain.HomeRegisterServiceProvider;

/**
 * 注册服务商记录Service接口
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
public interface IHomeRegisterServiceProviderService {
    /**
     * 查询注册服务商记录
     *
     * @param id 注册服务商记录主键
     * @return 注册服务商记录
     */
    public HomeRegisterServiceProvider selectHomeRegisterServiceProviderById(Long id);

    /**
     * 查询注册服务商记录列表
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 注册服务商记录集合
     */
    public List<HomeRegisterServiceProvider> selectHomeRegisterServiceProviderList(HomeRegisterServiceProvider homeRegisterServiceProvider);

    /**
     * 新增注册服务商记录
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 结果
     */
    public int insertHomeRegisterServiceProvider(HomeRegisterServiceProvider homeRegisterServiceProvider);

    /**
     * 修改注册服务商记录
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 结果
     */
    public int updateHomeRegisterServiceProvider(HomeRegisterServiceProvider homeRegisterServiceProvider);

    /**
     * 批量删除注册服务商记录
     *
     * @param ids 需要删除的注册服务商记录主键集合
     * @return 结果
     */
    public int deleteHomeRegisterServiceProviderByIds(Long[] ids);

    /**
     * 删除注册服务商记录信息
     *
     * @param id 注册服务商记录主键
     * @return 结果
     */
    public int deleteHomeRegisterServiceProviderById(Long id);

    /**
     * 再次提交注册服务商
     *
     * @param homeRegisterServiceProvider
     * @return
     */
    int againRegisterServiceProvider(HomeRegisterServiceProvider homeRegisterServiceProvider);

    /**
     * 根据系统id获取商家信息
     *
     * @param id
     * @return
     */
    HomeRegisterServiceProvider getServiceProvidersInfoBySysUserId(Long id);
}
