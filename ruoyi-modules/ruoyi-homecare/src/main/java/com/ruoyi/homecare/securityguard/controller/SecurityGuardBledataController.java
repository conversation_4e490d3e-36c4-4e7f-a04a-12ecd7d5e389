package com.ruoyi.homecare.securityguard.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardBledata;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardBledataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 血压信息Controller
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@RestController
@RequestMapping("/bledata")
public class SecurityGuardBledataController extends BaseController {
    @Autowired
    private ISecurityGuardBledataService securityGuardBledataService;

    /**
     * 查询血压信息列表
     */
    @RequiresPermissions("securityguard:bledata:list")
    @GetMapping("/list")
    public TableDataInfo list(SecurityGuardBledata securityGuardBledata) {
        startPage();
        List<SecurityGuardBledata> list = securityGuardBledataService.selectSecurityGuardBledataList(securityGuardBledata);
        return getDataTable(list);
    }

    /**
     * 导出血压信息列表
     */
    @RequiresPermissions("securityguard:bledata:export")
    @Log(title = "血压信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecurityGuardBledata securityGuardBledata) {
        List<SecurityGuardBledata> list = securityGuardBledataService.selectSecurityGuardBledataList(securityGuardBledata);
        ExcelUtil<SecurityGuardBledata> util = new ExcelUtil<SecurityGuardBledata>(SecurityGuardBledata.class);
        util.exportExcel(response, list, "血压信息数据");
    }

    /**
     * 获取血压信息详细信息
     */
    @RequiresPermissions("securityguard:bledata:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardBledataService.selectSecurityGuardBledataById(id));
    }

    /**
     * 新增血压信息
     */
    @RequiresPermissions("securityguard:bledata:add")
    @Log(title = "血压信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityGuardBledata securityGuardBledata) {
        return toAjax(securityGuardBledataService.insertSecurityGuardBledata(securityGuardBledata));
    }

    /**
     * 修改血压信息
     */
    @RequiresPermissions("securityguard:bledata:edit")
    @Log(title = "血压信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityGuardBledata securityGuardBledata) {
        return toAjax(securityGuardBledataService.updateSecurityGuardBledata(securityGuardBledata));
    }

    /**
     * 删除血压信息
     */
    @RequiresPermissions("securityguard:bledata:remove")
    @Log(title = "血压信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardBledataService.deleteSecurityGuardBledataByIds(ids));
    }
}
