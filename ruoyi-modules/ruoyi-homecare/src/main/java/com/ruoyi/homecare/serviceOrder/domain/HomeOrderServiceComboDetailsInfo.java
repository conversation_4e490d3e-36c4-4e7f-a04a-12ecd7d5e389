package com.ruoyi.homecare.serviceOrder.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description home_order_service_combo_details_info
 * @date 2022-07-15
 */
@Data
@ApiModel("t_home_order_service_combo_details_info")
@TableName("t_home_order_service_combo_details_info")
public class HomeOrderServiceComboDetailsInfo implements Serializable {

    public static final Integer COMPLETED_STATUS_HAS = 1;   // 完成状态 1：未完成 2：已完成
    public static final Integer COMPLETED_STATUS_NONE = 2;
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private String orderId;

    /**
     * 订单id
     */
    @ApiModelProperty("服务商id")
    private Long providerId;

    /**
     * 订单套餐关联id
     */
    @ApiModelProperty("订单套餐关联id")
    private Long orderComboId;

    /**
     * 服务id
     */
    @ApiModelProperty("服务id")
    private Long serviceId;

    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    private String serviceName;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 老人id
     */
    @ApiModelProperty("老人id")
    private String elderlyPeopleId;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal price;

    /**
     * 图片
     */
    @ApiModelProperty("图片")
    private String img;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String describeMsg;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Integer number;

    /**
     * 完成数量
     */
    @ApiModelProperty("完成次数")
    private Integer completedCount;

    /**
     * 总次数
     */
    @ApiModelProperty("总次数")
    private Integer totalCount;

    /**
     * 总次数
     */
    @ApiModelProperty("单项服务次数")
    private Integer count;

    /**
     * 完成状态 1：剩余 2：已用完
     */
    @ApiModelProperty("完成状态 1：剩余 2：已用完")
    private Integer completedStatus;

    /**
     * 过期时间
     */
    @ApiModelProperty("过期时间")
    private Date expiredDate;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("计费方式（字典） 0：次 1：件 2：小时")
    private Integer chargeMode;

}
