package com.ruoyi.homecare.elderlyPeople.service;

import com.ruoyi.homecare.elderlyPeople.domain.QuestionnaireRecords;

import java.util.List;

/**
 * 调查问卷历史Service接口
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
public interface IHomeCareQuestionnaireRecordsService {
    /**
     * 查询调查问卷历史
     *
     * @param id 调查问卷历史主键
     * @return 调查问卷历史
     */
    public QuestionnaireRecords selectQuestionnaireRecordsById(Long id);

    /**
     * 查询调查问卷历史列表
     *
     * @param questionnaireRecords 调查问卷历史
     * @return 调查问卷历史集合
     */
    public List<QuestionnaireRecords> selectQuestionnaireRecordsList(QuestionnaireRecords questionnaireRecords);

    /**
     * 新增调查问卷历史
     *
     * @param questionnaireRecords 调查问卷历史
     * @return 结果
     */
    public int insertQuestionnaireRecords(QuestionnaireRecords questionnaireRecords);

    /**
     * 修改调查问卷历史
     *
     * @param questionnaireRecords 调查问卷历史
     * @return 结果
     */
    public int updateQuestionnaireRecords(QuestionnaireRecords questionnaireRecords);

    /**
     * 批量删除调查问卷历史
     *
     * @param ids 需要删除的调查问卷历史主键集合
     * @return 结果
     */
    public int deleteQuestionnaireRecordsByIds(Long[] ids);

    /**
     * 删除调查问卷历史信息
     *
     * @param id 调查问卷历史主键
     * @return 结果
     */
    public int deleteQuestionnaireRecordsById(Long id);
}
