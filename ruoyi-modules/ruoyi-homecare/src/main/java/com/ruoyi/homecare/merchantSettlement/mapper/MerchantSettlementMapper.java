package com.ruoyi.homecare.merchantSettlement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.homecare.merchantSettlement.domain.MerchantSettlement;
import com.ruoyi.homecare.merchantSettlement.vo.MSOrderInfoResVo;
import com.ruoyi.homecare.merchantSettlement.vo.MerchantSettlementListReqVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 商家结算信息表Mapper
 * @date 2022-08-10
 */
@Mapper
public interface MerchantSettlementMapper extends BaseMapper<MerchantSettlement> {

    @Select(
            "<script>select t0.* from merchant_settlement t0 " +
                    // add here if need left join
                    "where 1=1" +
                    "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
                    "<when test='serviceProviderId!=null and serviceProviderId!=&apos;&apos; '> and t0.service_provider_id=#{serviceProviderId}</when> " +
                    "<when test='serviceProviderName!=null and serviceProviderName!=&apos;&apos; '> and t0.service_provider_name=#{serviceProviderName}</when> " +
                    "<when test='billName!=null and billName!=&apos;&apos; '> and t0.bill_name=#{billName}</when> " +
                    "<when test='billMonth!=null and billMonth!=&apos;&apos; '> and t0.bill_month=#{billMonth}</when> " +
                    "<when test='businessAmount!=null and businessAmount!=&apos;&apos; '> and t0.business_amount=#{businessAmount}</when> " +
                    "<when test='commissionAmount!=null and commissionAmount!=&apos;&apos; '> and t0.commission_amount=#{commissionAmount}</when> " +
                    "<when test='status!=null and status!=&apos;&apos; '> and t0.status=#{status}</when> " +
                    "<when test='settlementAmount!=null and settlementAmount!=&apos;&apos; '> and t0.settlement_amount=#{settlementAmount}</when> " +
                    "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
                    // add here if need page limit
                    //" limit ${page},${limit} " +
                    " </script>")
    List<MerchantSettlement> pageAll(MerchantSettlement queryParamDTO, int page, int limit);

    @Select("<script>select count(1) from merchant_settlement t0 " +
            // add here if need left join
            "where 1=1" +
            "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
            "<when test='serviceProviderId!=null and serviceProviderId!=&apos;&apos; '> and t0.service_provider_id=#{serviceProviderId}</when> " +
            "<when test='serviceProviderName!=null and serviceProviderName!=&apos;&apos; '> and t0.service_provider_name=#{serviceProviderName}</when> " +
            "<when test='billName!=null and billName!=&apos;&apos; '> and t0.bill_name=#{billName}</when> " +
            "<when test='billMonth!=null and billMonth!=&apos;&apos; '> and t0.bill_month=#{billMonth}</when> " +
            "<when test='businessAmount!=null and businessAmount!=&apos;&apos; '> and t0.business_amount=#{businessAmount}</when> " +
            "<when test='commissionAmount!=null and commissionAmount!=&apos;&apos; '> and t0.commission_amount=#{commissionAmount}</when> " +
            "<when test='status!=null and status!=&apos;&apos; '> and t0.status=#{status}</when> " +
            "<when test='settlementAmount!=null and settlementAmount!=&apos;&apos; '> and t0.settlement_amount=#{settlementAmount}</when> " +
            "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
            " </script>")
    int countAll(MerchantSettlement queryParamDTO);

    List<MSOrderInfoResVo> getOrderList(MerchantSettlementListReqVo reqVo);

    MerchantSettlement selectServiceProviderMonthCountInfo(@Param("serviceProviderId") Long serviceProviderId, @Param("queryMonth") String queryMonth);
}
