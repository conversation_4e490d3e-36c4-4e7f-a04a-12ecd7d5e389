package com.ruoyi.homecare.serviceProviders.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.serviceProviders.domain.HomeRegisterServiceProvider;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.mapper.HomeRegisterServiceProviderMapper;
import com.ruoyi.homecare.serviceProviders.service.IHomeRegisterServiceProviderService;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderManagementService;
import com.ruoyi.homecare.utils.SysUserUtils;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 注册服务商记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@Service
public class HomeRegisterServiceProviderServiceImpl implements IHomeRegisterServiceProviderService {
    @Autowired
    private HomeRegisterServiceProviderMapper homeRegisterServiceProviderMapper;

    @Autowired
    private IHomeServiceProviderManagementService homeServiceProviderManagementService;

    /**
     * 查询注册服务商记录
     *
     * @param id 注册服务商记录主键
     * @return 注册服务商记录
     */
    @Override
    public HomeRegisterServiceProvider selectHomeRegisterServiceProviderById(Long id) {
        HomeRegisterServiceProvider homeRegisterServiceProvider = homeRegisterServiceProviderMapper.selectHomeRegisterServiceProviderById(id);
        strToList(homeRegisterServiceProvider);
        return homeRegisterServiceProvider;
    }

    /**
     * 查询注册服务商记录列表
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 注册服务商记录
     */
    @Override
    public List<HomeRegisterServiceProvider> selectHomeRegisterServiceProviderList(HomeRegisterServiceProvider homeRegisterServiceProvider) {
        return homeRegisterServiceProviderMapper.selectHomeRegisterServiceProviderList(homeRegisterServiceProvider);
    }

    /**
     * 新增注册服务商记录
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 结果
     */
    @Override
    public int insertHomeRegisterServiceProvider(HomeRegisterServiceProvider homeRegisterServiceProvider) {

        listToStr(homeRegisterServiceProvider);

        homeRegisterServiceProvider.setCreateTime(new Date());
        // 自己注册需要管理员审核
        R<SysUser> loginUserR = SysUserUtils.addSysUser(homeRegisterServiceProvider.getName(), homeRegisterServiceProvider.getUserName(), homeRegisterServiceProvider.getPassword(), "4", null, homeRegisterServiceProvider.getPhone());
        if (loginUserR.getCode() != 200) {
            throw new ServiceException(loginUserR.getMsg());
        }
        SysUser sysUser = loginUserR.getData();
        homeRegisterServiceProvider.setSysUserId(sysUser.getUserId());
        return homeRegisterServiceProviderMapper.insertHomeRegisterServiceProvider(homeRegisterServiceProvider);
    }

    private void listToStr(HomeRegisterServiceProvider homeRegisterServiceProvider) {
        List<String> labelList = homeRegisterServiceProvider.getLabelList();
        homeRegisterServiceProvider.setLabel(CollUtil.join(labelList, ","));

        List<String> businessLicenseList = homeRegisterServiceProvider.getBusinessLicenseList();
        homeRegisterServiceProvider.setBusinessLicense(CollUtil.join(businessLicenseList, ","));

        List<String> contractAttachmentList = homeRegisterServiceProvider.getContractAttachmentList();
        homeRegisterServiceProvider.setContractAttachment(CollUtil.join(contractAttachmentList, ","));

        List<String> serviceTypeList = homeRegisterServiceProvider.getServiceTypeList();
        homeRegisterServiceProvider.setServiceType(CollUtil.join(serviceTypeList, ","));

        List<String> merchantsPhotosList = homeRegisterServiceProvider.getMerchantsPhotosList();
        homeRegisterServiceProvider.setMerchantsPhotos(CollUtil.join(merchantsPhotosList, ","));

        List<Integer> communityIdList = homeRegisterServiceProvider.getCommunityIdList();
        homeRegisterServiceProvider.setCommunityId(CollUtil.join(communityIdList, ","));
    }


    private void strToList(HomeRegisterServiceProvider homeRegisterServiceProvider) {


        if (StrUtil.isEmpty(homeRegisterServiceProvider.getServiceType())) {
            homeRegisterServiceProvider.setServiceTypeList(new ArrayList<>());
        } else {
            List<String> serviceTypeList = StrUtil.split(homeRegisterServiceProvider.getServiceType(), ",");
            homeRegisterServiceProvider.setServiceTypeList(serviceTypeList);
        }


        if (StrUtil.isEmpty(homeRegisterServiceProvider.getLabel())) {
            homeRegisterServiceProvider.setLabelList(new ArrayList<>());
        } else {
            List<String> labelList = StrUtil.split(homeRegisterServiceProvider.getLabel(), ",");
            homeRegisterServiceProvider.setLabelList(labelList);
        }


        if (StrUtil.isEmpty(homeRegisterServiceProvider.getBusinessLicense())) {
            homeRegisterServiceProvider.setBusinessLicenseList(new ArrayList<>());
        } else {
            List<String> businessLicenseList = StrUtil.split(homeRegisterServiceProvider.getBusinessLicense(), ",");
            homeRegisterServiceProvider.setBusinessLicenseList(businessLicenseList);
        }


        if (StrUtil.isEmpty(homeRegisterServiceProvider.getContractAttachment())) {
            homeRegisterServiceProvider.setContractAttachmentList(new ArrayList<>());
        } else {
            List<String> businessLicenseList = StrUtil.split(homeRegisterServiceProvider.getContractAttachment(), ",");
            homeRegisterServiceProvider.setContractAttachmentList(businessLicenseList);
        }


        if (StrUtil.isEmpty(homeRegisterServiceProvider.getMerchantsPhotos())) {
            homeRegisterServiceProvider.setMerchantsPhotosList(new ArrayList<>());
        } else {
            List<String> businessLicenseList = StrUtil.split(homeRegisterServiceProvider.getMerchantsPhotos(), ",");
            homeRegisterServiceProvider.setMerchantsPhotosList(businessLicenseList);
        }


        if (StrUtil.isEmpty(homeRegisterServiceProvider.getCommunityId())) {
            homeRegisterServiceProvider.setCommunityIdList(new ArrayList<>());
        } else {
            List<Integer> communityIdList = Arrays.stream(homeRegisterServiceProvider.getCommunityId().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            homeRegisterServiceProvider.setCommunityIdList(communityIdList);
        }


    }


    /**
     * 修改注册服务商记录
     *
     * @param homeRegisterServiceProvider 注册服务商记录
     * @return 结果
     */
    @Override
    public int updateHomeRegisterServiceProvider(HomeRegisterServiceProvider homeRegisterServiceProvider) {
        homeRegisterServiceProvider.setUpdateTime(DateUtils.getNowDate());
        return homeRegisterServiceProviderMapper.updateHomeRegisterServiceProvider(homeRegisterServiceProvider);
    }

    /**
     * 批量删除注册服务商记录
     *
     * @param ids 需要删除的注册服务商记录主键
     * @return 结果
     */
    @Override
    public int deleteHomeRegisterServiceProviderByIds(Long[] ids) {
        return homeRegisterServiceProviderMapper.deleteHomeRegisterServiceProviderByIds(ids);
    }

    /**
     * 删除注册服务商记录信息
     *
     * @param id 注册服务商记录主键
     * @return 结果
     */
    @Override
    public int deleteHomeRegisterServiceProviderById(Long id) {
        return homeRegisterServiceProviderMapper.deleteHomeRegisterServiceProviderById(id);
    }

    /**
     * 再次提交注册服务商
     *
     * @param homeRegisterServiceProvider
     * @return
     */
    @Override
    public int againRegisterServiceProvider(HomeRegisterServiceProvider homeRegisterServiceProvider) {

        listToStr(homeRegisterServiceProvider);

        if (!"1".equals(homeRegisterServiceProvider.getState())) {// 再次提交审核
            Long userId = SecurityUtils.getUserId();
            homeRegisterServiceProviderMapper.updateBySysUserId(userId);
            homeRegisterServiceProvider.setSysUserId(userId);
            homeRegisterServiceProvider.setAuditTime(null);
            homeRegisterServiceProvider.setFlag("1");
            homeRegisterServiceProvider.setSelectState("0");
            homeRegisterServiceProvider.setCreateTime(new Date());
            homeRegisterServiceProvider.setState("0");
            return homeRegisterServiceProviderMapper.insertHomeRegisterServiceProvider(homeRegisterServiceProvider);
        } else {// 修改审核通过的表数据
            HomeServiceProviderManagement homeServiceProviderManagement = new HomeServiceProviderManagement();
            BeanUtils.copyProperties(homeRegisterServiceProvider, homeServiceProviderManagement);
            return homeServiceProviderManagementService.updateHomeServiceProviderManagement(homeServiceProviderManagement);
        }
    }

    /**
     * 根据系统id获取商家信息
     *
     * @param id
     * @return
     */
    @Override
    public HomeRegisterServiceProvider getServiceProvidersInfoBySysUserId(Long id) {
        HomeRegisterServiceProvider homeRegisterServiceProvider = new HomeRegisterServiceProvider();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        List<SysRole> roleIds = sysUser.getRoles();
        List<Long> roleList = new ArrayList<>();
        for (SysRole role : roleIds) { // 组装角色id来判断是否已经审核
            Long roleId = role.getRoleId();
            roleList.add(roleId);
        }
        if (roleList.contains(5L) || roleList.contains(12L) || roleList.contains(13L)) {// 5服务服务商 12餐品服务商 13商品服务商
            HomeServiceProviderManagement serviceProviderBySysUserId = homeServiceProviderManagementService.getServiceProviderBySysUserId(loginUser.getUserid());
            BeanUtils.copyProperties(serviceProviderBySysUserId, homeRegisterServiceProvider);
        } else {
            homeRegisterServiceProvider = homeRegisterServiceProviderMapper.getServiceProvidersInfoBySysUserId(loginUser.getUserid());
        }

        HomeServiceProviderManagement providerManagement = homeServiceProviderManagementService.getServiceProviderBySysUserId(loginUser.getUserid());
        if (providerManagement != null && providerManagement.getBanFlag() != null) {
            homeRegisterServiceProvider.setBanFlag(providerManagement.getBanFlag().toString());
        } else {
            homeRegisterServiceProvider.setBanFlag("0");
        }

        strToList(homeRegisterServiceProvider);

        return homeRegisterServiceProvider;
    }
}
