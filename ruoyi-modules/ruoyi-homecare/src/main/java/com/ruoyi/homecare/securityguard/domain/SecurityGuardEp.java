package com.ruoyi.homecare.securityguard.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * ep设备对象 t_security_guard_ep
 *
 * <AUTHOR>
 * @date 2022-12-01
 */
public class SecurityGuardEp extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String name;

    /**
     * ep设备的 ep 值
     */
    @Excel(name = "ep设备的 ep 值")
    private String ep;

    /**
     * ep设备的 MAC 值
     */
    @Excel(name = "ep设备的 MAC 值")
    private String ieee;

    /**
     * 设备类型：smartPlug（81 0x0051）
     * light（256 0x0100）
     * sensor(1026 0x0402)
     * WA201(49665 0xC201)
     * PCT501(769 0x0301)
     * IAS warning device(1027 0x0403)
     */
    @Excel(name = "设备类型（）")
    private Integer devicetype;

    /**
     * 在线连接状态
     */
    @Excel(name = "在线连接状态")
    private Integer linkstatus;

    /**
     * profileid
     */
    @Excel(name = "profileid")
    private Integer profileid;

    /**
     * 设备类型：deviceType=0x0402 时有效，表示传感器的类型，如：
     * doorSensor(0x0015)
     * motionSensor(0x000d)
     * smokeSensor(0x0028)
     * IAS warning device(0x0225)
     */
    @Excel(name = "设备型号")
    private String devmodel;

    /**
     * 传感器的类型
     */
    @Excel(name = "传感器的类型")
    private Integer iaszonetype;

    /**
     * ep节点的设备属性：0—coordinator，1—router，2—end device
     */
    @Excel(name = "ep节点的设备属性：0—coordinator，1—router，2—end device")
    private Integer netdevicetype;

    /**
     * 设备支持场景功能
     */
    @Excel(name = "设备支持场景功能")
    private Integer clusterflag;

    /**
     * 设备工厂码
     */
    @Excel(name = "设备工厂码")
    private Integer manucode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEp() {
        return ep;
    }

    public void setEp(String ep) {
        this.ep = ep;
    }

    public String getIeee() {
        return ieee;
    }

    public void setIeee(String ieee) {
        this.ieee = ieee;
    }

    public Integer getDevicetype() {
        return devicetype;
    }

    public void setDevicetype(Integer devicetype) {
        this.devicetype = devicetype;
    }

    public Integer getLinkstatus() {
        return linkstatus;
    }

    public void setLinkstatus(Integer linkstatus) {
        this.linkstatus = linkstatus;
    }

    public Integer getProfileid() {
        return profileid;
    }

    public void setProfileid(Integer profileid) {
        this.profileid = profileid;
    }

    public String getDevmodel() {
        return devmodel;
    }

    public void setDevmodel(String devmodel) {
        this.devmodel = devmodel;
    }

    public Integer getIaszonetype() {
        return iaszonetype;
    }

    public void setIaszonetype(Integer iaszonetype) {
        this.iaszonetype = iaszonetype;
    }

    public Integer getNetdevicetype() {
        return netdevicetype;
    }

    public void setNetdevicetype(Integer netdevicetype) {
        this.netdevicetype = netdevicetype;
    }

    public Integer getClusterflag() {
        return clusterflag;
    }

    public void setClusterflag(Integer clusterflag) {
        this.clusterflag = clusterflag;
    }

    public Integer getManucode() {
        return manucode;
    }

    public void setManucode(Integer manucode) {
        this.manucode = manucode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("ep", getEp())
                .append("ieee", getIeee())
                .append("devicetype", getDevicetype())
                .append("linkstatus", getLinkstatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("profileid", getProfileid())
                .append("devmodel", getDevmodel())
                .append("iaszonetype", getIaszonetype())
                .append("netdevicetype", getNetdevicetype())
                .append("clusterflag", getClusterflag())
                .append("manucode", getManucode())
                .toString();
    }
}
