package com.ruoyi.homecare.securityguard.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardEp;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardEpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ep设备Controller
 *
 * <AUTHOR>
 * @date 2022-12-01
 */
@RestController
@RequestMapping("/ep")
public class SecurityGuardEpController extends BaseController {
    @Autowired
    private ISecurityGuardEpService securityGuardEpService;

    /**
     * 查询ep设备列表
     */
    @RequiresPermissions("securityguard:ep:list")
    @GetMapping("/list")
    public TableDataInfo list(SecurityGuardEp securityGuardEp) {
        startPage();
        List<SecurityGuardEp> list = securityGuardEpService.selectSecurityGuardEpList(securityGuardEp);
        return getDataTable(list);
    }

    /**
     * 导出ep设备列表
     */
    @RequiresPermissions("securityguard:ep:export")
    @Log(title = "ep设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecurityGuardEp securityGuardEp) {
        List<SecurityGuardEp> list = securityGuardEpService.selectSecurityGuardEpList(securityGuardEp);
        ExcelUtil<SecurityGuardEp> util = new ExcelUtil<SecurityGuardEp>(SecurityGuardEp.class);
        util.exportExcel(response, list, "ep设备数据");
    }

    /**
     * 获取ep设备详细信息
     */
    @RequiresPermissions("securityguard:ep:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardEpService.selectSecurityGuardEpById(id));
    }

    /**
     * 新增ep设备
     */
    @RequiresPermissions("securityguard:ep:add")
    @Log(title = "ep设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityGuardEp securityGuardEp) {
        return toAjax(securityGuardEpService.insertSecurityGuardEp(securityGuardEp));
    }

    /**
     * 修改ep设备
     */
    @RequiresPermissions("securityguard:ep:edit")
    @Log(title = "ep设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityGuardEp securityGuardEp) {
        return toAjax(securityGuardEpService.updateSecurityGuardEp(securityGuardEp));
    }

    /**
     * 删除ep设备
     */
    @RequiresPermissions("securityguard:ep:remove")
    @Log(title = "ep设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardEpService.deleteSecurityGuardEpByIds(ids));
    }
}
