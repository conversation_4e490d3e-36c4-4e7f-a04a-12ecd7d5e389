package com.ruoyi.homecare.serviceOrder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName
 * @Description
 * <AUTHOR>
 * @Date 2022/8/05 14:51
 */
@Data
@ApiModel("服务商PC服务列表订单返回值")
public class HomeProviderOrderServiceListVo {


    @ApiModelProperty("工单号")
    private String id;

    @ApiModelProperty("订单号")
    private String orderId;

    @ApiModelProperty("服务商名称")
    private String providerName;

    @ApiModelProperty("下单人名称")
    private String userName;

    @ApiModelProperty("订单状态")
    private String statusLabel;

    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    @ApiModelProperty("下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;

    @ApiModelProperty("老人名称")
    private String elderlyName;

    @ApiModelProperty("老人电话")
    private String elderlyPhone;

    @ApiModelProperty("订单状态 0：已关闭 1：待支付 2：已付款 3：已接单 4：已完成  5:已评价 6：退款")
    private Integer status;

    @ApiModelProperty("工单状态(1待指派人员，2未开始，3服务中，4已完成)")
    private Integer workOrderStatus;

    @ApiModelProperty("服务类型 0服务，1服务套餐")
    private Integer serviceType;


}
