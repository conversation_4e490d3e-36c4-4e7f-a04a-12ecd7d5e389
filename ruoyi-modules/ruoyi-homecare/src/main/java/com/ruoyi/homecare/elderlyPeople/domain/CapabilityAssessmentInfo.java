package com.ruoyi.homecare.elderlyPeople.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.homecare.utils.DictUtils;
import io.seata.common.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 能力评估报告对象 t_capability_assessment_info
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@ApiModel(value = "能力评估报告")
public class CapabilityAssessmentInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 老人id
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id")
    private String userId;

    /**
     * 评估原因值
     */
    @Excel(name = "评估原因值")
    @ApiModelProperty(value = "评估原因值")
    private String assessReasonValue;

    /**
     * 评估原因文字
     */
    @Excel(name = "评估原因文字")
    @ApiModelProperty(value = "评估原因文字")
    private String assessReasonLabel;

    /**
     * 行业标准
     */
    @Excel(name = "行业标准")
    @ApiModelProperty(value = "行业标准")
    private String industryStandard;

    /**
     * 调查问卷日常生活活动评估总分值
     */
    @ApiModelProperty(value = "调查问卷日常生活活动评估总分值")
    private Integer selfCareAbilityAssessmentScore;
    /**
     * 日常生活活动评估值
     */
    @Excel(name = "日常生活活动评估值")
    @ApiModelProperty(value = "日常生活活动评估值")
    private Integer selfCareAbilityAssessmentValue;

    /**
     * 日常生活活动评估文字
     */
    @Excel(name = "日常生活活动评估文字")
    @ApiModelProperty(value = "日常生活活动评估文字")
    private String selfCareAbilityAssessmentLabel;

    /**
     * 调查问卷感知觉与沟通评估总分值
     */
    private Integer athleticAbilityAssessmentScore;
    /**
     * 感知觉与沟通评估值
     */
    @Excel(name = "感知觉与沟通评估值")
    @ApiModelProperty(value = "感知觉与沟通评估值")
    private Integer athleticAbilityAssessmentValue;

    /**
     * 感知觉与沟通评估文字
     */
    @Excel(name = "感知觉与沟通评估文字")
    @ApiModelProperty(value = "感知觉与沟通评估文字")
    private String athleticAbilityAssessmentLabel;

    /**
     * 调查问卷精神状态评估总分值
     */
    @ApiModelProperty(value = "调查问卷精神状态评估总分值")
    private Integer mentalStateAssessmentScore;

    /**
     * 精神状态评估值
     */
    @Excel(name = "精神状态评估值")
    @ApiModelProperty(value = "精神状态评估值")
    private Integer mentalStateAssessmentValue;

    /**
     * 精神状态评估文字
     */
    @Excel(name = "精神状态评估文字")
    @ApiModelProperty(value = "精神状态评估文字")
    private String mentalStateAssessmentLabel;

    /**
     * 调查问卷感知觉与社会参与评估总分值
     */
    @ApiModelProperty(value = "调查问卷感知觉与社会参与评估总分值")
    private Integer perceptionSocialEngagementAssessmentScore;

    /**
     * 感知觉与社会参与评估值
     */
    @Excel(name = "感知觉与社会参与评估值")
    @ApiModelProperty(value = "感知觉与社会参与评估值")
    private Integer perceptionSocialEngagementAssessmentValue;

    /**
     * 感知觉与社会参与评估文字
     */
    @Excel(name = "感知觉与社会参与评估文字")
    @ApiModelProperty(value = "感知觉与社会参与评估文字")
    private String perceptionSocialEngagementAssessmentLabel;

    /**
     * 老年人能力初步评估等级
     */
    @Excel(name = "老年人能力初步评估等级")
    @ApiModelProperty(value = "老年人能力初步评估等级")
    private String preliminaryAssessmentLevel;
    @ApiModelProperty(value = "老年人能力初步评估等级文字")
    private String preliminaryAssessmentLevelLabel;

    /**
     * 老人能力最终等级
     */
    @Excel(name = "老人能力最终等级")
    @ApiModelProperty(value = "老人能力最终等级")
    private Integer elderlyAbilityFinalLevel;
    @ApiModelProperty(value = "老人能力最终等级文字")
    private String elderlyAbilityFinalLevelLabel;

    /**
     * 评估人id
     */
    @Excel(name = "评估人id")
    @ApiModelProperty(value = "评估人id")
    private String assessorId;

    /**
     * 评估意见
     */
    @Excel(name = "评估意见")
    @ApiModelProperty(value = "评估意见")
    private String evaluationOpinion;

    /**
     * 评估时间
     */
    @Excel(name = "评估时间")
    @ApiModelProperty(value = "评估时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date evaluationTime;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    @ApiModelProperty(value = "老人名称")
    private String userName;
    @ApiModelProperty(value = "老人性别")
    private String sex;
    private String sexStr;
    @ApiModelProperty(value = "老人出生年月日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateBirth;
    @ApiModelProperty(value = "老人头像")
    private String img;
    @ApiModelProperty(value = "评估人名称")
    private String assessorName;


    @ApiModelProperty(value = "复核是否有值")
    private Boolean flag;
    /**
     * 复核日常生活活动评估值
     */
    @Excel(name = "复核日常生活活动评估值")
    @ApiModelProperty(value = "复核日常生活活动评估值")
    private Long reasonSelfCareAbilityAssessmentValue;

    /**
     * 复核日常生活活动评估文字
     */
    @Excel(name = "复核日常生活活动评估文字")
    @ApiModelProperty(value = "复核日常生活活动评估文字")
    private String reasonSelfCareAbilityAssessmentLabel;

    /**
     * 复核卷感知觉与沟通评估值
     */
    @Excel(name = "复核卷感知觉与沟通评估值")
    @ApiModelProperty(value = "复核卷感知觉与沟通评估值")
    private Long reasonAthleticAbilityAssessmentValue;

    /**
     * 复核卷感知觉与沟通评估文字
     */
    @Excel(name = "复核卷感知觉与沟通评估文字")
    @ApiModelProperty(value = "复核卷感知觉与沟通评估文字")
    private String reasonAthleticAbilityAssessmentLabel;

    /**
     * 复核精神状态评估值
     */
    @Excel(name = "复核精神状态评估值")
    @ApiModelProperty(value = "复核精神状态评估值")
    private Long reasonMentalStateAssessmentValue;

    /**
     * 复核精神状态评估文字
     */
    @Excel(name = "复核精神状态评估文字")
    @ApiModelProperty(value = "复核精神状态评估文字")
    private String reasonMentalStateAssessmentLabel;

    /**
     * 复核感知觉与社会参与评估值
     */
    @Excel(name = "复核感知觉与社会参与评估值")
    @ApiModelProperty(value = "复核感知觉与社会参与评估值")
    private Long reasonPerceptionSocialEngagementAssessmentValue;

    /**
     * 感知觉与社会参与评估文字
     */
    @Excel(name = "复核感知觉与社会参与评估文字")
    @ApiModelProperty(value = "复核感知觉与社会参与评估文字")
    private String reasonPerceptionSocialEngagementAssessmentLabel;

    /**
     * 复核原因
     */
    @Excel(name = "复核原因")
    @ApiModelProperty(value = "复核原因")
    private String reasonReview;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAssessReasonValue() {
        return assessReasonValue;
    }

    public void setAssessReasonValue(String assessReasonValue) {
        this.assessReasonValue = assessReasonValue;
    }

    public String getAssessReasonLabel() {
        if (null == this.assessReasonValue) {
            return this.assessReasonLabel;
        }
        return DictUtils.selectDictLabel("assess_reason", this.assessReasonValue);
    }

    public void setAssessReasonLabel(String assessReasonLabel) {
        this.assessReasonLabel = assessReasonLabel;
    }

    public String getIndustryStandard() {
        return industryStandard;
    }

    public void setIndustryStandard(String industryStandard) {
        this.industryStandard = industryStandard;
    }

    public Integer getSelfCareAbilityAssessmentValue() {
        return selfCareAbilityAssessmentValue;
    }

    public void setSelfCareAbilityAssessmentValue(Integer selfCareAbilityAssessmentValue) {
        this.selfCareAbilityAssessmentValue = selfCareAbilityAssessmentValue;
    }

    public String getSelfCareAbilityAssessmentLabel() {
        if (null == this.selfCareAbilityAssessmentValue) {
            return this.selfCareAbilityAssessmentLabel;
        }
        return DictUtils.selectDictLabel("class_type", String.valueOf(this.selfCareAbilityAssessmentValue));
    }

    public void setSelfCareAbilityAssessmentLabel(String selfCareAbilityAssessmentLabel) {
        this.selfCareAbilityAssessmentLabel = selfCareAbilityAssessmentLabel;
    }

    public Integer getAthleticAbilityAssessmentValue() {
        return athleticAbilityAssessmentValue;
    }

    public void setAthleticAbilityAssessmentValue(Integer athleticAbilityAssessmentValue) {
        this.athleticAbilityAssessmentValue = athleticAbilityAssessmentValue;
    }

    public String getAthleticAbilityAssessmentLabel() {
        if (null == this.athleticAbilityAssessmentValue) {
            return this.athleticAbilityAssessmentLabel;
        }
        return DictUtils.selectDictLabel("class_type", String.valueOf(this.athleticAbilityAssessmentValue));
    }

    public void setAthleticAbilityAssessmentLabel(String athleticAbilityAssessmentLabel) {
        this.athleticAbilityAssessmentLabel = athleticAbilityAssessmentLabel;
    }

    public Integer getMentalStateAssessmentValue() {
        return mentalStateAssessmentValue;
    }

    public void setMentalStateAssessmentValue(Integer mentalStateAssessmentValue) {
        this.mentalStateAssessmentValue = mentalStateAssessmentValue;
    }

    public String getMentalStateAssessmentLabel() {
        if (null == this.mentalStateAssessmentValue) {
            return this.mentalStateAssessmentLabel;
        }
        return DictUtils.selectDictLabel("class_type", String.valueOf(this.mentalStateAssessmentValue));
    }

    public void setMentalStateAssessmentLabel(String mentalStateAssessmentLabel) {
        this.mentalStateAssessmentLabel = mentalStateAssessmentLabel;
    }

    public Integer getPerceptionSocialEngagementAssessmentValue() {
        return perceptionSocialEngagementAssessmentValue;
    }

    public void setPerceptionSocialEngagementAssessmentValue(Integer perceptionSocialEngagementAssessmentValue) {
        this.perceptionSocialEngagementAssessmentValue = perceptionSocialEngagementAssessmentValue;
    }

    public String getPerceptionSocialEngagementAssessmentLabel() {
        if (null == this.perceptionSocialEngagementAssessmentValue) {
            return this.perceptionSocialEngagementAssessmentLabel;
        }
        return DictUtils.selectDictLabel("class_type", String.valueOf(this.perceptionSocialEngagementAssessmentValue));
    }

    public void setPerceptionSocialEngagementAssessmentLabel(String perceptionSocialEngagementAssessmentLabel) {
        this.perceptionSocialEngagementAssessmentLabel = perceptionSocialEngagementAssessmentLabel;
    }

    public String getPreliminaryAssessmentLevel() {
        return preliminaryAssessmentLevel;
    }

    public void setPreliminaryAssessmentLevel(String preliminaryAssessmentLevel) {
        this.preliminaryAssessmentLevel = preliminaryAssessmentLevel;
    }

    public Integer getElderlyAbilityFinalLevel() {
        return elderlyAbilityFinalLevel;
    }

    public void setElderlyAbilityFinalLevel(Integer elderlyAbilityFinalLevel) {
        this.elderlyAbilityFinalLevel = elderlyAbilityFinalLevel;
    }

    public String getAssessorId() {
        return assessorId;
    }

    public void setAssessorId(String assessorId) {
        this.assessorId = assessorId;
    }

    public String getEvaluationOpinion() {
        return evaluationOpinion;
    }

    public void setEvaluationOpinion(String evaluationOpinion) {
        this.evaluationOpinion = evaluationOpinion;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public Date getEvaluationTime() {
        return evaluationTime;
    }

    public void setEvaluationTime(Date evaluationTime) {
        this.evaluationTime = evaluationTime;
    }

    public Integer getSelfCareAbilityAssessmentScore() {
        return selfCareAbilityAssessmentScore;
    }

    public void setSelfCareAbilityAssessmentScore(Integer selfCareAbilityAssessmentScore) {
        this.selfCareAbilityAssessmentScore = selfCareAbilityAssessmentScore;
    }

    public Integer getAthleticAbilityAssessmentScore() {
        return athleticAbilityAssessmentScore;
    }

    public void setAthleticAbilityAssessmentScore(Integer athleticAbilityAssessmentScore) {
        this.athleticAbilityAssessmentScore = athleticAbilityAssessmentScore;
    }

    public Integer getMentalStateAssessmentScore() {
        return mentalStateAssessmentScore;
    }

    public void setMentalStateAssessmentScore(Integer mentalStateAssessmentScore) {
        this.mentalStateAssessmentScore = mentalStateAssessmentScore;
    }

    public Integer getPerceptionSocialEngagementAssessmentScore() {
        return perceptionSocialEngagementAssessmentScore;
    }

    public void setPerceptionSocialEngagementAssessmentScore(Integer perceptionSocialEngagementAssessmentScore) {
        this.perceptionSocialEngagementAssessmentScore = perceptionSocialEngagementAssessmentScore;
    }


    public String getPreliminaryAssessmentLevelLabel() {
        if (null == this.preliminaryAssessmentLevel) {
            return this.preliminaryAssessmentLevelLabel;
        }
        return DictUtils.selectDictLabel("assessment_type", String.valueOf(this.preliminaryAssessmentLevel));
    }

    public void setPreliminaryAssessmentLevelLabel(String preliminaryAssessmentLevelLabel) {
        this.preliminaryAssessmentLevelLabel = preliminaryAssessmentLevelLabel;
    }

    public String getElderlyAbilityFinalLevelLabel() {
        if (null == this.elderlyAbilityFinalLevel) {
            return this.elderlyAbilityFinalLevelLabel;
        }
        return DictUtils.selectDictLabel("assessment_type", String.valueOf(this.elderlyAbilityFinalLevel));
    }

    public void setElderlyAbilityFinalLevelLabel(String elderlyAbilityFinalLevelLabel) {
        this.elderlyAbilityFinalLevelLabel = elderlyAbilityFinalLevelLabel;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getDateBirth() {
        return dateBirth;
    }

    public void setDateBirth(Date dateBirth) {
        this.dateBirth = dateBirth;
    }

    public String getAssessorName() {
        return assessorName;
    }

    public void setAssessorName(String assessorName) {
        this.assessorName = assessorName;
    }

    public String getSexStr() {
        if (StringUtils.isBlank(this.sex)) {
            return this.sex;
        }
        return DictUtils.selectDictLabel("sys_user_sex", this.sex);
    }

    public void setSexStr(String sexStr) {
        this.sexStr = sexStr;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }


    public Boolean getFlag() {
        return flag;
    }

    public void setFlag(Boolean flag) {
        this.flag = flag;
    }

    public Long getReasonSelfCareAbilityAssessmentValue() {
        return reasonSelfCareAbilityAssessmentValue;
    }

    public void setReasonSelfCareAbilityAssessmentValue(Long reasonSelfCareAbilityAssessmentValue) {
        this.reasonSelfCareAbilityAssessmentValue = reasonSelfCareAbilityAssessmentValue;
    }

    public String getReasonSelfCareAbilityAssessmentLabel() {
        if (null == this.reasonSelfCareAbilityAssessmentValue) {
            return reasonSelfCareAbilityAssessmentLabel;
        }
        return DictUtils.selectDictLabel("class_type", String.valueOf(this.reasonSelfCareAbilityAssessmentValue));
    }

    public void setReasonSelfCareAbilityAssessmentLabel(String reasonSelfCareAbilityAssessmentLabel) {
        this.reasonSelfCareAbilityAssessmentLabel = reasonSelfCareAbilityAssessmentLabel;
    }

    public Long getReasonAthleticAbilityAssessmentValue() {
        return reasonAthleticAbilityAssessmentValue;
    }

    public void setReasonAthleticAbilityAssessmentValue(Long reasonAthleticAbilityAssessmentValue) {
        this.reasonAthleticAbilityAssessmentValue = reasonAthleticAbilityAssessmentValue;
    }

    public String getReasonAthleticAbilityAssessmentLabel() {
        if (null == this.reasonAthleticAbilityAssessmentValue) {
            return reasonAthleticAbilityAssessmentLabel;
        }
        return DictUtils.selectDictLabel("class_type", String.valueOf(this.reasonAthleticAbilityAssessmentValue));
    }

    public void setReasonAthleticAbilityAssessmentLabel(String reasonAthleticAbilityAssessmentLabel) {
        this.reasonAthleticAbilityAssessmentLabel = reasonAthleticAbilityAssessmentLabel;
    }

    public Long getReasonMentalStateAssessmentValue() {
        return reasonMentalStateAssessmentValue;
    }

    public void setReasonMentalStateAssessmentValue(Long reasonMentalStateAssessmentValue) {
        this.reasonMentalStateAssessmentValue = reasonMentalStateAssessmentValue;
    }

    public String getReasonMentalStateAssessmentLabel() {
        if (null == this.reasonMentalStateAssessmentValue) {
            return reasonMentalStateAssessmentLabel;
        }
        return DictUtils.selectDictLabel("class_type", String.valueOf(this.reasonMentalStateAssessmentValue));
    }

    public void setReasonMentalStateAssessmentLabel(String reasonMentalStateAssessmentLabel) {
        this.reasonMentalStateAssessmentLabel = reasonMentalStateAssessmentLabel;
    }

    public Long getReasonPerceptionSocialEngagementAssessmentValue() {
        return reasonPerceptionSocialEngagementAssessmentValue;
    }

    public void setReasonPerceptionSocialEngagementAssessmentValue(Long reasonPerceptionSocialEngagementAssessmentValue) {
        this.reasonPerceptionSocialEngagementAssessmentValue = reasonPerceptionSocialEngagementAssessmentValue;
    }

    public String getReasonPerceptionSocialEngagementAssessmentLabel() {
        if (null == this.reasonPerceptionSocialEngagementAssessmentValue) {
            return reasonPerceptionSocialEngagementAssessmentLabel;
        }
        return DictUtils.selectDictLabel("class_type", String.valueOf(this.reasonPerceptionSocialEngagementAssessmentValue));
    }

    public void setReasonPerceptionSocialEngagementAssessmentLabel(String reasonPerceptionSocialEngagementAssessmentLabel) {
        this.reasonPerceptionSocialEngagementAssessmentLabel = reasonPerceptionSocialEngagementAssessmentLabel;
    }

    public String getReasonReview() {
        return reasonReview;
    }

    public void setReasonReview(String reasonReview) {
        this.reasonReview = reasonReview;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("assessReasonValue", getAssessReasonValue())
                .append("assessReasonLabel", getAssessReasonLabel())
                .append("industryStandard", getIndustryStandard())
                .append("selfCareAbilityAssessmentValue", getSelfCareAbilityAssessmentValue())
                .append("selfCareAbilityAssessmentLabel", getSelfCareAbilityAssessmentLabel())
                .append("athleticAbilityAssessmentValue", getAthleticAbilityAssessmentValue())
                .append("athleticAbilityAssessmentLabel", getAthleticAbilityAssessmentLabel())
                .append("mentalStateAssessmentValue", getMentalStateAssessmentValue())
                .append("mentalStateAssessmentLabel", getMentalStateAssessmentLabel())
                .append("perceptionSocialEngagementAssessmentValue", getPerceptionSocialEngagementAssessmentValue())
                .append("perceptionSocialEngagementAssessmentLabel", getPerceptionSocialEngagementAssessmentLabel())
                .append("preliminaryAssessmentLevel", getPreliminaryAssessmentLevel())
                .append("elderlyAbilityFinalLevel", getElderlyAbilityFinalLevel())
                .append("assessorId", getAssessorId())
                .append("evaluationOpinion", getEvaluationOpinion())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("evaluationTime", getEvaluationTime())
                .append("remark", getRemark())
                .toString();
    }
}
