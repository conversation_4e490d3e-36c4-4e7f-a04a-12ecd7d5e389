package com.ruoyi.homecare.securityguard.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.homecare.securityguard.service.impl.OwonParse;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;


/**
 * 拉绳传感器报警信息对象 t_security_guard_sensor
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Data
@ApiModel("拉绳传感器报警信息对象")
@TableName("t_security_guard_sensor")
public class SecurityGuardSensor {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /** $column.columnComment */
    private Long id;

    /**
     * 设备名称
     */
//    @Excel(name = "设备名称")
    private String name;

    /**
     * 设备分区类型
     */
//    @Excel(name = "设备分区类型")
    private String zoneType;

    /**
     * 设备分区号
     */
//    @Excel(name = "设备分区号")
    private Long zoneId;

    /**
     * ep设备的 ep 值
     */
//    @Excel(name = "ep设备的 ep 值")
    private String ep;

    /**
     * ep设备的 MAC 值
     */
//    @Excel(name = "ep设备的 MAC 值")
    private String ieee;

    /**
     * 状态值
     */
//    @Excel(name = "状态值")
    private Long status;


    @Excel(name = "老人姓名")
    @TableField(exist = false)
    private String peopleName;

    @Excel(name = "身份证号")
    @TableField(exist = false)
    private String idCardNum;

    @Excel(name = "手机号")
    @TableField(exist = false)
    private String phone;

    @Excel(name = "设备名称")
    @TableField(exist = false)
    private String deviceName;

    @Excel(name = "设备型号")
    @TableField(exist = false)
    private String modelNumber;

    @TableField(exist = false)
    @Excel(name = "状态值")
    private String statusStr;

    /**
     * mac
     */
//    @Excel(name = "mac")
    private String mac;

    @Excel(name = "关联标识")
    private String contextid;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private Integer devType;

    public String getContextid() {
        return contextid;
    }

    public void setContextid(String contextid) {
        this.contextid = contextid;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public Integer getDevType() {
        return devType;
    }

    public void setDevType(Integer devType) {
        this.devType = devType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getZoneType() {
        return zoneType;
    }

    public void setZoneType(String zoneType) {
        this.zoneType = zoneType;
    }

    public Long getZoneId() {
        return zoneId;
    }

    public void setZoneId(Long zoneId) {
        this.zoneId = zoneId;
    }

    public String getEp() {
        return ep;
    }

    public void setEp(String ep) {
        this.ep = ep;
    }

    public String getIeee() {
        return ieee;
    }

    public void setIeee(String ieee) {
        this.ieee = ieee;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.statusStr = OwonParse.parseStatus(status.intValue());

        this.status = status;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

}
