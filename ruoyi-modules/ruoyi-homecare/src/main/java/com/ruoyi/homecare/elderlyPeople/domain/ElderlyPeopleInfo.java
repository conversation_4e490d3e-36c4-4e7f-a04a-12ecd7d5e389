package com.ruoyi.homecare.elderlyPeople.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.homecare.utils.DictUtils;
import io.seata.common.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 老人基础信息对象 t_home_elderly_people_info
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@ApiModel(value = "老人基础信息")
@TableName("t_home_elderly_people_info")
public class ElderlyPeopleInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    private String id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别
     */
    @Excel(name = "性别")
    @ApiModelProperty(value = "性别")
    private String sex;
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty(value = "性别label")
    private String sexStr;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    @ApiModelProperty(value = "手机号")
    @NotNull(message = "手机号必填！")
    private String phone;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dateBirth;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    @ApiModelProperty(value = "年龄")
    private int age;

    /**
     * 民族
     */
    @Excel(name = "民族")
    @ApiModelProperty(value = "民族")
    private String nation;
    @ApiModelProperty(value = "民族label")
    private String nationStr;

    /**
     * 婚姻情况
     */
    @Excel(name = "婚姻情况")
    @ApiModelProperty(value = "婚姻情况")
    private String marriageStatus;
    @ApiModelProperty(value = "婚姻情况label")
    private String marriageStatusStr;

    /**
     * 居住情况
     */
    @Excel(name = "居住情况")
    @ApiModelProperty(value = "居住情况")
    private String livingSituation;
    @ApiModelProperty(value = "居住情况label")
    private String livingSituationStr;

    /**
     * 家庭住址
     */
    @Excel(name = "家庭住址")
    @ApiModelProperty(value = "家庭住址")
    private String homeAddress;

    /**
     * 紧急联系人姓名
     */
    @Excel(name = "紧急联系人姓名")
    @ApiModelProperty(value = "紧急联系人姓名")
    private String emergencyContactName;

    /**
     * 紧急联系人手机号
     */
    @Excel(name = "紧急联系人手机号")
    @ApiModelProperty(value = "紧急联系人手机号")
    private String emergencyContactPhone;

    /**
     * 关系
     */
    @Excel(name = "关系")
    @ApiModelProperty(value = "关系")
    private String relation;
    @ApiModelProperty(value = "关系label")
    private String relationStr;

    /**
     * 经济来源
     */
    @Excel(name = "经济来源")
    @ApiModelProperty(value = "经济来源")
    private String economicSources;
    @ApiModelProperty(value = "经济来源label")
    private String economicSourcesStr;

    /**
     * 月收入
     */
    @Excel(name = "月收入")
    @ApiModelProperty(value = "月收入")
    private String monthlyIncome;

    /**
     * 社保号
     */
    @Excel(name = "社保号")
    @ApiModelProperty(value = "社保号")
    private String socialSecurityNo;

    /**
     * 入住状态
     */
    @Excel(name = "入住状态")
    @ApiModelProperty(value = "入住状态")
    private String status;
    @ApiModelProperty(value = "入住状态label")
    private String statusStr;

    /**
     * 头像照片
     */
    @Excel(name = "头像照片")
    @ApiModelProperty(value = "头像照片")
    private String img;

    /**
     * 所属社区id
     */
    @Excel(name = "所属社区id")
    @ApiModelProperty(value = "所属社区id")
    private String serviceStationId;

    /**
     * 所属社区名称
     */
    @Excel(name = "所属社区名称")
    @ApiModelProperty(value = "所属社区名称")
    private String serviceStationLabel;

    /**
     * 护理等级
     */
    @Excel(name = "护理等级")
    @ApiModelProperty(value = "护理等级")
    private String careLevel;

    /**
     * 服务套餐
     */
    @Excel(name = "服务套餐")
    @ApiModelProperty(value = "服务套餐")
    private String serviceComboId;

    /**
     * 失能情况
     */
    @Excel(name = "失能情况")
    @ApiModelProperty(value = "失能情况")
    private String disability;

    /**
     * 失能情况Label
     */
    @Excel(name = "失能情况Label")
    @ApiModelProperty(value = "失能情况Label")
    private String disabilityLabel;

    /**
     * 余额
     */
    @Excel(name = "余额")
    @ApiModelProperty(value = "余额")
    private BigDecimal amount;

    /**
     * 系统用户id
     */
    @Excel(name = "系统用户id")
    @ApiModelProperty(value = "系统用户id")
    private Long sysUserId;

    /**
     * 是否是志愿者
     */
    @Excel(name = "是否是志愿者 0不是，1是")
    @ApiModelProperty(value = "是否是志愿者")
    private String volunteerFlag;


    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getIdCardNum() {
        return idCardNum;
    }

    public void setIdCardNum(String idCardNum) {
        this.idCardNum = idCardNum;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Date getDateBirth() {
        return dateBirth;
    }

    public void setDateBirth(Date dateBirth) {
        this.dateBirth = dateBirth;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getMarriageStatus() {
        return marriageStatus;
    }

    public void setMarriageStatus(String marriageStatus) {
        this.marriageStatus = marriageStatus;
    }

    public String getLivingSituation() {
        return livingSituation;
    }

    public void setLivingSituation(String livingSituation) {
        this.livingSituation = livingSituation;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getEmergencyContactName() {
        return emergencyContactName;
    }

    public void setEmergencyContactName(String emergencyContactName) {
        this.emergencyContactName = emergencyContactName;
    }

    public String getEmergencyContactPhone() {
        return emergencyContactPhone;
    }

    public void setEmergencyContactPhone(String emergencyContactPhone) {
        this.emergencyContactPhone = emergencyContactPhone;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getEconomicSources() {
        return economicSources;
    }

    public void setEconomicSources(String economicSources) {
        this.economicSources = economicSources;
    }

    public String getMonthlyIncome() {
        return monthlyIncome;
    }

    public void setMonthlyIncome(String monthlyIncome) {
        this.monthlyIncome = monthlyIncome;
    }

    public String getSocialSecurityNo() {
        return socialSecurityNo;
    }

    public void setSocialSecurityNo(String socialSecurityNo) {
        this.socialSecurityNo = socialSecurityNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSexStr() {
        if (StringUtils.isBlank(this.sex)) {
            return this.sex;
        }
        return DictUtils.selectDictLabel("sys_user_sex", this.sex);
    }

    public void setSexStr(String sexStr) {
        this.sexStr = sexStr;
    }

    public String getNationStr() {
        if (StringUtils.isBlank(this.nation)) {
            return this.nation;
        }
        return DictUtils.selectDictLabel("nation", this.nation);
    }

    public void setNationStr(String nationStr) {
        this.nationStr = nationStr;
    }

    public String getMarriageStatusStr() {
        if (StringUtils.isBlank(this.marriageStatus)) {
            return this.marriageStatus;
        }
        return DictUtils.selectDictLabel("marriage_state", this.marriageStatus);
    }

    public void setMarriageStatusStr(String marriageStatusStr) {
        this.marriageStatusStr = marriageStatusStr;
    }

    public String getLivingSituationStr() {
        if (StringUtils.isBlank(this.livingSituation)) {
            return this.livingSituation;
        }
        return DictUtils.selectDictLabel("living_situation", this.livingSituation);
    }

    public void setLivingSituationStr(String livingSituationStr) {
        this.livingSituationStr = livingSituationStr;
    }

    public String getRelationStr() {
        if (StringUtils.isBlank(this.relation)) {
            return this.relation;
        }
        return DictUtils.selectDictLabel("relation", this.relation);
    }

    public void setRelationStr(String relationStr) {
        this.relationStr = relationStr;
    }

    public String getEconomicSourcesStr() {
        if (StringUtils.isBlank(this.economicSources)) {
            return this.economicSources;
        }
        return DictUtils.selectDictLabel("economic_sources", this.economicSources);
    }

    public void setEconomicSourcesStr(String economicSourcesStr) {
        this.economicSourcesStr = economicSourcesStr;
    }

    public String getStatusStr() {
        if (StringUtils.isBlank(this.status)) {
            return this.status;
        }
        return DictUtils.selectDictLabel("live_state", this.status);
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        {
            if ("".equals(img) || img == null) {
                this.img = "";// 去除该属性的前后空格并进行非空非null判断
            } else {
                this.img = img;
            }
        }
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public String getServiceStationId() {
        return serviceStationId;
    }

    public void setServiceStationId(String serviceStationId) {
        this.serviceStationId = serviceStationId;
    }

    public String getCareLevel() {
        return careLevel;
    }

    public void setCareLevel(String careLevel) {
        this.careLevel = careLevel;
    }

    public String getServiceComboId() {
        return serviceComboId;
    }

    public void setServiceComboId(String serviceComboId) {
        this.serviceComboId = serviceComboId;
    }

    public String getDisability() {
        return disability;
    }

    public void setDisability(String disability) {
        this.disability = disability;
    }

    public String getDisabilityLabel() {
        if (StringUtils.isEmpty(this.disability)) {
            return "";
        }
        return DictUtils.selectDictLabel("assessment_type", this.disability);
    }

    public void setDisabilityLabel(String disabilityLabel) {
        this.disabilityLabel = disabilityLabel;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(Long sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getServiceStationLabel() {
        return serviceStationLabel;
    }

    public void setServiceStationLabel(String serviceStationLabel) {
        this.serviceStationLabel = serviceStationLabel;
    }

    public String getVolunteerFlag() {
        return volunteerFlag;
    }

    public void setVolunteerFlag(String volunteerFlag) {
        this.volunteerFlag = volunteerFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("sex", getSex())
                .append("idCardNum", getIdCardNum())
                .append("phone", getPhone())
                .append("dateBirth", getDateBirth())
                .append("age", getAge())
                .append("nation", getNation())
                .append("marriageStatus", getMarriageStatus())
                .append("livingSituation", getLivingSituation())
                .append("homeAddress", getHomeAddress())
                .append("emergencyContactName", getEmergencyContactName())
                .append("emergencyContactPhone", getEmergencyContactPhone())
                .append("relation", getRelation())
                .append("economicSources", getEconomicSources())
                .append("monthlyIncome", getMonthlyIncome())
                .append("socialSecurityNo", getSocialSecurityNo())
                .append("status", getStatus())
                .append("img", getImg())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
