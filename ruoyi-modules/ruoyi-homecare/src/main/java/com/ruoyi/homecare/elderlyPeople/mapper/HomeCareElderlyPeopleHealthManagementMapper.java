package com.ruoyi.homecare.elderlyPeople.mapper;

import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthManagement;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 老人健康管理信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Mapper
public interface HomeCareElderlyPeopleHealthManagementMapper {
    /**
     * 查询老人健康管理信息
     *
     * @param id 老人健康管理信息主键
     * @return 老人健康管理信息
     */
    public ElderlyPeopleHealthManagement selectElderlyPeopleHealthManagementById(String id);

    /**
     * 查询老人健康管理信息列表
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 老人健康管理信息集合
     */
    public List<ElderlyPeopleHealthManagement> selectElderlyPeopleHealthManagementList(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement);

    /**
     * 新增老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 结果
     */
    public int insertElderlyPeopleHealthManagement(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement);

    /**
     * 修改老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 结果
     */
    public int updateElderlyPeopleHealthManagement(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement);

    /**
     * 删除老人健康管理信息
     *
     * @param id 老人健康管理信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleHealthManagementById(String id);

    /**
     * 批量删除老人健康管理信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleHealthManagementByIds(String[] ids);
}
