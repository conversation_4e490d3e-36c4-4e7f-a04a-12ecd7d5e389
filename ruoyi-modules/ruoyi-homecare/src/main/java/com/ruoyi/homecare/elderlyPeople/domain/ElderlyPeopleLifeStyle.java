package com.ruoyi.homecare.elderlyPeople.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 老人生活方式信息对象 t_elderly_people_life_style
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@ApiModel(value = "老人生活方式信息")
public class ElderlyPeopleLifeStyle extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 饮食情况
     */
    @Excel(name = "饮食情况")
    @ApiModelProperty(value = "饮食情况")
    private String dietaryStatus;

    /**
     * 口味选择
     */
    @Excel(name = "口味选择")
    @ApiModelProperty(value = "口味选择")
    private String tasteSelection;

    /**
     * 是否吃早餐
     */
    @Excel(name = "是否吃早餐")
    @ApiModelProperty(value = "是否吃早餐")
    private String eatBreakfast;

    /**
     * 是否锻炼
     */
    @Excel(name = "是否锻炼")
    @ApiModelProperty(value = "是否锻炼")
    private String takeExercise;

    /**
     * 每周锻炼次数
     */
    @Excel(name = "每周锻炼次数")
    @ApiModelProperty(value = "每周锻炼次数")
    private String exerciseTimesPerWeek;

    /**
     * 锻炼时间
     */
    @Excel(name = "锻炼时间")
    @ApiModelProperty(value = "锻炼时间")
    private String exerciseTime;

    /**
     * 睡眠质量
     */
    @Excel(name = "睡眠质量")
    @ApiModelProperty(value = "睡眠质量")
    private String sleepQuality;

    /**
     * 睡眠时间
     */
    @Excel(name = "睡眠时间")
    @ApiModelProperty(value = "睡眠时间")
    private String sleepTime;

    /**
     * 熬夜情况
     */
    @Excel(name = "熬夜情况")
    @ApiModelProperty(value = "熬夜情况")
    private String stayUpLateStatus;

    /**
     * 抽烟情况
     */
    @Excel(name = "抽烟情况")
    @ApiModelProperty(value = "抽烟情况")
    private String smokingStatus;

    /**
     * 戒烟年数
     */
    @Excel(name = "戒烟年数")
    @ApiModelProperty(value = "戒烟年数")
    private String quitSmokingYears;

    /**
     * 日吸烟量
     */
    @Excel(name = "日吸烟量")
    @ApiModelProperty(value = "日吸烟量")
    private String numberCigarettesSmokedDaily;

    /**
     * 烟龄
     */
    @Excel(name = "烟龄")
    @ApiModelProperty(value = "烟龄")
    private String smokingTime;

    /**
     * 饮酒情况
     */
    @Excel(name = "饮酒情况")
    @ApiModelProperty(value = "饮酒情况")
    private String drinkingStatus;

    /**
     * 每次酒量
     */
    @Excel(name = "每次酒量")
    @ApiModelProperty(value = "每次酒量")
    private String capacityForLiquor;

    /**
     * 酒龄
     */
    @Excel(name = "酒龄")
    @ApiModelProperty(value = "酒龄")
    private String drinkingTime;

    /**
     * 饮酒种类
     */
    @Excel(name = "饮酒种类")
    @ApiModelProperty(value = "饮酒种类")
    private String drinkingTypes;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 老人基础信息id
     */
    @Excel(name = "老人基础信息id")
    @ApiModelProperty(value = "老人基础信息id")
    private String userId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDietaryStatus() {
        return dietaryStatus;
    }

    public void setDietaryStatus(String dietaryStatus) {
        this.dietaryStatus = dietaryStatus;
    }

    public String getTasteSelection() {
        return tasteSelection;
    }

    public void setTasteSelection(String tasteSelection) {
        this.tasteSelection = tasteSelection;
    }

    public String getEatBreakfast() {
        return eatBreakfast;
    }

    public void setEatBreakfast(String eatBreakfast) {
        this.eatBreakfast = eatBreakfast;
    }

    public String getTakeExercise() {
        return takeExercise;
    }

    public void setTakeExercise(String takeExercise) {
        this.takeExercise = takeExercise;
    }

    public String getExerciseTimesPerWeek() {
        return exerciseTimesPerWeek;
    }

    public void setExerciseTimesPerWeek(String exerciseTimesPerWeek) {
        this.exerciseTimesPerWeek = exerciseTimesPerWeek;
    }

    public String getExerciseTime() {
        return exerciseTime;
    }

    public void setExerciseTime(String exerciseTime) {
        this.exerciseTime = exerciseTime;
    }

    public String getSleepQuality() {
        return sleepQuality;
    }

    public void setSleepQuality(String sleepQuality) {
        this.sleepQuality = sleepQuality;
    }

    public String getSleepTime() {
        return sleepTime;
    }

    public void setSleepTime(String sleepTime) {
        this.sleepTime = sleepTime;
    }

    public String getStayUpLateStatus() {
        return stayUpLateStatus;
    }

    public void setStayUpLateStatus(String stayUpLateStatus) {
        this.stayUpLateStatus = stayUpLateStatus;
    }

    public String getSmokingStatus() {
        return smokingStatus;
    }

    public void setSmokingStatus(String smokingStatus) {
        this.smokingStatus = smokingStatus;
    }

    public String getQuitSmokingYears() {
        return quitSmokingYears;
    }

    public void setQuitSmokingYears(String quitSmokingYears) {
        this.quitSmokingYears = quitSmokingYears;
    }

    public String getNumberCigarettesSmokedDaily() {
        return numberCigarettesSmokedDaily;
    }

    public void setNumberCigarettesSmokedDaily(String numberCigarettesSmokedDaily) {
        this.numberCigarettesSmokedDaily = numberCigarettesSmokedDaily;
    }

    public String getSmokingTime() {
        return smokingTime;
    }

    public void setSmokingTime(String smokingTime) {
        this.smokingTime = smokingTime;
    }

    public String getDrinkingStatus() {
        return drinkingStatus;
    }

    public void setDrinkingStatus(String drinkingStatus) {
        this.drinkingStatus = drinkingStatus;
    }

    public String getCapacityForLiquor() {
        return capacityForLiquor;
    }

    public void setCapacityForLiquor(String capacityForLiquor) {
        this.capacityForLiquor = capacityForLiquor;
    }

    public String getDrinkingTime() {
        return drinkingTime;
    }

    public void setDrinkingTime(String drinkingTime) {
        this.drinkingTime = drinkingTime;
    }

    public String getDrinkingTypes() {
        return drinkingTypes;
    }

    public void setDrinkingTypes(String drinkingTypes) {
        this.drinkingTypes = drinkingTypes;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("dietaryStatus", getDietaryStatus())
                .append("tasteSelection", getTasteSelection())
                .append("eatBreakfast", getEatBreakfast())
                .append("takeExercise", getTakeExercise())
                .append("exerciseTimesPerWeek", getExerciseTimesPerWeek())
                .append("exerciseTime", getExerciseTime())
                .append("sleepQuality", getSleepQuality())
                .append("sleepTime", getSleepTime())
                .append("stayUpLateStatus", getStayUpLateStatus())
                .append("smokingStatus", getSmokingStatus())
                .append("quitSmokingYears", getQuitSmokingYears())
                .append("numberCigarettesSmokedDaily", getNumberCigarettesSmokedDaily())
                .append("smokingTime", getSmokingTime())
                .append("drinkingStatus", getDrinkingStatus())
                .append("capacityForLiquor", getCapacityForLiquor())
                .append("drinkingTime", getDrinkingTime())
                .append("drinkingTypes", getDrinkingTypes())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .append("userId", getUserId())
                .toString();
    }
}
