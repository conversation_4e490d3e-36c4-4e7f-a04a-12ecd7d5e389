package com.ruoyi.homecare.serviceProviders.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.serviceProviders.domain.HomeRegisterServiceProvider;
import com.ruoyi.homecare.serviceProviders.service.IHomeRegisterServiceProviderService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 注册服务商记录Controller
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@RestController
@RequestMapping("/HomeRegisterServiceProvider")
@Api(tags = "服务商PC端-服务商", value = "服务商PC端-服务商")
public class HomeRegisterServiceProviderController extends BaseController {
    @Autowired
    private IHomeRegisterServiceProviderService homeRegisterServiceProviderService;

    /**
     * 查询注册服务商记录列表
     */
    //@RequiresPermissions("serviceProviders:HomeRegisterServiceProvider:list")
    @ApiOperation(value = "注册服务商记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "name", value = "名称", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(name = "auditTime", value = "审核时间", required = false, dataType = "Date"),
            @ApiImplicitParam(name = "state", value = "状态：0未审核，2拒绝", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(name = "selectState", value = "查询状态：0正在使用，1不使用", required = false, dataTypeClass = String.class),
    }
    )
    @ApiIgnore
    public TableDataInfo list(@ApiIgnore HomeRegisterServiceProvider homeRegisterServiceProvider) {
        startPage();
        List<HomeRegisterServiceProvider> list = homeRegisterServiceProviderService.selectHomeRegisterServiceProviderList(homeRegisterServiceProvider);
        return getDataTable(list);
    }


    /**
     * 注册服务商
     */
    @ApiOperation(value = "注册服务商")
    @PostMapping("registerServiceProvider")
    public AjaxResult registerServiceProvider(@RequestBody HomeRegisterServiceProvider homeRegisterServiceProvider) {
        return toAjax(homeRegisterServiceProviderService.insertHomeRegisterServiceProvider(homeRegisterServiceProvider));
    }


    /**
     * 再次提交注册服务商
     */
    @ApiOperation(value = "再次提交注册服务商")
    @PostMapping("againRegisterServiceProvider")
    public AjaxResult againRegisterServiceProvider(@RequestBody HomeRegisterServiceProvider homeRegisterServiceProvider) {
        return toAjax(homeRegisterServiceProviderService.againRegisterServiceProvider(homeRegisterServiceProvider));
    }


    /**
     * 获取注册服务商详细信息
     */
    @GetMapping("getServiceProvidersInfoBySysUserId")
    @ApiOperation(value = "获取注册服务商详细信息")
    public AjaxResult getServiceProvidersInfoBySysUserId() {
        return AjaxResult.success(homeRegisterServiceProviderService.getServiceProvidersInfoBySysUserId(1L));
    }


    /**
     * 导出注册服务商记录列表
     */
    //@RequiresPermissions("serviceProviders:HomeRegisterServiceProvider:export")
    @Log(platform = "2", title = "注册服务商记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, HomeRegisterServiceProvider homeRegisterServiceProvider) {
        List<HomeRegisterServiceProvider> list = homeRegisterServiceProviderService.selectHomeRegisterServiceProviderList(homeRegisterServiceProvider);
        ExcelUtil<HomeRegisterServiceProvider> util = new ExcelUtil<HomeRegisterServiceProvider>(HomeRegisterServiceProvider.class);
        util.exportExcel(response, list, "注册服务商记录数据");
    }

    /**
     * 获取注册服务商记录详细信息
     */
    //@RequiresPermissions("serviceProviders:HomeRegisterServiceProvider:query")
    @GetMapping(value = "/{id}")
    @ApiIgnore
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(homeRegisterServiceProviderService.selectHomeRegisterServiceProviderById(id));
    }

    /**
     * 新增注册服务商记录
     */
    //@RequiresPermissions("serviceProviders:HomeRegisterServiceProvider:add")
    @Log(platform = "2", title = "注册服务商记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody HomeRegisterServiceProvider homeRegisterServiceProvider) {
        return toAjax(homeRegisterServiceProviderService.insertHomeRegisterServiceProvider(homeRegisterServiceProvider));
    }

    /**
     * 修改注册服务商记录
     */
    //@RequiresPermissions("serviceProviders:HomeRegisterServiceProvider:edit")
    @Log(platform = "2", title = "注册服务商记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody HomeRegisterServiceProvider homeRegisterServiceProvider) {
        return toAjax(homeRegisterServiceProviderService.updateHomeRegisterServiceProvider(homeRegisterServiceProvider));
    }

    /**
     * 删除注册服务商记录
     */
    //@RequiresPermissions("serviceProviders:HomeRegisterServiceProvider:remove")
    @Log(platform = "2", title = "注册服务商记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiIgnore
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeRegisterServiceProviderService.deleteHomeRegisterServiceProviderByIds(ids));
    }
}
