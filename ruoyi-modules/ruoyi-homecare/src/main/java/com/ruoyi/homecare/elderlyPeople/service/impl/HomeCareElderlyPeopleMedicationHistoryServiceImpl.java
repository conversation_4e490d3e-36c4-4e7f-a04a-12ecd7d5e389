package com.ruoyi.homecare.elderlyPeople.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleMedicationHistory;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleMedicationHistoryMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleMedicationHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 老人用药史信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Service
public class HomeCareElderlyPeopleMedicationHistoryServiceImpl implements IHomeCareElderlyPeopleMedicationHistoryService {
    @Autowired
    private HomeCareElderlyPeopleMedicationHistoryMapper elderlyPeopleMedicationHistoryMapper;

    /**
     * 查询老人用药史信息
     *
     * @param id 老人用药史信息主键
     * @return 老人用药史信息
     */
    @Override
    public ElderlyPeopleMedicationHistory selectElderlyPeopleMedicationHistoryById(String id) {
        return elderlyPeopleMedicationHistoryMapper.selectElderlyPeopleMedicationHistoryById(id);
    }

    /**
     * 查询老人用药史信息列表
     *
     * @param elderlyPeopleMedicationHistory 老人用药史信息
     * @return 老人用药史信息
     */
    @Override
    public List<ElderlyPeopleMedicationHistory> selectElderlyPeopleMedicationHistoryList(ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory) {
        return elderlyPeopleMedicationHistoryMapper.selectElderlyPeopleMedicationHistoryList(elderlyPeopleMedicationHistory);
    }

    /**
     * 新增老人用药史信息
     *
     * @param elderlyPeopleMedicationHistory 老人用药史信息
     * @return 结果
     */
    @Override
    public int insertElderlyPeopleMedicationHistory(ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory) {
        elderlyPeopleMedicationHistory.setCreateTime(DateUtils.getNowDate());
        String id = IdUtils.fastSimpleUUID();
        elderlyPeopleMedicationHistory.setId(id);
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleMedicationHistory.setCreateBy(String.valueOf(userId));
        return elderlyPeopleMedicationHistoryMapper.insertElderlyPeopleMedicationHistory(elderlyPeopleMedicationHistory);
    }

    /**
     * 修改老人用药史信息
     *
     * @param elderlyPeopleMedicationHistory 老人用药史信息
     * @return 结果
     */
    @Override
    public int updateElderlyPeopleMedicationHistory(ElderlyPeopleMedicationHistory elderlyPeopleMedicationHistory) {
        elderlyPeopleMedicationHistory.setUpdateTime(DateUtils.getNowDate());
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleMedicationHistory.setUpdateBy(String.valueOf(userId));
        return elderlyPeopleMedicationHistoryMapper.updateElderlyPeopleMedicationHistory(elderlyPeopleMedicationHistory);
    }

    /**
     * 批量删除老人用药史信息
     *
     * @param ids 需要删除的老人用药史信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleMedicationHistoryByIds(String[] ids) {
        return elderlyPeopleMedicationHistoryMapper.deleteElderlyPeopleMedicationHistoryByIds(ids);
    }

    /**
     * 删除老人用药史信息信息
     *
     * @param id 老人用药史信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleMedicationHistoryById(String id) {
        return elderlyPeopleMedicationHistoryMapper.deleteElderlyPeopleMedicationHistoryById(id);
    }
}
