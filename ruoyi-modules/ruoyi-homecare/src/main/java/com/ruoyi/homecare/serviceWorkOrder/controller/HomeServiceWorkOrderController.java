package com.ruoyi.homecare.serviceWorkOrder.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.log.enums.OperatorType;
import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import com.ruoyi.homecare.serviceWorkOrder.param.HomeServiceWorkOrderParam;
import com.ruoyi.homecare.serviceWorkOrder.service.HomeServiceWorkOrderService;
import com.ruoyi.homecare.serviceWorkOrder.vo.HomeServiceWorkOrderRequestVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_service_work_order控制器
 * @date 2022-07-18
 */
@Slf4j
@RestController
@RequestMapping("/homeServiceWorkOrder")
@Api(tags = "服务人员工单模块")
public class HomeServiceWorkOrderController extends BaseController {

    @Autowired
    private HomeServiceWorkOrderService homeServiceWorkOrderService;

    @ApiOperation("获取工单list")
    @PostMapping("/getWorkOrderList")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = true, dataTypeClass = String.class),
    })
    public TableDataInfo<HomeOrderServiceWork> getWorkOrderList(@ApiIgnore HomeOrderServiceWork homeOrderServiceWork) {
        startPage();
        List<HomeOrderServiceWork> list = homeServiceWorkOrderService.getWorkOrderList(homeOrderServiceWork);
        return getDataTable(list);
    }


    /**
     * 指派服务人员  并 生成工单
     */
    @PostMapping("/assignWorker")
    @ApiOperation(value = "指派服务人员  并 生成工单")
    public Object assignWorker(@RequestBody HomeServiceWorkOrderParam homeServiceWorkOrderParam) {
        int i = homeServiceWorkOrderService.assignWorker(homeServiceWorkOrderParam);
        return toAjax(i);
    }


}
