package com.ruoyi.homecare.serviceProviders.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.serviceProviders.mapper.HomeGoodsServiceProviderIndexMapper;
import com.ruoyi.homecare.serviceProviders.domain.HomeGoodsServiceProviderIndex;
import com.ruoyi.homecare.serviceProviders.service.IHomeGoodsServiceProviderIndexService;

/**
 * 商品和服务商关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
@Service
public class HomeGoodsServiceProviderIndexServiceImpl implements IHomeGoodsServiceProviderIndexService {
    @Autowired
    private HomeGoodsServiceProviderIndexMapper homeGoodsServiceProviderIndexMapper;

    /**
     * 查询商品和服务商关联
     *
     * @param id 商品和服务商关联主键
     * @return 商品和服务商关联
     */
    @Override
    public HomeGoodsServiceProviderIndex selectHomeGoodsServiceProviderIndexById(Long id) {
        return homeGoodsServiceProviderIndexMapper.selectHomeGoodsServiceProviderIndexById(id);
    }

    /**
     * 查询商品和服务商关联列表
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 商品和服务商关联
     */
    @Override
    public List<HomeGoodsServiceProviderIndex> selectHomeGoodsServiceProviderIndexList(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex) {
        return homeGoodsServiceProviderIndexMapper.selectHomeGoodsServiceProviderIndexList(homeGoodsServiceProviderIndex);
    }

    /**
     * 新增商品和服务商关联
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 结果
     */
    @Override
    public int insertHomeGoodsServiceProviderIndex(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex) {
        homeGoodsServiceProviderIndex.setCreateTime(DateUtils.getNowDate());
        return homeGoodsServiceProviderIndexMapper.insertHomeGoodsServiceProviderIndex(homeGoodsServiceProviderIndex);
    }

    /**
     * 修改商品和服务商关联
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 结果
     */
    @Override
    public int updateHomeGoodsServiceProviderIndex(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex) {
        homeGoodsServiceProviderIndex.setUpdateTime(DateUtils.getNowDate());
        return homeGoodsServiceProviderIndexMapper.updateHomeGoodsServiceProviderIndex(homeGoodsServiceProviderIndex);
    }

    /**
     * 批量删除商品和服务商关联
     *
     * @param ids 需要删除的商品和服务商关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeGoodsServiceProviderIndexByIds(Long[] ids) {
        return homeGoodsServiceProviderIndexMapper.deleteHomeGoodsServiceProviderIndexByIds(ids);
    }

    /**
     * 删除商品和服务商关联信息
     *
     * @param id 商品和服务商关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeGoodsServiceProviderIndexById(Long id) {
        return homeGoodsServiceProviderIndexMapper.deleteHomeGoodsServiceProviderIndexById(id);
    }
}
