package com.ruoyi.homecare.securityguard.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardDevciePeopleInfo;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardDevciePeopleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 老人与设备关联信息Controller
 *
 * <AUTHOR>
 * @date 2023-02-06
 */
@RestController
@RequestMapping("/devicePeople")
public class SecurityGuardDevciePeopleInfoController extends BaseController {
    @Autowired
    private ISecurityGuardDevciePeopleInfoService securityGuardDevciePeopleInfoService;

    /**
     * 查询老人与设备关联信息列表
     */
    @RequiresPermissions("securityguard:devicePeople:list")
    @GetMapping("/list")
    public TableDataInfo list(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo) {
        startPage();
        List<SecurityGuardDevciePeopleInfo> list = securityGuardDevciePeopleInfoService.selectSecurityGuardDevciePeopleInfoList(securityGuardDevciePeopleInfo);
        return getDataTable(list);
    }

    /**
     * 导出老人与设备关联信息列表
     */
    @RequiresPermissions("securityguard:devicePeople:export")
    @Log(title = "老人与设备关联信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo) {
        List<SecurityGuardDevciePeopleInfo> list = securityGuardDevciePeopleInfoService.selectSecurityGuardDevciePeopleInfoList(securityGuardDevciePeopleInfo);
        ExcelUtil<SecurityGuardDevciePeopleInfo> util = new ExcelUtil<SecurityGuardDevciePeopleInfo>(SecurityGuardDevciePeopleInfo.class);
        util.exportExcel(response, list, "老人与设备关联信息数据");
    }

    /**
     * 获取老人与设备关联信息详细信息
     */
    @RequiresPermissions("securityguard:devicePeople:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardDevciePeopleInfoService.selectSecurityGuardDevciePeopleInfoById(id));
    }

    /**
     * 新增老人与设备关联信息
     */
    @RequiresPermissions("securityguard:devicePeople:add")
    @Log(title = "老人与设备关联信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo) {
        return toAjax(securityGuardDevciePeopleInfoService.insertSecurityGuardDevciePeopleInfo(securityGuardDevciePeopleInfo));
    }

    /**
     * 修改老人与设备关联信息
     */
    @RequiresPermissions("securityguard:devicePeople:edit")
    @Log(title = "老人与设备关联信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo) {
        return toAjax(securityGuardDevciePeopleInfoService.updateSecurityGuardDevciePeopleInfo(securityGuardDevciePeopleInfo));
    }

    /**
     * 删除老人与设备关联信息
     */
    @RequiresPermissions("securityguard:devicePeople:remove")
    @Log(title = "老人与设备关联信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardDevciePeopleInfoService.deleteSecurityGuardDevciePeopleInfoByIds(ids));
    }
}
