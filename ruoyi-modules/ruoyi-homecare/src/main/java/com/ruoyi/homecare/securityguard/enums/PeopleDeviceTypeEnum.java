package com.ruoyi.homecare.securityguard.enums;

/**
 * 发放类型
 *
 * <AUTHOR>
 */
public enum PeopleDeviceTypeEnum {

    免费("1", "免费"),
    购买("2", "购买");

    String value;
    String description;

    PeopleDeviceTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static PeopleDeviceTypeEnum getByValue(String type) {
        if (type == null) {
            return null;
        }
        for (PeopleDeviceTypeEnum deviceTypeEnum : PeopleDeviceTypeEnum.values()) {
            if (deviceTypeEnum.value.equals(type)) {
                return deviceTypeEnum;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
