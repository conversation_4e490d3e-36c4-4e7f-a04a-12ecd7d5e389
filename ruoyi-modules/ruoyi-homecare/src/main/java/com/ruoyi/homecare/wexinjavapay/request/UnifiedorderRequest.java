package com.ruoyi.homecare.wexinjavapay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ClassName MiniAppPayRequest01
 * @Description 小程序统一下单参数
 * <AUTHOR>
 * @Date 2022/7/7 11:49
 */
@ApiModel(value = "小程序统一下单请求参数")
@Data
public class UnifiedorderRequest {

    @NotBlank(message = "description不能为空！")
    @ApiModelProperty(value = "商品简单描述", required = true)
    private String description;

    @ApiModelProperty(value = "商户订单号", required = true)
    private String outTradeNo;

    @NotNull(message = "total不能为空！")
    @ApiModelProperty(value = "订单总金额，单位为分", required = true)
    private Integer total;

    @NotBlank(message = "ip不能为空！")
    @ApiModelProperty(value = "终端IP APP和网页支付提交用户端ip，Native支付填调用微信支付API的机器IP", required = true)
    private String spbillCreateIp;

    @ApiModelProperty(value = "此参数为微信用户在商户对应appid下的唯一标识", required = true)
    private String openid;


}
