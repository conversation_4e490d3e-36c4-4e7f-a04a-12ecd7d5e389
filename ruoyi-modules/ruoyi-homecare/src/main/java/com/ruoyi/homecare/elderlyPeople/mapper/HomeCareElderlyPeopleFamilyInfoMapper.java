package com.ruoyi.homecare.elderlyPeople.mapper;

import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleFamilyInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 老人家属信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Mapper
public interface HomeCareElderlyPeopleFamilyInfoMapper {
    /**
     * 查询老人家属信息
     *
     * @param id 老人家属信息主键
     * @return 老人家属信息
     */
    public ElderlyPeopleFamilyInfo selectElderlyPeopleFamilyInfoById(String id);

    /**
     * 查询老人家属信息列表
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 老人家属信息集合
     */
    public List<ElderlyPeopleFamilyInfo> selectElderlyPeopleFamilyInfoList(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 新增老人家属信息
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 结果
     */
    public int insertElderlyPeopleFamilyInfo(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 修改老人家属信息
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 结果
     */
    public int updateElderlyPeopleFamilyInfo(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 删除老人家属信息
     *
     * @param id 老人家属信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleFamilyInfoById(String id);

    /**
     * 批量删除老人家属信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleFamilyInfoByIds(String[] ids);
}
