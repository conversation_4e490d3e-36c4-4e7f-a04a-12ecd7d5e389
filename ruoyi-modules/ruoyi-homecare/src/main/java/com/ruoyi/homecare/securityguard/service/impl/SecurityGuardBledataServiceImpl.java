package com.ruoyi.homecare.securityguard.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleInfoService;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardBledata;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardDevciePeopleInfo;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardDeviceInfo;
import com.ruoyi.homecare.securityguard.mapper.SecurityGuardBledataMapper;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardBledataService;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardDevciePeopleInfoService;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardDeviceInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 血压信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Service
public class SecurityGuardBledataServiceImpl extends MPJBaseServiceImpl<SecurityGuardBledataMapper, SecurityGuardBledata> implements ISecurityGuardBledataService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ISecurityGuardDevciePeopleInfoService securityGuardDevciePeopleInfoService;

    @Autowired
    private SecurityGuardBledataMapper securityGuardBledataMapper;

    @Autowired
    private IHomeCareElderlyPeopleInfoService homeCareElderlyPeopleInfoService;

    @Autowired
    private ISecurityGuardDeviceInfoService securityGuardDeviceInfoService;

    /**
     * 查询血压信息
     *
     * @param id 血压信息主键
     * @return 血压信息
     */
    @Override
    public SecurityGuardBledata selectSecurityGuardBledataById(Long id) {
        SecurityGuardBledata securityGuardBledata = securityGuardBledataMapper.selectSecurityGuardBledataById(id);
        if (securityGuardBledata != null && StringUtils.isNotEmpty(securityGuardBledata.getContextid())) {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("contextid", securityGuardBledata.getContextid());
            SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo = securityGuardDevciePeopleInfoService.getOne(queryWrapper);
            ElderlyPeopleInfo elderlyPeopleInfo = homeCareElderlyPeopleInfoService.selectElderlyPeopleInfoById(securityGuardDevciePeopleInfo.getPeopleId());
            securityGuardBledata.setPeopleName(elderlyPeopleInfo.getName());
            securityGuardBledata.setIdCardNum(elderlyPeopleInfo.getIdCardNum());
            securityGuardBledata.setPhone(elderlyPeopleInfo.getPhone());
            return securityGuardBledata;
        }
        return securityGuardBledata;
    }

    /**
     * 查询血压信息列表
     *
     * @param securityGuardBledata 血压信息
     * @return 血压信息
     */
    @Override
    public List<SecurityGuardBledata> selectSecurityGuardBledataList(SecurityGuardBledata securityGuardBledata) {
        MPJLambdaWrapper<SecurityGuardBledata> mpjLambdaWrapper = new MPJLambdaWrapper();
        mpjLambdaWrapper.selectAll(SecurityGuardBledata.class)
                .select(ElderlyPeopleInfo::getIdCardNum, ElderlyPeopleInfo::getPhone)// 老人信息
                .selectAs(ElderlyPeopleInfo::getName, SecurityGuardBledata::getPeopleName)
                .selectAs(SecurityGuardDeviceInfo::getName, SecurityGuardBledata::getDeviceName)
                .select(SecurityGuardDeviceInfo::getModelNumber)
                .innerJoin(SecurityGuardDevciePeopleInfo.class, SecurityGuardDevciePeopleInfo::getContextid, SecurityGuardBledata::getContextid)
                .leftJoin(ElderlyPeopleInfo.class, ElderlyPeopleInfo::getId, SecurityGuardDevciePeopleInfo::getPeopleId)
                .leftJoin(SecurityGuardDeviceInfo.class, SecurityGuardDeviceInfo::getId, SecurityGuardDevciePeopleInfo::getDeviceId)
                .like(StringUtils.isNotEmpty(securityGuardBledata.getDeviceName()), SecurityGuardDeviceInfo::getName, securityGuardBledata.getDeviceName())// 模糊查询：设备名称
                .like(StringUtils.isNotEmpty(securityGuardBledata.getModelNumber()), SecurityGuardDeviceInfo::getModelNumber, securityGuardBledata.getModelNumber())// 模糊查询：设备型号
                .like(StringUtils.isNotEmpty(securityGuardBledata.getPeopleName()), ElderlyPeopleInfo::getName, securityGuardBledata.getPeopleName())// 模糊查询：老人名字
                .like(StringUtils.isNotEmpty(securityGuardBledata.getPhone()), ElderlyPeopleInfo::getPhone, securityGuardBledata.getPhone())// 模糊查询：手机号
                .like(StringUtils.isNotEmpty(securityGuardBledata.getIdCardNum()), ElderlyPeopleInfo::getIdCardNum, securityGuardBledata.getIdCardNum())// 模糊查询：身份证号码
                .like(StringUtils.isNotEmpty(securityGuardBledata.getContextid()), SecurityGuardBledata::getContextid, securityGuardBledata.getContextid())
                .eq(SecurityGuardBledata::getDevType, securityGuardBledata.getDevType())// 模糊查询：关联标识
                .orderByDesc(SecurityGuardBledata::getCreateTime);
        List<SecurityGuardBledata> list = selectJoinList(SecurityGuardBledata.class, mpjLambdaWrapper);
        return list;
    }

    /**
     * 新增血压信息
     *
     * @param securityGuardBledata 血压信息
     * @return 结果
     */
    @Override
    public int insertSecurityGuardBledata(SecurityGuardBledata securityGuardBledata) {
        securityGuardBledata.setCreateTime(DateUtils.getNowDate());
        return securityGuardBledataMapper.insertSecurityGuardBledata(securityGuardBledata);
    }

    /**
     * 修改血压信息
     *
     * @param securityGuardBledata 血压信息
     * @return 结果
     */
    @Override
    public int updateSecurityGuardBledata(SecurityGuardBledata securityGuardBledata) {
        return securityGuardBledataMapper.updateSecurityGuardBledata(securityGuardBledata);
    }

    /**
     * 批量删除血压信息
     *
     * @param ids 需要删除的血压信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardBledataByIds(Long[] ids) {
        return securityGuardBledataMapper.deleteSecurityGuardBledataByIds(ids);
    }

    /**
     * 删除血压信息信息
     *
     * @param id 血压信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardBledataById(Long id) {
        return securityGuardBledataMapper.deleteSecurityGuardBledataById(id);
    }

    @Override
    public int saveMsg(JSONObject argument, String mac) {
        if (0 == argument.getInt("dataType")) {
            Integer devType = argument.getInt("devType");
            SecurityGuardBledata bledata = new SecurityGuardBledata();
            bledata.setCreateTime(new Date());
            bledata.setName(argument.getStr("name"));
            bledata.setEp(argument.getStr("ep"));
            bledata.setIeee(argument.getStr("ieee"));
            bledata.setMac(mac);
            bledata.setZoneType(argument.getStr("zoneType"));
            bledata.setZoneId(argument.getLong("zoneId"));
            bledata.setDevType(argument.getInt("devType"));
            bledata.setContextid(mac + ":::" + bledata.getEp() + ":::" + bledata.getIeee());

            // 查找关联表示是否已记录
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("contextid", bledata.getContextid());
            int count = securityGuardDevciePeopleInfoService.count(queryWrapper);
            if (count < 1) {
                logger.warn("--------关联标识： " + bledata.getContextid() + "未记录！！----------");
                return -1;
            }
            if (devType == 48643) {
                logger.info(":::::::::::::::::::血压测试结果:::::::::::::::::::::");
                logger.info("关联标识" + bledata.getContextid());
                logger.info("高压" + argument.getStr("SBP"));
                logger.info("低压" + argument.getStr("DBP"));
                logger.info("脉搏" + argument.getStr("Sphygmus"));
                bledata.setDbp(argument.getLong("DBP"));
                bledata.setSbp(argument.getLong("SBP"));
                bledata.setSphygmus(argument.getLong("Sphygmus"));

            } else {
                logger.info(":::::::::::::::::::血糖测试结果:::::::::::::::::::::");
                logger.info("关联标识" + bledata.getContextid());
                logger.info("血糖" + argument.getStr("SBP"));
                logger.info("低压" + argument.getStr("DBP"));
                bledata.setGlucose(argument.getInt("Glucose"));
                bledata.setGlucoseParse(OwonParse.parseGlucose(bledata.getGlucose()));
            }
            return securityGuardBledataMapper.insertSecurityGuardBledata(bledata);
        }
        return 0;
    }
}
