package com.ruoyi.homecare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleLifeStyle;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleLifeStyleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家老人生活方式信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@RestController
@RequestMapping("/homeCareLifeStyle")
@Api(value = "老人管理-居家老人生活方式信息Controller", tags = "老人管理-居家老人生活方式信息")
public class HomeCareElderlyPeopleLifeStyleController extends BaseController {
    @Autowired
    private IHomeCareElderlyPeopleLifeStyleService elderlyPeopleLifeStyleService;

    /**
     * 查询居家老人生活方式信息列表
     */
    //@RequiresPermissions("elderlyPeople:lifeStyle:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询居家老人生活方式信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        startPage();
        List<ElderlyPeopleLifeStyle> list = elderlyPeopleLifeStyleService.selectElderlyPeopleLifeStyleList(elderlyPeopleLifeStyle);
        return getDataTable(list);
    }

    /**
     * 查询居家老人生活方式信息列表
     */
    //@RequiresPermissions("elderlyPeople:lifeStyle:list")
    @GetMapping("/getLifeStyleByUserId")
    @ApiOperation(value = "根据老人id查询居家老人生活方式信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = true, dataTypeClass = String.class),
    })
    public AjaxResult getLifeStyleByUserId(@ApiIgnore ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        List<ElderlyPeopleLifeStyle> list = elderlyPeopleLifeStyleService.selectElderlyPeopleLifeStyleList(elderlyPeopleLifeStyle);
        if (list.size() > 0) {
            return AjaxResult.success().put("data", list.get(0));
        } else {
            return AjaxResult.success().put("data", list);
        }
    }

    /**
     * 导出居家老人生活方式信息列表
     */
    //@RequiresPermissions("elderlyPeople:lifeStyle:export")
    @Log(platform = "2", title = "居家老人生活方式信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出居家老人生活方式信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        List<ElderlyPeopleLifeStyle> list = elderlyPeopleLifeStyleService.selectElderlyPeopleLifeStyleList(elderlyPeopleLifeStyle);
        ExcelUtil<ElderlyPeopleLifeStyle> util = new ExcelUtil<ElderlyPeopleLifeStyle>(ElderlyPeopleLifeStyle.class);
        util.exportExcel(response, list, "居家老人生活方式信息数据");
    }

    /**
     * 获取居家老人生活方式信息详细信息
     */
    //@RequiresPermissions("elderlyPeople:lifeStyle:query")
    @ApiOperation(value = "获取居家老人生活方式信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(elderlyPeopleLifeStyleService.selectElderlyPeopleLifeStyleById(id));
    }

    /**
     * 保存居家老人生活方式信息
     */
    @ApiOperation(value = "保存居家老人生活方式信息")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        if (StringUtils.isBlank(elderlyPeopleLifeStyle.getId())) {
            return toAjax(elderlyPeopleLifeStyleService.insertElderlyPeopleLifeStyle(elderlyPeopleLifeStyle));
        } else {
            return toAjax(elderlyPeopleLifeStyleService.updateElderlyPeopleLifeStyle(elderlyPeopleLifeStyle));
        }
    }


    /**
     * 新增居家老人生活方式信息
     */
    //@RequiresPermissions("elderlyPeople:lifeStyle:add")
    @Log(platform = "2", title = "居家老人生活方式信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增居家老人生活方式信息")
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        return toAjax(elderlyPeopleLifeStyleService.insertElderlyPeopleLifeStyle(elderlyPeopleLifeStyle));
    }

    /**
     * 修改居家老人生活方式信息
     */
    //@RequiresPermissions("elderlyPeople:lifeStyle:edit")
    @Log(platform = "2", title = "居家老人生活方式信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改居家老人生活方式信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        return toAjax(elderlyPeopleLifeStyleService.updateElderlyPeopleLifeStyle(elderlyPeopleLifeStyle));
    }

    /**
     * 删除居家老人生活方式信息
     */
    //@RequiresPermissions("elderlyPeople:lifeStyle:remove")
    @Log(platform = "2", title = "居家老人生活方式信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除居家老人生活方式信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(elderlyPeopleLifeStyleService.deleteElderlyPeopleLifeStyleByIds(ids));
    }
}
