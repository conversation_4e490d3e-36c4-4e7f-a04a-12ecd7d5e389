package com.ruoyi.homecare.elderlyPeople.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 能力评估复核对象 t_review_records_info
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@ApiModel(value = "能力评估复核")
public class ReviewRecordsInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 评估报告id
     */
    @Excel(name = "评估报告id")
    @ApiModelProperty(value = "评估报告id")
    private Long baseId;

    /**
     * 复核日常生活活动评估值
     */
    @Excel(name = "复核日常生活活动评估值")
    @ApiModelProperty(value = "复核日常生活活动评估值")
    private Long selfCareAbilityAssessmentValue;

    /**
     * 复核日常生活活动评估文字
     */
    @Excel(name = "复核日常生活活动评估文字")
    @ApiModelProperty(value = "复核日常生活活动评估文字")
    private String selfCareAbilityAssessmentLabel;

    /**
     * 复核卷感知觉与沟通评估值
     */
    @Excel(name = "复核卷感知觉与沟通评估值")
    @ApiModelProperty(value = "复核卷感知觉与沟通评估值")
    private Long athleticAbilityAssessmentValue;

    /**
     * 复核卷感知觉与沟通评估文字
     */
    @Excel(name = "复核卷感知觉与沟通评估文字")
    @ApiModelProperty(value = "复核卷感知觉与沟通评估文字")
    private String athleticAbilityAssessmentLabel;

    /**
     * 复核精神状态评估值
     */
    @Excel(name = "复核精神状态评估值")
    @ApiModelProperty(value = "复核精神状态评估值")
    private Long mentalStateAssessmentValue;

    /**
     * 复核精神状态评估文字
     */
    @Excel(name = "复核精神状态评估文字")
    @ApiModelProperty(value = "复核精神状态评估文字")
    private String mentalStateAssessmentLabel;

    /**
     * 复核感知觉与社会参与评估值
     */
    @Excel(name = "复核感知觉与社会参与评估值")
    @ApiModelProperty(value = "复核感知觉与社会参与评估值")
    private Long perceptionSocialEngagementAssessmentValue;

    /**
     * 感知觉与社会参与评估文字
     */
    @Excel(name = "复核感知觉与社会参与评估文字")
    @ApiModelProperty(value = "复核感知觉与社会参与评估文字")
    private String perceptionSocialEngagementAssessmentLabel;

    /**
     * 复核老年人能力初步评估等级
     */
    @Excel(name = "复核老年人能力评估等级")
    @ApiModelProperty(value = "复核老年人能力评估等级")
    private String preliminaryAssessmentLevel;

    /**
     * 复核原因
     */
    @Excel(name = "复核原因")
    @ApiModelProperty(value = "复核原因")
    private String reasonReview;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBaseId() {
        return baseId;
    }

    public void setBaseId(Long baseId) {
        this.baseId = baseId;
    }

    public Long getSelfCareAbilityAssessmentValue() {
        return selfCareAbilityAssessmentValue;
    }

    public void setSelfCareAbilityAssessmentValue(Long selfCareAbilityAssessmentValue) {
        this.selfCareAbilityAssessmentValue = selfCareAbilityAssessmentValue;
    }

    public String getSelfCareAbilityAssessmentLabel() {
        return selfCareAbilityAssessmentLabel;
    }

    public void setSelfCareAbilityAssessmentLabel(String selfCareAbilityAssessmentLabel) {
        this.selfCareAbilityAssessmentLabel = selfCareAbilityAssessmentLabel;
    }

    public Long getAthleticAbilityAssessmentValue() {
        return athleticAbilityAssessmentValue;
    }

    public void setAthleticAbilityAssessmentValue(Long athleticAbilityAssessmentValue) {
        this.athleticAbilityAssessmentValue = athleticAbilityAssessmentValue;
    }

    public String getAthleticAbilityAssessmentLabel() {
        return athleticAbilityAssessmentLabel;
    }

    public void setAthleticAbilityAssessmentLabel(String athleticAbilityAssessmentLabel) {
        this.athleticAbilityAssessmentLabel = athleticAbilityAssessmentLabel;
    }

    public Long getMentalStateAssessmentValue() {
        return mentalStateAssessmentValue;
    }

    public void setMentalStateAssessmentValue(Long mentalStateAssessmentValue) {
        this.mentalStateAssessmentValue = mentalStateAssessmentValue;
    }

    public String getMentalStateAssessmentLabel() {
        return mentalStateAssessmentLabel;
    }

    public void setMentalStateAssessmentLabel(String mentalStateAssessmentLabel) {
        this.mentalStateAssessmentLabel = mentalStateAssessmentLabel;
    }

    public Long getPerceptionSocialEngagementAssessmentValue() {
        return perceptionSocialEngagementAssessmentValue;
    }

    public void setPerceptionSocialEngagementAssessmentValue(Long perceptionSocialEngagementAssessmentValue) {
        this.perceptionSocialEngagementAssessmentValue = perceptionSocialEngagementAssessmentValue;
    }

    public String getPerceptionSocialEngagementAssessmentLabel() {
        return perceptionSocialEngagementAssessmentLabel;
    }

    public void setPerceptionSocialEngagementAssessmentLabel(String perceptionSocialEngagementAssessmentLabel) {
        this.perceptionSocialEngagementAssessmentLabel = perceptionSocialEngagementAssessmentLabel;
    }

    public String getPreliminaryAssessmentLevel() {
        return preliminaryAssessmentLevel;
    }

    public void setPreliminaryAssessmentLevel(String preliminaryAssessmentLevel) {
        this.preliminaryAssessmentLevel = preliminaryAssessmentLevel;
    }

    public String getReasonReview() {
        return reasonReview;
    }

    public void setReasonReview(String reasonReview) {
        this.reasonReview = reasonReview;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("baseId", getBaseId())
                .append("selfCareAbilityAssessmentValue", getSelfCareAbilityAssessmentValue())
                .append("selfCareAbilityAssessmentLabel", getSelfCareAbilityAssessmentLabel())
                .append("athleticAbilityAssessmentValue", getAthleticAbilityAssessmentValue())
                .append("athleticAbilityAssessmentLabel", getAthleticAbilityAssessmentLabel())
                .append("mentalStateAssessmentValue", getMentalStateAssessmentValue())
                .append("mentalStateAssessmentLabel", getMentalStateAssessmentLabel())
                .append("perceptionSocialEngagementAssessmentValue", getPerceptionSocialEngagementAssessmentValue())
                .append("perceptionSocialEngagementAssessmentLabel", getPerceptionSocialEngagementAssessmentLabel())
                .append("preliminaryAssessmentLevel", getPreliminaryAssessmentLevel())
                .append("reasonReview", getReasonReview())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
