package com.ruoyi.homecare.elderlyPeople.mapper;

import com.ruoyi.homecare.elderlyPeople.domain.CapabilityAssessmentInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 能力评估报告Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@Mapper
public interface HomeCareCapabilityAssessmentInfoMapper {
    /**
     * 查询能力评估报告
     *
     * @param id 能力评估报告主键
     * @return 能力评估报告
     */
    public CapabilityAssessmentInfo selectCapabilityAssessmentInfoById(Long id);

    /**
     * 查询能力评估报告列表
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 能力评估报告集合
     */
    public List<CapabilityAssessmentInfo> selectCapabilityAssessmentInfoList(CapabilityAssessmentInfo capabilityAssessmentInfo);

    /**
     * 新增能力评估报告
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 结果
     */
    public int insertCapabilityAssessmentInfo(CapabilityAssessmentInfo capabilityAssessmentInfo);

    /**
     * 修改能力评估报告
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 结果
     */
    public int updateCapabilityAssessmentInfo(CapabilityAssessmentInfo capabilityAssessmentInfo);

    /**
     * 删除能力评估报告
     *
     * @param id 能力评估报告主键
     * @return 结果
     */
    public int deleteCapabilityAssessmentInfoById(Long id);

    /**
     * 批量删除能力评估报告
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCapabilityAssessmentInfoByIds(Long[] ids);


    /**
     * 复核通过id更改最终评估结果
     *
     * @param id
     * @param level
     * @return
     */
    public int updateReviewById(@Param(value = "id") Long id, @Param(value = "level") String level);
}
