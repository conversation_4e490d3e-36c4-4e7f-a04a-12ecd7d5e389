package com.ruoyi.homecare.securityguard.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleInfoService;
import com.ruoyi.homecare.securityguard.domain.*;
import com.ruoyi.homecare.securityguard.mapper.SecurityGuardSensorMapper;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardDevciePeopleInfoService;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardEpService;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardSensorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 拉绳传感器报警信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Service
public class SecurityGuardSensorServiceImpl implements ISecurityGuardSensorService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SecurityGuardSensorMapper securityGuardSensorMapper;

    @Autowired
    private ISecurityGuardDevciePeopleInfoService securityGuardDevciePeopleInfoService;

    @Autowired
    private IHomeCareElderlyPeopleInfoService homeCareElderlyPeopleInfoService;
    @Autowired
    private ISecurityGuardEpService epService;

    /**
     * 查询拉绳传感器报警信息
     *
     * @param id 拉绳传感器报警信息主键
     * @return 拉绳传感器报警信息
     */
    @Override
    public SecurityGuardSensor selectSecurityGuardSensorById(Long id) {
        SecurityGuardSensor securityGuardSensor = securityGuardSensorMapper.selectSecurityGuardSensorById(id);
        if (securityGuardSensor != null && StringUtils.isNotEmpty(securityGuardSensor.getContextid())) {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("contextid", securityGuardSensor.getContextid());
            SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo = securityGuardDevciePeopleInfoService.getOne(queryWrapper);
            ElderlyPeopleInfo elderlyPeopleInfo = homeCareElderlyPeopleInfoService.selectElderlyPeopleInfoById(securityGuardDevciePeopleInfo.getPeopleId());
            securityGuardSensor.setPeopleName(elderlyPeopleInfo.getName());
            securityGuardSensor.setIdCardNum(elderlyPeopleInfo.getIdCardNum());
            securityGuardSensor.setPhone(elderlyPeopleInfo.getPhone());
            return securityGuardSensor;
        }
        return securityGuardSensor;
    }

    /**
     * 查询拉绳传感器报警信息列表
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 拉绳传感器报警信息
     */
    @Override
    public List<SecurityGuardSensor> selectSecurityGuardSensorList(SecurityGuardSensor securityGuardSensor) {

        MPJLambdaWrapper<SecurityGuardSensor> mpjLambdaWrapper = new MPJLambdaWrapper();
        mpjLambdaWrapper.selectAll(SecurityGuardSensor.class)
                .select(ElderlyPeopleInfo::getIdCardNum, ElderlyPeopleInfo::getPhone)// 老人信息
                .selectAs(ElderlyPeopleInfo::getName, SecurityGuardSensor::getPeopleName)
                .selectAs(SecurityGuardDeviceInfo::getName, SecurityGuardSensor::getDeviceName)
                .select(SecurityGuardDeviceInfo::getModelNumber)
                .innerJoin(SecurityGuardDevciePeopleInfo.class, SecurityGuardDevciePeopleInfo::getContextid, SecurityGuardSensor::getContextid)
                .leftJoin(ElderlyPeopleInfo.class, ElderlyPeopleInfo::getId, SecurityGuardDevciePeopleInfo::getPeopleId)
                .leftJoin(SecurityGuardDeviceInfo.class, SecurityGuardDeviceInfo::getId, SecurityGuardDevciePeopleInfo::getDeviceId)
                .like(StringUtils.isNotEmpty(securityGuardSensor.getDeviceName()), SecurityGuardDeviceInfo::getName, securityGuardSensor.getDeviceName())// 模糊查询：设备名称
                .like(StringUtils.isNotEmpty(securityGuardSensor.getModelNumber()), SecurityGuardDeviceInfo::getModelNumber, securityGuardSensor.getModelNumber())// 模糊查询：设备型号
                .like(StringUtils.isNotEmpty(securityGuardSensor.getPeopleName()), ElderlyPeopleInfo::getName, securityGuardSensor.getPeopleName())// 模糊查询：老人名字
                .like(StringUtils.isNotEmpty(securityGuardSensor.getPhone()), ElderlyPeopleInfo::getPhone, securityGuardSensor.getPhone())// 模糊查询：手机号
                .like(StringUtils.isNotEmpty(securityGuardSensor.getIdCardNum()), ElderlyPeopleInfo::getIdCardNum, securityGuardSensor.getIdCardNum())// 模糊查询：身份证号码
                .like(StringUtils.isNotEmpty(securityGuardSensor.getContextid()), SecurityGuardSensor::getContextid, securityGuardSensor.getContextid())
                .eq(SecurityGuardSensor::getDevType, securityGuardSensor.getDevType())// 模糊查询：关联标识
                .orderByDesc(SecurityGuardSensor::getCreateTime);
        List<SecurityGuardSensor> list = securityGuardSensorMapper.selectJoinList(SecurityGuardSensor.class, mpjLambdaWrapper);
        return list;
    }

    /**
     * 新增拉绳传感器报警信息
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 结果
     */
    @Override
    public int insertSecurityGuardSensor(SecurityGuardSensor securityGuardSensor) {
        securityGuardSensor.setCreateTime(DateUtils.getNowDate());
        return securityGuardSensorMapper.insertSecurityGuardSensor(securityGuardSensor);
    }

    /**
     * 修改拉绳传感器报警信息
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 结果
     */
    @Override
    public int updateSecurityGuardSensor(SecurityGuardSensor securityGuardSensor) {
        return securityGuardSensorMapper.updateSecurityGuardSensor(securityGuardSensor);
    }

    /**
     * 批量删除拉绳传感器报警信息
     *
     * @param ids 需要删除的拉绳传感器报警信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardSensorByIds(Long[] ids) {
        return securityGuardSensorMapper.deleteSecurityGuardSensorByIds(ids);
    }

    /**
     * 删除拉绳传感器报警信息信息
     *
     * @param id 拉绳传感器报警信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardSensorById(Long id) {
        return securityGuardSensorMapper.deleteSecurityGuardSensorById(id);
    }

    @Override
    public int saveMsg(JSONObject argument, String mac) {
        logger.info(":::::::::::::::::::报警器报警结果:::::::::::::::::::::");
        logger.info("报警设备::::::" + argument.getStr("name"));
        logger.info("报警设备ep::::::" + argument.getStr("ep"));
        logger.info("报警设备ep ieee::::::" + argument.getStr("ieee"));
        logger.info("报警状态" + argument.getStr("status"));
        String contextid = mac + ":::" + argument.getStr("name");
        logger.info("关联标识:" + contextid);

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("contextid", contextid);
        int count = securityGuardDevciePeopleInfoService.count(queryWrapper);
        if (count < 1) {
            logger.warn("--------关联标识： " + contextid + "  未记录！！----------");
            return -1;
        }

        SecurityGuardSensor sensor = new SecurityGuardSensor();
        SecurityGuardEp guardEp = epService.selectSecurityGuardByEPIEEE(argument.getStr("ep"), argument.getStr("ieee"));

        if (guardEp != null) {
            String devmodel = guardEp.getDevmodel();
            // 门磁传感器
            if (devmodel.contains("DWS312")) {
                sensor.setDevType(0);
            } else if (devmodel.contains("PB236")) { // 拉绳报警器
                sensor.setDevType(1);
            } else if (devmodel.contains("PIR323")) { // 活动传感器
                sensor.setDevType(2);
            }
        }
        sensor.setCreateTime(new Date());
        sensor.setName(argument.getStr("name"));
        sensor.setEp(argument.getStr("ep"));
        sensor.setMac(mac);
        sensor.setIeee(argument.getStr("ieee"));
        sensor.setZoneType(argument.getStr("zoneType"));
        sensor.setZoneId(argument.getLong("zoneId"));
        sensor.setStatus(argument.getLong("status"));
        sensor.setContextid(contextid);
        return securityGuardSensorMapper.insertSecurityGuardSensor(sensor);
    }

//    public static void main(String[] args) {
//        JSONObject jsonObject = JSONUtil.parseObj("{\n" +
//                "\t\"code\": \"102\",\n" +
//                "\t\"mac\": \"3C6A2CFFFED21FBD\",\n" +
//                "\t\"sjson\": \"{\\\"result\\\":true,\\\"sequence\\\":1026,\\\"response\\\":{\\\"total\\\":3,\\\"start\\\":0,\\\"count\\\":3,\\\"epList\\\":[{\\\"linkStatus\\\":true,\\\"deviceType\\\":1026,\\\"ProfileId\\\":260,\\\"devModel\\\":\\\"PB236-C\\\",\\\"IASZoneType\\\":44,\\\"netDeviceType\\\":2,\\\"name\\\":\\\"Sensor-D1E6EB\\\",\\\"clusterFlag\\\":0,\\\"manuCode\\\":4412,\\\"ep\\\":1,\\\"ieee\\\":\\\"EBE6D1FEFF2C6A3C\\\"},{\\\"linkStatus\\\":true,\\\"deviceType\\\":1026,\\\"ProfileId\\\":260,\\\"devModel\\\":\\\"PIR323-P\\\",\\\"IASZoneType\\\":13,\\\"netDeviceType\\\":2,\\\"name\\\":\\\"Sensor-D22547\\\",\\\"clusterFlag\\\":0,\\\"manuCode\\\":4412,\\\"ep\\\":1,\\\"ieee\\\":\\\"4725D2FEFF2C6A3C\\\"},{\\\"linkStatus\\\":true,\\\"deviceType\\\":1026,\\\"ProfileId\\\":260,\\\"devModel\\\":\\\"DWS312\\\",\\\"IASZoneType\\\":21,\\\"netDeviceType\\\":2,\\\"name\\\":\\\"Sensor-D2121C\\\",\\\"clusterFlag\\\":0,\\\"manuCode\\\":4412,\\\"ep\\\":1,\\\"ieee\\\":\\\"1C12D2FEFF2C6A3C\\\"}]},\\\"session\\\":\\\"SKOhj91IrlxTplQ\\\",\\\"description\\\":\\\"epList back success\\\"}\",\n" +
//                "\t\"token\": \"kIt-79JOk5\",\n" +
//                "\t\"ts\": \"723190042\"\n" +
//                "}");
//
//        JSONObject sjson = jsonObject.getJSONObject("sjson");
//        JSONArray epList = sjson.getJSONObject("response").getJSONArray("epList");
//        for (Object o : epList) {
//            JSONObject o1 = (JSONObject) o;
//            System.out.println(o1);
//        }
//
//
//    }
}
