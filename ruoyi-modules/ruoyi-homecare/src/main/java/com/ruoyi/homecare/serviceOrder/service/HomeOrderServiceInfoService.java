package com.ruoyi.homecare.serviceOrder.service;

import com.ruoyi.common.core.web.domain.TAjaxResult;

import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderBaseInfo;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboDetailsInfo;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboInfo;
import com.ruoyi.homecare.serviceOrder.param.*;
import com.ruoyi.homecare.serviceOrder.vo.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description home_order_service_info服务层
 * @date 2022-07-14
 */
@Service
public interface HomeOrderServiceInfoService {


    TAjaxResult saveServiceOrder(HttpServletRequest request, HomeOrderServiceRequestVo homeOrderServiceRequestVo);

    TAjaxResult saveServiceComboOrder(HttpServletRequest request, HomeOrderServiceComboRequestVo homeOrderServiceComboRequestVo);

    TableDataInfo<SoldComboListVo> getSoldComboList(EtSoldComboListParam etSoldComboListParam);

    int merchantOrders(MerchantOrdersRequestVo requestVo);

    HomeOrderBaseInfo getOrderServiceInfo(String id);

    String serviceComboOrderUse(HomeOrderServiceComboParam01 homeOrderServiceComboParam01);

    TableDataInfo getUserCombo(UserComboParam userComboParam);

    TAjaxResult getUserComboDetails(UserComboDetailsParam userComboParam);

    /**
     * 分页查询服务订单列表
     *
     * @param homeAdminOrderServiceInfoParam
     * @return
     */
    TableDataInfo getOrderServiceInfoAll(HomeAdminOrderServiceInfoParam homeAdminOrderServiceInfoParam);

    /**
     * 查询服务订单详情
     *
     * @param id 工单id
     * @return
     */
    HomeAdminOrderServiceInfoVo getOrderServiceDataInfo(String id);

    /**
     * 服务商分页查询服务订单列表
     *
     * @param homeAdminOrderServiceInfoParam
     * @return
     */
    TableDataInfo getProviderOrderServiceInfoAll(HomeProviderOrderServiceInfoParam homeAdminOrderServiceInfoParam);

    TAjaxResult getComboDetailsByOrderId(ComboDetailsByOrderIdParam comboDetailsByOrderIdParam);

    TableDataInfo geWorkList(ComboUseListVo comboUseListVo);

    TAjaxResult getComboDetailsInfo(ComboDetailsInfoVo comboDetailsInfoVo);

    String sellComboToOrder(SellComboToOrdertVo sellComboToOrdertVo);

    List getOrderServiceComboInfoList(String comboId, String serviceName);

    TAjaxResult getUserInfo(String comboDetailsId);
}
