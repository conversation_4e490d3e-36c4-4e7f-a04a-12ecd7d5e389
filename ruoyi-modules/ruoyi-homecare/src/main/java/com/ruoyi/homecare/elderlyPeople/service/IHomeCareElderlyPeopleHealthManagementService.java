package com.ruoyi.homecare.elderlyPeople.service;

import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthManagement;

import java.util.List;

/**
 * 老人健康管理信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
public interface IHomeCareElderlyPeopleHealthManagementService {
    /**
     * 查询老人健康管理信息
     *
     * @param id 老人健康管理信息主键
     * @return 老人健康管理信息
     */
    public ElderlyPeopleHealthManagement selectElderlyPeopleHealthManagementById(String id);

    /**
     * 查询老人健康管理信息列表
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 老人健康管理信息集合
     */
    public List<ElderlyPeopleHealthManagement> selectElderlyPeopleHealthManagementList(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement);

    /**
     * 新增老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 结果
     */
    public int insertElderlyPeopleHealthManagement(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement);

    /**
     * 保存老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement
     * @return
     */
    public int save(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement);

    /**
     * 修改老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 结果
     */
    public int updateElderlyPeopleHealthManagement(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement);

    /**
     * 批量删除老人健康管理信息
     *
     * @param ids 需要删除的老人健康管理信息主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleHealthManagementByIds(String[] ids);

    /**
     * 删除老人健康管理信息信息
     *
     * @param id 老人健康管理信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleHealthManagementById(String id);
}
