package com.ruoyi.homecare.serviceWorkOrder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderBaseInfo;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderCommentInfo;
import com.ruoyi.homecare.goodsOrder.mapper.HomeOrderBaseInfoMapper;
import com.ruoyi.homecare.goodsOrder.mapper.HomeOrderCommentInfoMapper;
import com.ruoyi.homecare.service.domain.HomeServiceProviderWorker;
import com.ruoyi.homecare.service.mapper.HomeServiceProviderWorkerMapper;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboDetailsInfo;
import com.ruoyi.homecare.serviceOrder.mapper.HomeOrderServiceComboDetailsInfoMapper;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProviderManagementMapper;
import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import com.ruoyi.homecare.serviceWorkOrder.mapper.HomeServiceWorkOrderMapper;
import com.ruoyi.homecare.serviceWorkOrder.param.HomeServiceWorkOrderParam;
import com.ruoyi.homecare.serviceWorkOrder.service.HomeServiceWorkOrderService;
import com.ruoyi.homecare.serviceWorkOrder.vo.HomeAppServiceWorkOrderInfoRequestVo;
import com.ruoyi.homecare.serviceWorkOrder.vo.HomeServiceWorkOrderRequestVo;
import com.ruoyi.homecare.utils.OrderUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class HomeServiceWorkOrderServiceImpl implements HomeServiceWorkOrderService {

    @Autowired
    private HomeServiceWorkOrderMapper homeServiceWorkOrderMapper;
    @Autowired
    private HomeOrderBaseInfoMapper homeOrderBaseInfoMapper;

    @Autowired
    private HomeServiceProviderWorkerMapper homeServiceProviderWorkerMapper;
    @Autowired
    private HomeOrderCommentInfoMapper homeOrderCommentInfoMapper;
    @Autowired
    private HomeServiceProviderManagementMapper homeServiceProviderManagementMapper;
    @Autowired
    private HomeOrderServiceComboDetailsInfoMapper homeOrderServiceComboDetailsInfoMapper;


    @Override
    public int save(HomeOrderServiceWork homeOrderServiceWork) {
        if (homeOrderServiceWork.getId() == null) {
            homeOrderServiceWork.setId(OrderUtils.getWorkOrderCode(SecurityUtils.getUserId()));
            homeOrderServiceWork.setCreateTime(new Date());
            homeOrderServiceWork.setCreateBy(SecurityUtils.getUserId().toString());
            return homeServiceWorkOrderMapper.insert(homeOrderServiceWork);
        } else {
            homeOrderServiceWork.setUpdateTime(new Date());
            homeOrderServiceWork.setUpdateBy(SecurityUtils.getUserId().toString());
            return homeServiceWorkOrderMapper.updateById(homeOrderServiceWork);
        }
    }

    /**
     * 指派服务人员  并 生成工单
     *
     * @param homeServiceWorkOrderParam
     * @return
     */
    @Override
    public int assignWorker(HomeServiceWorkOrderParam homeServiceWorkOrderParam) {
        Long workerId = homeServiceWorkOrderParam.getWorkerId();
        HomeServiceProviderWorker homeServiceProviderWorker = homeServiceProviderWorkerMapper.selectById(workerId);
        String id = homeServiceWorkOrderParam.getId();
        HomeOrderServiceWork orderServiceWork = homeServiceWorkOrderMapper.selectById(id);
        orderServiceWork.setWorkerId(workerId);
        orderServiceWork.setWorkerPhone(homeServiceProviderWorker.getPhone());
        if (orderServiceWork.getStatus() != 1) {
            throw new SecurityException("当前工单状态异常！");
        }
        orderServiceWork.setStatus(2);
        return homeServiceWorkOrderMapper.updateById(orderServiceWork);
    }

    @Override
    public List<HomeOrderServiceWork> getWorkOrderList(HomeOrderServiceWork homeOrderServiceWork) {
        QueryWrapper<HomeOrderServiceWork> query = Wrappers.query();
        if (StrUtil.isNotEmpty(homeOrderServiceWork.getId())) {
            query.like("id", homeOrderServiceWork.getId());
        }
        return homeServiceWorkOrderMapper.selectList(query);
    }

    /**
     * 服务人员开始服务
     *
     * @param requestVo
     * @return
     */
    @Override
    public int startService(HomeServiceWorkOrderRequestVo requestVo) {
        String id = requestVo.getId();
        HomeOrderServiceWork homeOrderServiceWork = homeServiceWorkOrderMapper.selectById(id);
        HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(homeOrderServiceWork.getOrderId());
        if (null == homeOrderServiceWork || null == homeOrderBaseInfo) {
            throw new ServiceException("当前订单服务信息异常！");
        }
        homeOrderServiceWork.setStartTime(new Date());
        homeOrderServiceWork.setStartImg(requestVo.getStartImg());
        homeOrderServiceWork.setStatus(3);
        /*//更新订单基础表
        if (homeOrderServiceWork.getServiceType() == 0){ //如果为服务改变订单基础表
            homeOrderBaseInfo.setReceiveTime(new Date());
            homeOrderBaseInfoMapper.updateById(homeOrderBaseInfo);
        }*/
        return homeServiceWorkOrderMapper.updateById(homeOrderServiceWork);
    }

    /**
     * 服务人员服务签退
     *
     * @param requestVo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int endService(HomeServiceWorkOrderRequestVo requestVo) {
        String id = requestVo.getId();
        Date date = new Date();
        HomeOrderServiceWork homeOrderServiceWork = homeServiceWorkOrderMapper.selectById(id);
        String orderId = homeOrderServiceWork.getOrderId();


        HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(orderId);
        if (null == homeOrderServiceWork || null == homeOrderBaseInfo) {
            throw new ServiceException("当前订单服务信息异常！");
        }
        homeOrderServiceWork.setEndTime(new Date());
        homeOrderServiceWork.setEndImg(requestVo.getEndImg());
        Date startTime = homeOrderServiceWork.getStartTime();
        Date newDate = new Date();
        Long time = (newDate.getTime() - startTime.getTime()) / 1000 / 60;
        homeOrderServiceWork.setServiceTime(time.intValue());
        homeOrderServiceWork.setStatus(4);
        // 更新订单基础表
        if (homeOrderServiceWork.getServiceType() == 0) { // 如果为服务改变订单基础表
            homeOrderBaseInfo.setFinishTime(new Date());
            homeOrderBaseInfo.setStatus(4);// 订单改为已完成状态
            homeOrderBaseInfo.setUpdateTime(date);
            homeOrderBaseInfoMapper.updateById(homeOrderBaseInfo);
        }
        if (homeOrderServiceWork.getServiceType() == 1) { // 如果为套餐服务

            HomeOrderServiceComboDetailsInfo homeOrderServiceComboDetailsInfo = homeOrderServiceComboDetailsInfoMapper.selectById(homeOrderServiceWork.getOrderServiceComboDetailsId());
            Integer totalCount = homeOrderServiceComboDetailsInfo.getTotalCount();// 总次数
            Integer completedCount = homeOrderServiceComboDetailsInfo.getCompletedCount() + 1;// 完成次数
            if (totalCount.equals(completedCount)) {
                homeOrderServiceComboDetailsInfo.setCompletedStatus(2);
            }
            if (completedCount < totalCount) {
                homeOrderServiceComboDetailsInfo.setCompletedCount(completedCount);
            }
            homeOrderServiceComboDetailsInfoMapper.updateById(homeOrderServiceComboDetailsInfo);

            QueryWrapper<HomeOrderServiceComboDetailsInfo> queryCount = Wrappers.query();
            queryCount.eq("order_id", orderId);
            queryCount.eq("completed_status", HomeOrderServiceComboDetailsInfo.COMPLETED_STATUS_HAS);
            Integer integer = homeOrderServiceComboDetailsInfoMapper.selectCount(queryCount);

            if (integer.equals(0)) {// 所有订单都使用完毕
                homeOrderBaseInfo.setStatus(HomeOrderBaseInfo.STATUS_FINISH);
                homeOrderBaseInfo.setUpdateTime(date);
                homeOrderBaseInfoMapper.updateById(homeOrderBaseInfo);
            }

        }
        HomeServiceProviderWorker homeServiceProviderWorker = homeServiceProviderWorkerMapper.selectById(homeOrderServiceWork.getWorkerId());
        if (null == homeServiceProviderWorker) {
            throw new SecurityException("当前工单有异常！");
        }
        // 工作人员服务次数+1
        homeServiceProviderWorker.setServiceTimes(homeServiceProviderWorker.getServiceTimes() + 1);
        homeServiceProviderWorkerMapper.updateById(homeServiceProviderWorker);
        // 商家总订单数增+1
        Long providerId = homeOrderBaseInfo.getProviderId();
        HomeServiceProviderManagement homeServiceProviderManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(providerId);
        homeServiceProviderManagement.setTotalOrderNum(homeServiceProviderManagement.getTotalOrderNum() + 1);
        homeServiceProviderManagementMapper.updateHomeServiceProviderManagement(homeServiceProviderManagement);
        return homeServiceWorkOrderMapper.updateById(homeOrderServiceWork);
    }

    /**
     * 服务人员现场照片保存
     *
     * @param requestVo
     * @return
     */
    @Override
    public int servingUpload(HomeServiceWorkOrderRequestVo requestVo) {
        String id = requestVo.getId();
        HomeOrderServiceWork homeOrderServiceWork = homeServiceWorkOrderMapper.selectById(id);
        if (null == homeOrderServiceWork) {
            throw new ServiceException("当前订单服务信息异常");
        }
        if (StringUtils.isEmpty(homeOrderServiceWork.getLiveImg())) {
            homeOrderServiceWork.setLiveImg(requestVo.getLiveImg());
        } else {
            homeOrderServiceWork.setLiveImg(homeOrderServiceWork.getLiveImg() + "|" + requestVo.getLiveImg());
        }

        return homeServiceWorkOrderMapper.updateById(homeOrderServiceWork);
    }

    /**
     * 服务人员移动待完成服务列表
     *
     * @param homeOrderServiceWork
     * @return
     */
    @Override
    public List<HomeOrderServiceWork> getNotCompletedServiceWorkOrderListByWork(HomeOrderServiceWork homeOrderServiceWork) {
        Long workId = homeOrderServiceWork.getWorkerId();
        QueryWrapper<HomeOrderServiceWork> query = Wrappers.query();
        if (null == workId) {
            throw new SecurityException("当前登录用户信息异常！");
        }
        query.eq("worker_id", workId);
        if (!StringUtils.isEmpty(homeOrderServiceWork.getName())) {
            query.like("name", homeOrderServiceWork.getName());
        }
        query.in("status", 2, 3);// 未完成状态
        query.orderByDesc("status");
        query.orderByAsc("reserve_time");
        return homeServiceWorkOrderMapper.selectList(query);
    }

    /**
     * 服务人员移动已完成服务列表
     *
     * @param homeOrderServiceWork
     * @return
     */
    @Override
    public List<HomeOrderServiceWork> getCompletedServiceWorkOrderListByWork(HomeOrderServiceWork homeOrderServiceWork) {
        Long workId = homeOrderServiceWork.getWorkerId();
        QueryWrapper<HomeOrderServiceWork> query = Wrappers.query();
        if (null == workId) {
            throw new SecurityException("当前登录用户信息异常！");
        }
        query.eq("worker_id", workId);
        if (!StringUtils.isEmpty(homeOrderServiceWork.getName())) {
            query.like("name", homeOrderServiceWork.getName());
        }
        query.eq("status", 4);// 已完成状态
        query.orderByDesc("end_time");
        return homeServiceWorkOrderMapper.selectList(query);
    }

    @Override
    public HomeAppServiceWorkOrderInfoRequestVo getServiceWorkOrderInfoById(String id) {
        HomeOrderServiceWork orderServiceWork = homeServiceWorkOrderMapper.selectById(id);
        HomeAppServiceWorkOrderInfoRequestVo homeAppServiceWorkOrderInfoRequestVo = new HomeAppServiceWorkOrderInfoRequestVo();
        BeanUtil.copyProperties(orderServiceWork, homeAppServiceWorkOrderInfoRequestVo);
        HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(orderServiceWork.getOrderId());
        if (null == homeOrderBaseInfo || null == orderServiceWork) {
            throw new SecurityException("当前工单信息异常！");
        }
        homeAppServiceWorkOrderInfoRequestVo.setPayType(homeOrderBaseInfo.getPayWay());
        if (orderServiceWork.getServiceType() == 0) {
            homeAppServiceWorkOrderInfoRequestVo.setPayAmount(homeOrderBaseInfo.getTotalFee());
        } else {// 如果是套餐的话支付金额为0
            homeAppServiceWorkOrderInfoRequestVo.setPayAmount(new BigDecimal(0));
        }
        QueryWrapper<HomeOrderCommentInfo> query = Wrappers.query();
        query.eq("order_id", homeOrderBaseInfo.getId());
        HomeOrderCommentInfo homeOrderCommentInfo = homeOrderCommentInfoMapper.selectOne(query);
        if (null != homeOrderCommentInfo) {
            homeAppServiceWorkOrderInfoRequestVo.setCommentContent(homeOrderCommentInfo.getCommentContent());
        }
        return homeAppServiceWorkOrderInfoRequestVo;
    }
}
