package com.ruoyi.homecare.serviceOrder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.domain.TAjaxResult;

import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.complaint.domain.HomeOrderComplaint;
import com.ruoyi.homecare.complaint.mapper.HomeOrderComplaintComplaintMapper;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.homecare.elderlyPeople.domain.vo.UserDataInfoResult;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleInfoMapper;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderBaseInfo;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderCommentInfo;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderRefundInfo;
import com.ruoyi.homecare.goodsOrder.mapper.*;
import com.ruoyi.homecare.goodsOrder.service.HomeOrderBaseInfoService;
import com.ruoyi.homecare.goodsOrder.vo.ProviderGetUserInfoVo;
import com.ruoyi.homecare.service.domain.HomeServiceComboDetails;
import com.ruoyi.homecare.service.domain.HomeServiceComboInfo;
import com.ruoyi.homecare.service.domain.HomeServiceProject;
import com.ruoyi.homecare.service.domain.HomeServiceProviderWorker;
import com.ruoyi.homecare.service.mapper.HomeServiceComboDetailsMapper;
import com.ruoyi.homecare.service.mapper.HomeServiceComboInfoMapper;
import com.ruoyi.homecare.service.mapper.HomeServiceProjectMapper;
import com.ruoyi.homecare.service.mapper.HomeServiceProviderWorkerMapper;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboDetailsInfo;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboInfo;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceInfo;
import com.ruoyi.homecare.serviceOrder.mapper.HomeOrderServiceComboDetailsInfoMapper;
import com.ruoyi.homecare.serviceOrder.mapper.HomeOrderServiceComboInfoMapper;
import com.ruoyi.homecare.serviceOrder.mapper.HomeOrderServiceInfoMapper;
import com.ruoyi.homecare.serviceOrder.param.*;
import com.ruoyi.homecare.serviceOrder.service.HomeOrderServiceInfoService;
import com.ruoyi.homecare.serviceOrder.vo.*;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProviderManagementMapper;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderManagementService;
import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import com.ruoyi.homecare.serviceWorkOrder.mapper.HomeServiceWorkOrderMapper;
import com.ruoyi.homecare.settings.domain.HomeBaseSettingsInfo;
import com.ruoyi.homecare.settings.service.IHomeBaseSettingsInfoService;
import com.ruoyi.homecare.utils.IpUtil;
import com.ruoyi.homecare.utils.OrderUtils;
import com.ruoyi.homecare.utils.SysUserUtils;
import com.ruoyi.homecare.wexinjavapay.components.WxPayComponent;
import com.ruoyi.homecare.wexinjavapay.request.UnifiedorderRequest;
import com.ruoyi.homecare.wxpayorderinfo.OrderPayComponent;
import com.ruoyi.homecare.wxpayorderinfo.domain.WxPayOrderInfo;
import com.ruoyi.homecare.wxpayorderinfo.mapper.WxPayOrderInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description home_order_service_info服务层
 * @date 2022-07-14
 */
@Service
public class HomeOrderServiceInfoServiceImpl implements HomeOrderServiceInfoService {

    @Autowired
    private HomeServiceProjectMapper serviceProjectMapper;
    @Autowired
    private HomeOrderBaseInfoService homeOrderBaseInfoService;
    @Autowired
    private HomeOrderBaseInfoMapper homeOrderBaseInfoMapper;
    @Autowired
    private HomeOrderServiceInfoMapper orderServiceInfoMapper;
    @Autowired
    private HomeServiceComboInfoMapper serviceComboInfoMapper;
    @Autowired
    private HomeServiceComboDetailsMapper serviceComboDetailsMapper;
    @Autowired
    private HomeOrderServiceComboDetailsInfoMapper homeOrderServiceComboDetailsInfoMapper;
    @Autowired
    private HomeOrderServiceComboInfoMapper orderServiceComboInfoMapper;
    @Autowired
    private HomeOrderServiceComboInfoMapper homeOrderServiceComboInfoMapper;
    @Autowired
    private HomeOrderRefundInfoMapper homeOrderRefundInfoMapper;
    @Autowired
    private HomeServiceWorkOrderMapper homeServiceWorkOrderMapper;
    @Autowired
    private HomeOrderCommentInfoMapper homeOrderCommentInfoMapper;
    @Autowired
    private HomeOrderComplaintComplaintMapper homeOrderComplaintComplaintMapper;
    @Autowired
    private IHomeBaseSettingsInfoService homeBaseSettingsInfoService;
    @Autowired
    private HomeServiceProviderManagementMapper homeServiceProviderManagementMapper;
    @Autowired
    private HomeOrderDeliveryInfoMapper homeOrderDeliveryInfoMapper;
    @Autowired
    private HomeCareElderlyPeopleInfoMapper homeCareElderlyPeopleInfoMapper;
    @Autowired
    private IHomeServiceProviderManagementService homeServiceProviderManagementService;
    @Autowired
    private IHomeBaseSettingsInfoService iHomeBaseSettingsInfoService;
    @Autowired
    private HomeServiceProviderWorkerMapper homeServiceProviderWorkerMapper;
    @Autowired
    private HomeServiceProjectMapper homeServiceProjectMapper;
    @Autowired
    private OrderPayComponent orderPayComponent;
    @Autowired
    private WxPayOrderInfoMapper wxPayOrderInfoMapper;
    @Autowired
    private WxPayComponent wxPayComponent;


    @Override
    @Transactional
    public synchronized TAjaxResult saveServiceOrder(HttpServletRequest request, HomeOrderServiceRequestVo requestVo) {

        BigDecimal totalFeeFront = requestVo.getTotalFee();
        Integer number = requestVo.getNumber();
        Integer payWay = requestVo.getPayWay();
        String openId = requestVo.getOpenId();
        Date currentDate = new Date();

        BigDecimal totalFeeBack = BigDecimal.ZERO;// 后端计算总价

        HomeServiceProject homeServiceProject = serviceProjectMapper.selectHomeServiceProjectById(requestVo.getServiceId());
        if (null != homeServiceProject) {

            if ("1".equals(homeServiceProject.getDelFlag())) {
                throw new ServiceException("该服务不存在");
            }
            BigDecimal price = homeServiceProject.getPrice();
            if (price.compareTo(requestVo.getPrice()) != 0) {
                throw new ServiceException("价格变动，请重新下单");
            }
            totalFeeBack = price.multiply(BigDecimal.valueOf(number));

            int compareTo = totalFeeFront.compareTo(totalFeeBack);

            if (compareTo != 0) {
                throw new ServiceException("总价核对错误，请刷新后重新下单！");
            }

        } else {
            throw new ServiceException("该服务不存在");
        }
        if (payWay == 2 && StrUtil.isBlank(openId)) {
            throw new ServiceException("微信支付openid必填！");
        }

        HomeBaseSettingsInfo homeBaseSettingsInfo = iHomeBaseSettingsInfoService.selectHomeBaseSettingsInfoById(1L);
        if (null == homeBaseSettingsInfo) {
            throw new ServiceException("HomeBaseSettingsInfo获取为null！");
        }

        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId("lryh");
        Long userId = SecurityUtils.getUserId();// 用户id
        String elderlyPeople = infoBySysUserId.getUserId();// 老人id

        // 添加订单主信息
        HomeOrderBaseInfo homeOrderBaseInfo = new HomeOrderBaseInfo();
        String orderId = OrderUtils.getCommonOrderCode(OrderUtils.BaseOrder);
        homeOrderBaseInfo.setId(orderId);
        homeOrderBaseInfo.setProviderId(requestVo.getServiceProviderId());
        homeOrderBaseInfo.setTotalFee(requestVo.getTotalFee());
        BigDecimal totalPrice = homeServiceProject.getPrice().multiply(BigDecimal.valueOf(number));// 总价
        homeOrderBaseInfo.setTotalPrice(totalPrice);// 总价
        UserDataInfoResult userInfo = SysUserUtils.getInfoBySysUserId("lryh");
        homeOrderBaseInfo.setElderlyPeopleId(userInfo.getUserId());
        homeOrderBaseInfo.setUserId(SecurityUtils.getUserId().toString());
        homeOrderBaseInfo.setType(HomeOrderBaseInfo.TYPE_SERVICE);
        homeOrderBaseInfo.setStatus(HomeOrderBaseInfo.STATUS_WAIT);
        homeOrderBaseInfo.setPayWay(requestVo.getPayWay());
        homeOrderBaseInfo.setCreateTime(currentDate);
        homeOrderBaseInfo.setUpdateTime(currentDate);
        homeOrderBaseInfo.setOrderWay(1);// 1:用户下单
        homeOrderBaseInfo.setGoodsTitle(homeServiceProject.getServiceProject());
        homeOrderBaseInfo.setShippingFee(BigDecimal.ZERO);
        homeOrderBaseInfo.setPackingFee(BigDecimal.ZERO);
        homeOrderBaseInfo.setUserName(requestVo.getName());
        homeOrderBaseInfo.setTotalNumber(number);

        BigDecimal serviceRatio = homeBaseSettingsInfo.getServiceRatio();// 服务的佣金比率
        BigDecimal commissionAmount = totalPrice.multiply(serviceRatio.divide(BigDecimal.valueOf(100)));
        homeOrderBaseInfo.setCommissionRate(serviceRatio);
        homeOrderBaseInfo.setCommissionAmount(commissionAmount);

        homeOrderBaseInfo.setTotalFee(requestVo.getTotalFee());
        JSONArray array = JSONUtil.createArray();
        JSONObject obj = JSONUtil.createObj();
        obj.set("name", requestVo.getServiceName());
        obj.set("img", homeServiceProject.getProjectImg());
        array.add(obj);
        homeOrderBaseInfo.setShowJson(array.toString());
        homeOrderBaseInfoMapper.insert(homeOrderBaseInfo);

        // 订单附表 详情表
        HomeOrderServiceInfo orderServiceInfo = new HomeOrderServiceInfo();
        orderServiceInfo.setOrderId(homeOrderBaseInfo.getId());
        orderServiceInfo.setServiceId(requestVo.getServiceId());
        orderServiceInfo.setServiceName(requestVo.getServiceName());
        orderServiceInfo.setImg(homeServiceProject.getProjectImg());
        orderServiceInfo.setCreateTime(new Date());
        orderServiceInfo.setPrice(requestVo.getPrice());
        orderServiceInfo.setTotalFee(requestVo.getTotalFee());
        orderServiceInfo.setNumber(requestVo.getNumber());
        orderServiceInfo.setUserId(userId);
        orderServiceInfo.setElderlyPeopleId(elderlyPeople);
        orderServiceInfoMapper.insert(orderServiceInfo);
        // 生成服务工单
        HomeOrderServiceWork homeOrderServiceWork = new HomeOrderServiceWork();
        homeOrderServiceWork.setId(OrderUtils.getCommonOrderCode(OrderUtils.serviceWorkOrder));
        homeOrderServiceWork.setOrderId(orderId);
        homeOrderServiceWork.setServiceId(requestVo.getServiceId());
        homeOrderServiceWork.setServiceName(requestVo.getServiceName());
        homeOrderServiceWork.setServiceType(0);// 服务
        homeOrderServiceWork.setPlaceOrderWay(1);// 用户

        homeOrderServiceWork.setElderlyPeopleId(userInfo.getUserId());
        homeOrderServiceWork.setName(requestVo.getName());
        homeOrderServiceWork.setPhone(requestVo.getPhone());
        homeOrderServiceWork.setStatus(1);// 1待指派人员
        homeOrderServiceWork.setReserveTime(requestVo.getReserveTime());
        homeOrderServiceWork.setServiceProviderId(requestVo.getServiceProviderId());
        homeOrderServiceWork.setServiceId(requestVo.getServiceId());
        homeOrderServiceWork.setServiceName(requestVo.getServiceName());
        homeOrderServiceWork.setServiceAddress(requestVo.getAddress());
        homeOrderServiceWork.setCreateBy(String.valueOf(userId));
        homeOrderServiceWork.setCreateTime(new Date());
        homeOrderServiceWork.setUpdateTime(new Date());
        homeOrderServiceWork.setUpdateBy(String.valueOf(userId));
        homeOrderServiceWork.setDelFlag("0");// 显示
        homeOrderServiceWork.setNumber(number);
        homeServiceWorkOrderMapper.insert(homeOrderServiceWork);


        String orderBaseInfoId = homeOrderBaseInfo.getId();
        TAjaxResult tAjaxResult = new TAjaxResult();
        // 1:余额付款 2：微信在线支付
        if (payWay == 1) {
            boolean b = orderPayComponent.balancePayment(elderlyPeople, totalFeeFront, orderBaseInfoId);// 余额支付是否成功
            if (!b) {// 如果不成功
                throw new ServiceException("余额支付失败，请重新下单！");
            }

            homeOrderBaseInfo.setStatus(HomeOrderBaseInfo.STATUS_PAID);
            homeOrderBaseInfo.setPayTime(currentDate);
            homeOrderBaseInfo.setPayWay(HomeOrderBaseInfo.PAY_WAY_BALANCE);
            homeOrderBaseInfoMapper.updateById(homeOrderBaseInfo);

            tAjaxResult.success(homeOrderBaseInfo);
        }

        if (payWay == 2) {// 微信支付
            WxPayOrderInfo wxPayOrderInfo = new WxPayOrderInfo();
            wxPayOrderInfo.setId(orderBaseInfoId);
            wxPayOrderInfo.setOpenId(openId);
            wxPayOrderInfo.setUserId(userInfo.getUserId());
            wxPayOrderInfo.setType(1);
            wxPayOrderInfo.setStatus(1);// 1:待付款
            wxPayOrderInfo.setAmount(totalFeeFront);
            wxPayOrderInfo.setCreateTime(currentDate);
            wxPayOrderInfo.setUpdateTime(currentDate);
            wxPayOrderInfoMapper.insert(wxPayOrderInfo);

            String remortIP = IpUtil.getRealIp(request);
            UnifiedorderRequest unifiedorderRequest = new UnifiedorderRequest();
            unifiedorderRequest.setOpenid(openId);
            unifiedorderRequest.setDescription(homeServiceProject.getServiceProject());
            unifiedorderRequest.setSpbillCreateIp(remortIP);
            int totalInt = totalFeeFront.multiply(BigDecimal.valueOf(100)).intValue();
            unifiedorderRequest.setTotal(totalInt);
            unifiedorderRequest.setOutTradeNo(homeOrderBaseInfo.getId());

            Object unifiedorder = wxPayComponent.unifiedorder(unifiedorderRequest);
            if (null == unifiedorder) {
                throw new ServiceException("微信统一下单异常！");
            }
            tAjaxResult.success(unifiedorder);

        }

        // 服务项目销量增加
        HomeServiceProject serviceProject = homeServiceProjectMapper.selectHomeServiceProjectById(homeOrderServiceWork.getServiceId());
        serviceProject.setSales(serviceProject.getSales() + requestVo.getNumber());
        homeServiceProjectMapper.updateHomeServiceProject(serviceProject);
        // 商家销量增加
        Long providerId = homeOrderBaseInfo.getProviderId();
        HomeServiceProviderManagement homeServiceProviderManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(providerId);
        homeServiceProviderManagement.setTotalSales(homeServiceProviderManagement.getTotalSales() + requestVo.getNumber());
        homeServiceProviderManagementMapper.updateHomeServiceProviderManagement(homeServiceProviderManagement);
        homeOrderBaseInfoMapper.updateById(homeOrderBaseInfo);

        return tAjaxResult;
    }

    @Transactional
    @Override
    public synchronized TAjaxResult saveServiceComboOrder(HttpServletRequest request, HomeOrderServiceComboRequestVo requestVo) {

        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId("lryh");
        Long userId = SecurityUtils.getUserId();// 用户id
        String username = SecurityUtils.getLoginUser().getSysUser().getNickName();
        String elderlyPeople = infoBySysUserId.getUserId();// 老人id
        String openId = requestVo.getOpenId();

        BigDecimal totalFeeFront = requestVo.getTotalFee();
        Integer number = requestVo.getNumber();// 购买份数
        Integer payWay = requestVo.getPayWay();
        Date currentDate = new Date();

        BigDecimal totalFeeBack = BigDecimal.ZERO;// 后端计算总价


        HomeServiceComboInfo serviceComboInfo = serviceComboInfoMapper.selectById(requestVo.getServiceComboId());
        if (null != serviceComboInfo) {

            if ("1".equals(serviceComboInfo.getDelFlag())) {
                throw new ServiceException("该服务套餐不存在");
            }
            BigDecimal price = serviceComboInfo.getPrice();
            if (price.compareTo(requestVo.getPrice()) != 0) {
                throw new ServiceException("价格变动，请重新下单");
            }

            totalFeeBack = price.multiply(BigDecimal.valueOf(number));

            if (totalFeeFront.compareTo(totalFeeBack) != 0) {
                throw new ServiceException("总价核对错误，请刷新后重新下单！");
            }

        } else {
            throw new ServiceException("该服务套餐不存在");
        }

        if (payWay == 2 && StrUtil.isBlank(openId)) {
            throw new ServiceException("微信支付，openid必填！");
        }

        HomeBaseSettingsInfo homeBaseSettingsInfo = homeBaseSettingsInfoService.selectHomeBaseSettingsInfoById(1L);
        BigDecimal serviceRatio = homeBaseSettingsInfo.getServiceRatio();// 佣金率

        // 添加订单主信息
        HomeOrderBaseInfo orderBaseInfo = new HomeOrderBaseInfo();
        String orderCode = OrderUtils.getCommonOrderCode(OrderUtils.BaseOrder);
        orderBaseInfo.setId(orderCode);
        orderBaseInfo.setProviderId(requestVo.getServiceProviderId());
        orderBaseInfo.setCreateTime(currentDate);
        orderBaseInfo.setUpdateTime(currentDate);

        BigDecimal total = serviceComboInfo.getPrice().multiply(BigDecimal.valueOf(number));

        orderBaseInfo.setTotalFee(total);
        orderBaseInfo.setTotalPrice(total);

        orderBaseInfo.setShippingFee(BigDecimal.ZERO);
        orderBaseInfo.setUserId(SecurityUtils.getUserId().toString());
        orderBaseInfo.setElderlyPeopleId(elderlyPeople);
        orderBaseInfo.setType(HomeOrderBaseInfo.TYPE_SERVICE_COMBO);
        orderBaseInfo.setStatus(HomeOrderBaseInfo.STATUS_WAIT);
        orderBaseInfo.setUserName(username);
        orderBaseInfo.setCommissionRate(serviceRatio);
        orderBaseInfo.setTotalNumber(number);
        orderBaseInfo.setGoodsTitle(serviceComboInfo.getComboName());

        orderBaseInfo.setCommissionAmount(total.multiply(serviceRatio.divide(new BigDecimal(100))));

        // 组装-showJson
        JSONArray array = JSONUtil.createArray();
        JSONObject obj = JSONUtil.createObj();
        obj.set("name", serviceComboInfo.getComboName());
        obj.set("img", serviceComboInfo.getImg());
        array.add(obj);

        QueryWrapper<HomeServiceComboDetails> queryWrapper = Wrappers.query();
        queryWrapper.eq("combo_id", serviceComboInfo.getId());
        queryWrapper.eq("del_flag", "0");
        List<HomeServiceComboDetails> homeServiceComboDetails = serviceComboDetailsMapper.selectList(queryWrapper);

        if (homeServiceComboDetails.size() < 1) {
            throw new ServiceException("套餐服务项为空，不能下单");
        }
        HomeOrderServiceComboInfo orderServiceComboInfo = new HomeOrderServiceComboInfo();
        orderServiceComboInfo.setServiceComboId(requestVo.getServiceComboId());
        orderServiceComboInfo.setServiceComboName(requestVo.getServiceComboName());
        orderServiceComboInfo.setOrderId(orderBaseInfo.getId());
        orderServiceComboInfo.setCreateTime(new Date());
        BigDecimal price = serviceComboInfo.getPrice();
        orderServiceComboInfo.setPrice(price);
        orderServiceComboInfo.setElderlyPeopleId(elderlyPeople);
        orderServiceComboInfo.setUserId(SecurityUtils.getUserId());
        orderServiceComboInfo.setProviderId(serviceComboInfo.getServiceProviderId());
        orderServiceComboInfo.setNumber(String.valueOf(number));
        orderServiceComboInfo.setCompletedStatus(HomeOrderServiceComboInfo.COMPLETED_STATUS_HAS);// 有剩余套餐未使用
        orderServiceComboInfo.setImg(serviceComboInfo.getImg());
        orderServiceComboInfo.setTotalPrice(price.multiply(BigDecimal.valueOf(number)));

        // 计算过期日期
        Integer validityPeriod = serviceComboInfo.getValidityPeriod();
        if (serviceComboInfo.getValidFlag() == 0) {// 如果是无期限给固定值
            validityPeriod = 100 * 365;
        }
        DateTime dateTime = DateUtil.offsetDay(new Date(), validityPeriod);
        orderServiceComboInfo.setExpiredDate(dateTime);

        orderServiceComboInfo.setAddress(requestVo.getAddress());
        orderServiceComboInfo.setPhone(requestVo.getPhone());
        orderServiceComboInfo.setName(requestVo.getName());
        orderServiceComboInfoMapper.insert(orderServiceComboInfo);

        for (HomeServiceComboDetails homeServiceComboDetail : homeServiceComboDetails) {
            HomeOrderServiceComboDetailsInfo homeOrderServiceComboDetailsInfo = new HomeOrderServiceComboDetailsInfo();
            homeOrderServiceComboDetailsInfo.setServiceId(homeServiceComboDetail.getServiceId());
            homeOrderServiceComboDetailsInfo.setServiceName(homeServiceComboDetail.getServiceName());
            homeOrderServiceComboDetailsInfo.setOrderId(orderBaseInfo.getId());
            homeOrderServiceComboDetailsInfo.setOrderComboId(orderServiceComboInfo.getId());
            homeOrderServiceComboDetailsInfo.setUserId(SecurityUtils.getUserId());
            homeOrderServiceComboDetailsInfo.setElderlyPeopleId(elderlyPeople);
            homeOrderServiceComboDetailsInfo.setPrice(homeServiceComboDetail.getPrice());
            homeOrderServiceComboDetailsInfo.setImg(homeServiceComboDetail.getImg());
            homeOrderServiceComboDetailsInfo.setDescribeMsg(homeServiceComboDetail.getDescribeMsg());
            Integer count = homeServiceComboDetail.getCount();
            homeOrderServiceComboDetailsInfo.setCount(count);
            homeOrderServiceComboDetailsInfo.setNumber(number);
            homeOrderServiceComboDetailsInfo.setCompletedCount(0);
            homeOrderServiceComboDetailsInfo.setTotalCount(count * number);
            homeOrderServiceComboDetailsInfo.setCompletedStatus(HomeOrderServiceComboDetailsInfo.COMPLETED_STATUS_HAS);
            homeOrderServiceComboDetailsInfo.setProviderId(homeServiceComboDetail.getServiceProviderId());
            homeOrderServiceComboDetailsInfo.setExpiredDate(dateTime);
            homeOrderServiceComboDetailsInfo.setCreateTime(currentDate);
            homeOrderServiceComboDetailsInfo.setUpdateTime(currentDate);
            homeOrderServiceComboDetailsInfo.setChargeMode(homeServiceComboDetail.getChargeMode());

            homeOrderServiceComboDetailsInfoMapper.insert(homeOrderServiceComboDetailsInfo);
        }

        String orderBaseInfoId = orderBaseInfo.getId();
        orderBaseInfo.setShowJson(array.toString());

        TAjaxResult tAjaxResult = new TAjaxResult();
        // 1:余额付款 2：微信在线支付
        if (payWay == 1) {
            boolean b = orderPayComponent.balancePayment(elderlyPeople, totalFeeFront, orderBaseInfoId);// 余额支付是否成功
            if (!b) {// 如果不成功
                throw new ServiceException("余额支付失败，请重新下单！");
            }

            orderBaseInfo.setStatus(HomeOrderBaseInfo.STATUS_PAID);// 服务套餐已付款
            orderBaseInfo.setPayTime(currentDate);
            orderBaseInfo.setPayWay(HomeOrderBaseInfo.PAY_WAY_BALANCE);
            orderBaseInfo.setAfterSalesStatus(0);// 未售后
            tAjaxResult.success(orderBaseInfo);
        }

        if (payWay == 2) {// 微信支付

            orderBaseInfo.setPayWay(HomeOrderBaseInfo.PAY_WAY_WECHAT);

            WxPayOrderInfo wxPayOrderInfo = new WxPayOrderInfo();
            wxPayOrderInfo.setId(orderBaseInfoId);
            wxPayOrderInfo.setOpenId(openId);
            wxPayOrderInfo.setUserId(elderlyPeople);
            wxPayOrderInfo.setType(1);
            wxPayOrderInfo.setStatus(1);// 1:待付款
            wxPayOrderInfo.setAmount(totalFeeFront);
            wxPayOrderInfo.setCreateTime(currentDate);
            wxPayOrderInfo.setUpdateTime(currentDate);
            wxPayOrderInfoMapper.insert(wxPayOrderInfo);

            String remortIP = IpUtil.getRealIp(request);
            UnifiedorderRequest unifiedorderRequest = new UnifiedorderRequest();
            unifiedorderRequest.setOpenid(openId);
            unifiedorderRequest.setDescription(serviceComboInfo.getComboName());
            unifiedorderRequest.setSpbillCreateIp(remortIP);
            int totalInt = totalFeeFront.multiply(BigDecimal.valueOf(100)).intValue();
            unifiedorderRequest.setTotal(totalInt);
            unifiedorderRequest.setOutTradeNo(orderBaseInfoId);

            Object unifiedorder = wxPayComponent.unifiedorder(unifiedorderRequest);
            if (null == unifiedorder) {
                throw new ServiceException("微信统一下单异常！");
            }
            tAjaxResult.success(unifiedorder);

        }

        // 保存订单
        homeOrderBaseInfoMapper.insert(orderBaseInfo);

        // 套餐更新销量
        serviceComboInfo.setSales(serviceComboInfo.getSales() + requestVo.getNumber());
        serviceComboInfoMapper.updateById(serviceComboInfo);

        // 商家销量增加
        Long providerId = requestVo.getServiceProviderId();
        HomeServiceProviderManagement homeServiceProviderManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(providerId);
        homeServiceProviderManagement.setTotalSales(homeServiceProviderManagement.getTotalSales() + requestVo.getNumber());
        homeServiceProviderManagementMapper.updateHomeServiceProviderManagement(homeServiceProviderManagement);

        return tAjaxResult;

    }

    @Override
    public TableDataInfo<SoldComboListVo> getSoldComboList(EtSoldComboListParam etSoldComboListParam) {

        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId(null);
        if (infoBySysUserId == null) {
            throw new ServiceException("获取商家信息异常！");
        }
        Long serviceProviderId = infoBySysUserId.getServiceProviderId();

        String elderlyPeopleName = etSoldComboListParam.getElderlyPeopleName();

        Page<HomeOrderServiceComboInfo> page = new Page<>(etSoldComboListParam.getPageNum(), etSoldComboListParam.getPageSize());
        page.setOptimizeCountSql(true);// 优化

        String sql = "  SELECT c.*,p.`name` AS elderly_people_name FROM t_home_order_service_combo_info c LEFT JOIN t_home_elderly_people_info p ON p.id = c.elderly_people_id WHERE c.provider_id = " + serviceProviderId;
        if (StrUtil.isNotBlank(elderlyPeopleName)) {
            sql = sql + " and p.name LIKE '%" + elderlyPeopleName + "%'";
        }
        sql = sql + " order by c.create_time desc";

        Page<SoldComboListVo> soldComboListNew = homeOrderServiceComboInfoMapper.getSoldComboListNew(page, sql);
        int total = (int) soldComboListNew.getTotal();

        TableDataInfo tableDataInfo = new TableDataInfo(soldComboListNew.getRecords(), total);
        tableDataInfo.setCode(200);
        return tableDataInfo;

    }

    @Override
    public int merchantOrders(MerchantOrdersRequestVo requestVo) {

        String orderId = requestVo.getOrderId();

        HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(orderId);

        if (homeOrderBaseInfo == null) {
            throw new ServiceException("订单信息不存在");
        }
        if (!requestVo.getServiceProviderId().equals(homeOrderBaseInfo.getProviderId())) {
            throw new ServiceException("接单商家信息不匹配");
        }
        if (homeOrderBaseInfo.getStatus() != 2) {
            throw new ServiceException("该订单已被接单");
        }
        homeOrderBaseInfo.setStatus(3);
        homeOrderBaseInfo.setUpdateTime(new Date());
        homeOrderBaseInfo.setReceiveTime(new Date());
        return homeOrderBaseInfoMapper.updateById(homeOrderBaseInfo);

    }

    @Override
    public HomeOrderBaseInfo getOrderServiceInfo(String id) {

        ServiceOrderResponseVo serviceOrderResponseVo = new ServiceOrderResponseVo();

        // 获取订单基础信息
        HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.getInfo(id);

        // 服务订单
        QueryWrapper<HomeOrderServiceInfo> orderServiceQuery = Wrappers.query();
        orderServiceQuery.eq("order_id", id);
        HomeOrderServiceInfo homeOrderServiceInfo = orderServiceInfoMapper.selectOne(orderServiceQuery);
        serviceOrderResponseVo.setOrderService(homeOrderServiceInfo);
        // 评论
        QueryWrapper<HomeOrderCommentInfo> orderCommentQuery = Wrappers.query();
        orderCommentQuery.eq("order_id", id);
        HomeOrderCommentInfo homeOrderCommentInfo = homeOrderCommentInfoMapper.selectOne(orderCommentQuery);
        if (homeOrderCommentInfo != null) {
            serviceOrderResponseVo.setOrderComment(homeOrderCommentInfo.getCommentContent());
            serviceOrderResponseVo.setCommentStatus(1);
        }

        // 退款
        QueryWrapper<HomeOrderRefundInfo> query4 = Wrappers.query();
        query4.eq("order_id", id);
        HomeOrderRefundInfo refundInfo = homeOrderRefundInfoMapper.selectOne(query4);
        if (null != refundInfo) {
            serviceOrderResponseVo.setRefundStatus(1);
        }

        // 投诉
        QueryWrapper<HomeOrderComplaint> query3 = Wrappers.query();
        query3.eq("order_id", id);
        HomeOrderComplaint complaint = homeOrderComplaintComplaintMapper.selectOne(query3);
        if (null != complaint) {
            serviceOrderResponseVo.setComplaintStatus(1);
        }

        // 服务工单
        QueryWrapper<HomeOrderServiceWork> workOrderQuery = Wrappers.query();
        workOrderQuery.eq("order_id", id);
        HomeOrderServiceWork homeOrderServiceWork = homeServiceWorkOrderMapper.selectOne(workOrderQuery);

        if (homeServiceWorkOrderMapper == null) {
            serviceOrderResponseVo.setWorkerOrder(new HomeOrderServiceWork());
        } else {
            serviceOrderResponseVo.setWorkerOrder(homeOrderServiceWork);
        }

        serviceOrderResponseVo.setCommentContent(homeOrderCommentInfo == null ? "" : homeOrderCommentInfo.getCommentContent());
        serviceOrderResponseVo.setComplaintResult(complaint == null ? "" : complaint.getDealWithResult());
        serviceOrderResponseVo.setRefundStatusLabel(refundInfo == null ? "" : String.valueOf(refundInfo.getAuditStatusLabel()));

        homeOrderBaseInfo.setSepObj(serviceOrderResponseVo);

        return homeOrderBaseInfo;
    }

    @Override
    @Transactional
    public String serviceComboOrderUse(HomeOrderServiceComboParam01 homeOrderServiceComboParam01) {

        Long orderComboDetailsId = homeOrderServiceComboParam01.getOrderComboDetailsId();

        HomeOrderServiceComboDetailsInfo detailsInfo = homeOrderServiceComboDetailsInfoMapper.selectById(orderComboDetailsId);

        Integer number = homeOrderServiceComboParam01.getNumber();
        Integer completedCount = detailsInfo.getCompletedCount();// 完成次数
        Integer totalCount = detailsInfo.getTotalCount();// 总次数
        int sumCount = number + completedCount;// 当次完成次数

        if (detailsInfo == null) {
            throw new ServiceException("未查询到该订单");
        }

        long time = detailsInfo.getExpiredDate().getTime();
        long currentTime = new Date().getTime();
        if (currentTime > time) {
            throw new ServiceException("该订单已过期");
        }
        if (sumCount > totalCount) {
            throw new ServiceException("当前使用数量超过了，剩余数量，下单失败！");
        }

        Date currentDate = new Date();

        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId("lryh");
        Long userId = SecurityUtils.getUserId();// 用户id
        String elderlyPeople = infoBySysUserId.getUserId();// 老人id

        // 库存增减
        detailsInfo.setCompletedCount(sumCount);// 完成次数
        detailsInfo.setUpdateTime(currentDate);
        if (sumCount == totalCount) {// 完成次数 等于总次数时状态更新为 completed_status 2
            detailsInfo.setCompletedStatus(HomeOrderServiceComboDetailsInfo.COMPLETED_STATUS_NONE);
        }

        QueryWrapper<HomeOrderServiceComboDetailsInfo> query = Wrappers.query();
        query.eq("completed_count", completedCount);
        query.eq("id", detailsInfo.getId());

        int update = homeOrderServiceComboDetailsInfoMapper.update(detailsInfo, query);
        if (update != 1) {
            throw new ServiceException("库存数据异常，请重试");
        }

        // 增加工单
        HomeOrderServiceWork homeOrderServiceWork = new HomeOrderServiceWork();
        homeOrderServiceWork.setId(OrderUtils.getCommonOrderCode(OrderUtils.serviceWorkOrder));
        homeOrderServiceWork.setServiceType(1);
        homeOrderServiceWork.setOrderId(detailsInfo.getOrderId());
        homeOrderServiceWork.setPlaceOrderWay(1);
        homeOrderServiceWork.setElderlyPeopleId(elderlyPeople);
        homeOrderServiceWork.setName(homeOrderServiceComboParam01.getName());
        homeOrderServiceWork.setStatus(1);
        homeOrderServiceWork.setNumber(number);

        homeOrderServiceWork.setServiceAddress(homeOrderServiceComboParam01.getAddress());
        homeOrderServiceWork.setPhone(homeOrderServiceComboParam01.getPhone());
        homeOrderServiceWork.setReserveTime(homeOrderServiceComboParam01.getReserveTime());

        homeOrderServiceWork.setServiceProviderId(detailsInfo.getProviderId());
        homeOrderServiceWork.setServiceId(detailsInfo.getServiceId());
        homeOrderServiceWork.setServiceName(detailsInfo.getServiceName());
        homeOrderServiceWork.setServiceComboId(detailsInfo.getOrderComboId());
        homeOrderServiceWork.setCreateTime(currentDate);
        homeOrderServiceWork.setDelFlag("0");

        // 套餐订单id套餐详情id
        homeOrderServiceWork.setOrderServiceComboId(detailsInfo.getOrderComboId());
        homeOrderServiceWork.setOrderServiceComboDetailsId(detailsInfo.getId());

        homeServiceWorkOrderMapper.insert(homeOrderServiceWork);

        return "1";
    }

    @Override
    public TableDataInfo getUserCombo(UserComboParam userComboParam) {

        Long pageNum = userComboParam.getPageNum();
        Long pageSize = userComboParam.getPageSize();
        Page<HomeOrderServiceComboInfo> page = new Page<>(pageNum, pageSize);
        page.setOptimizeCountSql(true);// 优化

        Long userId = SecurityUtils.getUserId();

        QueryWrapper<HomeOrderServiceComboInfo> queryWrapper = Wrappers.query();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("create_time");

        Page<HomeOrderServiceComboInfo> homeOrderServiceComboInfos = homeOrderServiceComboInfoMapper.selectPage(page, queryWrapper);

        long total = homeOrderServiceComboInfos.getTotal();
        List<HomeOrderServiceComboInfo> records = homeOrderServiceComboInfos.getRecords();
        List<HomeOrderServiceComboInfo> recordsNew = new ArrayList<>();

        for (HomeOrderServiceComboInfo record : records) {// 维护completed_status 完成状态  1:有剩余 2：已用完

            // 如果没有剩余则不需查询
            if (record.getCompletedStatus() == HomeOrderServiceComboInfo.COMPLETED_STATUS_NONE) {
                recordsNew.add(record);
                continue;
            }

            QueryWrapper<HomeOrderServiceComboDetailsInfo> query = Wrappers.query();
            query.eq("order_combo_id", record.getId());
            query.eq("completed_status", HomeOrderServiceComboDetailsInfo.COMPLETED_STATUS_HAS);
            Integer integer = homeOrderServiceComboDetailsInfoMapper.selectCount(query);

            if (integer.equals(0)) {// 如果没有查询到则
                record.setCompletedStatus(HomeOrderServiceComboInfo.COMPLETED_STATUS_NONE);
                homeOrderServiceComboInfoMapper.updateById(record);
            }
            recordsNew.add(record);

        }


        TableDataInfo tableDataInfo = new TableDataInfo(recordsNew, (int) total);
        tableDataInfo.setCode(200);

        return tableDataInfo;
    }

    @Override
    public TAjaxResult getUserComboDetails(UserComboDetailsParam userComboParam) {

        String comboOrderId = userComboParam.getComboOrderId();
        Long userId = SecurityUtils.getUserId();


        HomeOrderServiceComboInfo homeOrderServiceComboInfo = homeOrderServiceComboInfoMapper.selectById(comboOrderId);
        if (homeOrderServiceComboInfo == null) {
            throw new ServiceException("未查询该套餐订单！");
        }

        HomeServiceProviderManagement providerManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(homeOrderServiceComboInfo.getProviderId());
        if (providerManagement == null) {
            throw new ServiceException("未查询到该服务商信息！");
        }

        QueryWrapper<HomeOrderServiceComboDetailsInfo> queryWrapper = Wrappers.query();
        queryWrapper.eq("order_combo_id", comboOrderId);
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("create_time");

        List<HomeOrderServiceComboDetailsInfo> homeOrderServiceComboDetailsInfos = homeOrderServiceComboDetailsInfoMapper.selectList(queryWrapper);

        UserComboDetailsVo userComboDetailsVo = new UserComboDetailsVo();
        userComboDetailsVo.setComboDetailsInfos(homeOrderServiceComboDetailsInfos);
        userComboDetailsVo.setServiceComboName(homeOrderServiceComboInfo.getServiceComboName());
        userComboDetailsVo.setDescribeMsg(homeOrderServiceComboInfo.getDescribeMsg());
        userComboDetailsVo.setExpiredDate(homeOrderServiceComboInfo.getExpiredDate());
        userComboDetailsVo.setProviderName(providerManagement.getName());
        userComboDetailsVo.setTotalPrice(homeOrderServiceComboInfo.getTotalPrice());
        userComboDetailsVo.setServiceComboImg(homeOrderServiceComboInfo.getImg());

        return new TAjaxResult().success(userComboDetailsVo);
    }

    /**
     * 分页查询服务订单列表
     *
     * @param homeAdminOrderServiceInfoParam
     * @return
     */
    @Override
    public TableDataInfo getOrderServiceInfoAll(HomeAdminOrderServiceInfoParam homeAdminOrderServiceInfoParam) {
        Long pageNum = homeAdminOrderServiceInfoParam.getPageNum();
        Long pageSize = homeAdminOrderServiceInfoParam.getPageSize();
        Page<HomeAdminOrderServiceListVo> page = new Page<>(pageNum, pageSize);
        Page<HomeAdminOrderServiceListVo> orderServiceInfoAll = orderServiceInfoMapper.getOrderServiceInfoAll(page, homeAdminOrderServiceInfoParam.getWorkName(), homeAdminOrderServiceInfoParam.getBeginTime(), homeAdminOrderServiceInfoParam.getEndTime());
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(orderServiceInfoAll.getRecords());
        tableDataInfo.setTotal(orderServiceInfoAll.getTotal());
        tableDataInfo.setCode(200);
        tableDataInfo.setMsg("成功！");
        return tableDataInfo;
    }

    /**
     * 查询服务订单详情
     *
     * @param id
     * @return
     */
    @Override
    public HomeAdminOrderServiceInfoVo getOrderServiceDataInfo(String id) {

        // 服务列表详情订单返回值
        HomeAdminOrderServiceInfoVo adminOrderServiceInfoVo = new HomeAdminOrderServiceInfoVo();
        // 根据工单id获取订单服务信息
        HomeOrderServiceWork orderServiceWork = homeServiceWorkOrderMapper.selectById(id);
        // 根据订单id获取订单详情的基础信息
        String orderId = orderServiceWork.getOrderId();
        HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(orderId);
        // 订单评论信息
        QueryWrapper<HomeOrderCommentInfo> query = Wrappers.query();
        query.eq("order_id", orderId);
        HomeOrderCommentInfo homeOrderCommentInfo = homeOrderCommentInfoMapper.selectOne(query);
        if (null == homeOrderCommentInfo) {
            homeOrderCommentInfo = new HomeOrderCommentInfo();
        }
        BigDecimal price = new BigDecimal("0");
//        BigDecimal totalFee = new BigDecimal("0");
        // 并且判断是否是服务 获取进度条
        if (orderServiceWork.getServiceType() == 0) {// 服务
            JSONArray setpList = OrderUtils.setpList(homeOrderBaseInfo);
            adminOrderServiceInfoVo.setSetpList(setpList);
            QueryWrapper<HomeOrderServiceInfo> orderServiceQuery = Wrappers.query();
            orderServiceQuery.eq("order_id", orderId);
            HomeOrderServiceInfo homeOrderServiceInfo = orderServiceInfoMapper.selectOne(orderServiceQuery);
            price = homeOrderServiceInfo.getPrice();
//            totalFee = homeOrderBaseInfo.getTotalFee();
        } else {// 套餐 :不展示进度条以及单价为0
//            totalFee = homeOrderBaseInfo.getTotalFee();
        }
        // 组装工单信息
        HomeServiceOrderBaseInfoAdminVo homeServiceOrderBaseInfoAdminVo = new HomeServiceOrderBaseInfoAdminVo();
        BeanUtil.copyProperties(orderServiceWork, homeServiceOrderBaseInfoAdminVo);
        homeServiceOrderBaseInfoAdminVo.setPayWay(homeOrderBaseInfo.getPayWay());
        homeServiceOrderBaseInfoAdminVo.setPrice(price);
        homeServiceOrderBaseInfoAdminVo.setTotalFee(homeOrderBaseInfo.getTotalFee());
        homeServiceOrderBaseInfoAdminVo.setStatus(homeOrderBaseInfo.getStatus());// 订单状态
        adminOrderServiceInfoVo.setHomeServiceOrderBaseInfoAdminVo(homeServiceOrderBaseInfoAdminVo);
        adminOrderServiceInfoVo.setHomeOrderCommentInfo(homeOrderCommentInfo);
        HomeOrderServiceWorkAdminVo homeOrderServiceWorkAdminVo = new HomeOrderServiceWorkAdminVo();
        BeanUtil.copyProperties(orderServiceWork, homeOrderServiceWorkAdminVo);
        if (null != orderServiceWork.getWorkerId() && orderServiceWork.getWorkerId() != 0) {
            HomeServiceProviderWorker homeServiceProviderWorker = homeServiceProviderWorkerMapper.selectById(orderServiceWork.getWorkerId());
            homeOrderServiceWorkAdminVo.setWorkerName(homeServiceProviderWorker.getName());
        }
        adminOrderServiceInfoVo.setHomeOrderServiceWorkAdminVo(homeOrderServiceWorkAdminVo);

        return adminOrderServiceInfoVo;

    }

    /**
     * 服务商PC分页查询服务订单列表
     *
     * @param homeProviderOrderServiceInfoParam
     * @return
     */
    @Override
    public TableDataInfo getProviderOrderServiceInfoAll(HomeProviderOrderServiceInfoParam homeProviderOrderServiceInfoParam) {
        Long pageNum = homeProviderOrderServiceInfoParam.getPageNum();
        Long pageSize = homeProviderOrderServiceInfoParam.getPageSize();
        Page<HomeAdminOrderServiceListVo> page = new Page<>(pageNum, pageSize);
        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId(null);
        Page<HomeProviderOrderServiceListVo> orderServiceInfoAll = orderServiceInfoMapper.getProviderOrderServiceInfoAll(page, infoBySysUserId.getServiceProviderId(), homeProviderOrderServiceInfoParam.getId());


        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(orderServiceInfoAll.getRecords());
        tableDataInfo.setTotal(orderServiceInfoAll.getTotal());
        tableDataInfo.setCode(200);
        tableDataInfo.setMsg("成功！");
        return tableDataInfo;
    }

    @Override
    public TAjaxResult getComboDetailsByOrderId(ComboDetailsByOrderIdParam comboDetailsByOrderIdParam) {

        String orderId = comboDetailsByOrderIdParam.getOrderId();
        HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(orderId);

        ComboDetailsByOrderIdVo comboDetailsByOrderIdVo = new ComboDetailsByOrderIdVo();

        QueryWrapper<HomeOrderServiceComboDetailsInfo> query = Wrappers.query();
        query.eq("order_id", comboDetailsByOrderIdParam.getOrderId());
        query.orderByDesc("create_time");

        HomeServiceProviderManagement serviceProviderBySysUserId = homeServiceProviderManagementService.selectHomeServiceProviderManagementById(homeOrderBaseInfo.getProviderId());
        String providerName = "";// 商家名称
        if (serviceProviderBySysUserId == null) {
            providerName = "";
        } else {
            providerName = serviceProviderBySysUserId.getName();
        }

        HomeOrderBaseInfo01Vo homeOrderBaseInfo01Vo = new HomeOrderBaseInfo01Vo();
        BeanUtil.copyProperties(homeOrderBaseInfo, homeOrderBaseInfo01Vo);
        homeOrderBaseInfo01Vo.setProviderName(providerName);

        QueryWrapper<HomeOrderServiceComboInfo> queryWrapper = Wrappers.query();
        queryWrapper.eq("order_id", orderId);
        HomeOrderServiceComboInfo homeServiceComboInfo = homeOrderServiceComboInfoMapper.selectOne(queryWrapper);

        // 投诉
        QueryWrapper<HomeOrderComplaint> query3 = Wrappers.query();
        query3.eq("order_id", orderId);
        HomeOrderComplaint complaint = homeOrderComplaintComplaintMapper.selectOne(query3);
        if (null != complaint) {
            comboDetailsByOrderIdVo.setComplaintStatus(1);
        }
        // 退款
        QueryWrapper<HomeOrderRefundInfo> query4 = Wrappers.query();
        query4.eq("order_id", orderId);
        HomeOrderRefundInfo refundInfo = homeOrderRefundInfoMapper.selectOne(query4);
        if (null != refundInfo) {
            comboDetailsByOrderIdVo.setRefundStatus(1);
        }
        // 评论
        QueryWrapper<HomeOrderCommentInfo> query5 = Wrappers.query();
        query5.eq("order_id", orderId);
        HomeOrderCommentInfo homeOrderCommentInfo = homeOrderCommentInfoMapper.selectOne(query5);
        if (null != homeOrderCommentInfo) {
            comboDetailsByOrderIdVo.setCommentStatus(1);
        }

        List<HomeOrderServiceComboDetailsInfo> homeOrderServiceComboDetailsInfos = homeOrderServiceComboDetailsInfoMapper.selectList(query);

        comboDetailsByOrderIdVo.setHomeOrderBaseInfo01Vo(homeOrderBaseInfo01Vo);
        comboDetailsByOrderIdVo.setComboDetailsInfoList(homeOrderServiceComboDetailsInfos);
        comboDetailsByOrderIdVo.setHomeOrderServiceComboInfo(homeServiceComboInfo);
        comboDetailsByOrderIdVo.setCommentContent(homeOrderCommentInfo == null ? "" : homeOrderCommentInfo.getCommentContent());
        comboDetailsByOrderIdVo.setComplaintResult(complaint == null ? "" : complaint.getDealWithResult());
        comboDetailsByOrderIdVo.setRefundStatusLabel(refundInfo == null ? "" : String.valueOf(refundInfo.getAuditStatusLabel()));

        return new TAjaxResult<>().success(comboDetailsByOrderIdVo);
    }

    @Override
    public TableDataInfo geWorkList(ComboUseListVo comboUseListVo) {

        String comboId = comboUseListVo.getComboId();

        QueryWrapper<HomeOrderServiceWork> query = Wrappers.query();
        query.eq("service_combo_id", comboId);
        query.eq("del_flag", 0);
        query.orderByDesc("create_time");

        Page<HomeOrderServiceWork> page = new Page<>(comboUseListVo.getPageNum(), comboUseListVo.getPageSize());
        page.setOptimizeCountSql(true);// 优化

        Page<HomeOrderServiceWork> homeOrderServiceWorkPage = homeServiceWorkOrderMapper.selectPage(page, query);

        TableDataInfo<HomeOrderServiceWork> objectTableDataInfo = new TableDataInfo<>();
        objectTableDataInfo.setRows(homeOrderServiceWorkPage.getRecords());
        objectTableDataInfo.setTotal(homeOrderServiceWorkPage.getTotal());
        objectTableDataInfo.setCode(200);

        return objectTableDataInfo;
    }

    @Override
    public TAjaxResult getComboDetailsInfo(ComboDetailsInfoVo comboDetailsInfoVo) {

        String workId = comboDetailsInfoVo.getWorkId();

        HomeOrderServiceWork homeOrderServiceWork = homeServiceWorkOrderMapper.selectById(workId);

        if (homeOrderServiceWork == null) {
            homeOrderServiceWork = new HomeOrderServiceWork();
        }

        return new TAjaxResult<>().success(homeOrderServiceWork);
    }

    @Override
    public String sellComboToOrder(SellComboToOrdertVo sellComboToOrdertVo) {

        String comboDetailsId = sellComboToOrdertVo.getComboDetailsId();

        HomeOrderServiceComboDetailsInfo orderServiceComboDetailsInfo = homeOrderServiceComboDetailsInfoMapper.selectById(comboDetailsId);
        if (orderServiceComboDetailsInfo == null) {
            throw new ServiceException("订单未找到！");
        }

        Integer number = sellComboToOrdertVo.getNumber();
        Integer completedCount = orderServiceComboDetailsInfo.getCompletedCount();// 完成次数
        Integer totalCount = orderServiceComboDetailsInfo.getTotalCount();// 总次数
        int sumCount = number + completedCount;// 当次完成次数

        long time = orderServiceComboDetailsInfo.getExpiredDate().getTime();
        long currentTime = new Date().getTime();
        if (currentTime > time) {
            throw new ServiceException("该订单已过期");
        }
        if (sumCount > totalCount) {
            throw new ServiceException("当前使用数量超过了，剩余数量，下单失败！");
        }

        Date currentDate = new Date();

        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId("lryh");
        Long userId = SecurityUtils.getUserId();// 用户id
        String elderlyPeople = infoBySysUserId.getUserId();// 老人id

        // 库存增减
        orderServiceComboDetailsInfo.setCompletedCount(sumCount);// 完成次数
        orderServiceComboDetailsInfo.setUpdateTime(currentDate);
        if (sumCount == totalCount) {// 完成次数 等于总次数时状态更新为 completed_status 2
            orderServiceComboDetailsInfo.setCompletedStatus(HomeOrderServiceComboDetailsInfo.COMPLETED_STATUS_NONE);
        }


        QueryWrapper<HomeOrderServiceComboDetailsInfo> query = Wrappers.query();
        query.eq("completed_count", completedCount);
        query.eq("id", orderServiceComboDetailsInfo.getId());

        int update = homeOrderServiceComboDetailsInfoMapper.update(orderServiceComboDetailsInfo, query);
        if (update != 1) {
            throw new ServiceException("库存数据异常，请重试");
        }

        // 增加工单
        HomeOrderServiceWork homeOrderServiceWork = new HomeOrderServiceWork();
        homeOrderServiceWork.setId(OrderUtils.getCommonOrderCode(OrderUtils.serviceWorkOrder));
        homeOrderServiceWork.setServiceType(1);
        homeOrderServiceWork.setOrderId(orderServiceComboDetailsInfo.getOrderId());
        homeOrderServiceWork.setPlaceOrderWay(2);
        homeOrderServiceWork.setElderlyPeopleId(elderlyPeople);
        homeOrderServiceWork.setName(sellComboToOrdertVo.getName());
        homeOrderServiceWork.setStatus(1);
        homeOrderServiceWork.setRemark(sellComboToOrdertVo.getRemark());
        homeOrderServiceWork.setChargeMode(orderServiceComboDetailsInfo.getChargeMode());

        homeOrderServiceWork.setServiceAddress(sellComboToOrdertVo.getAddress());
        homeOrderServiceWork.setPhone(sellComboToOrdertVo.getPhone());
        homeOrderServiceWork.setReserveTime(sellComboToOrdertVo.getReserveTime());

        homeOrderServiceWork.setServiceProviderId(orderServiceComboDetailsInfo.getProviderId());
        homeOrderServiceWork.setServiceId(orderServiceComboDetailsInfo.getServiceId());
        homeOrderServiceWork.setServiceName(orderServiceComboDetailsInfo.getServiceName());
        homeOrderServiceWork.setServiceComboId(orderServiceComboDetailsInfo.getOrderComboId());
        homeOrderServiceWork.setCreateTime(currentDate);
        homeOrderServiceWork.setDelFlag("0");

        // 套餐订单id套餐详情id
        homeOrderServiceWork.setOrderServiceComboId(orderServiceComboDetailsInfo.getOrderComboId());
        homeOrderServiceWork.setOrderServiceComboDetailsId(orderServiceComboDetailsInfo.getId());

        homeServiceWorkOrderMapper.insert(homeOrderServiceWork);

        return "1";
    }

    @Override
    public List<HomeOrderServiceComboDetailsInfo> getOrderServiceComboInfoList(String comboId, String serviceName) {

        if (StrUtil.isBlank(comboId)) {
            throw new ServiceException("套餐id必填！");
        }

        QueryWrapper<HomeOrderServiceComboDetailsInfo> query = Wrappers.query();
        query.eq("order_combo_id", comboId);
        if (StrUtil.isNotBlank(serviceName)) {
            query.like("service_name", serviceName);
        }

        query.orderByDesc("create_time");

        List<HomeOrderServiceComboDetailsInfo> detailsInfoList = homeOrderServiceComboDetailsInfoMapper.selectList(query);

        return detailsInfoList;
    }

    @Override
    public TAjaxResult getUserInfo(String comboDetailsId) {

        HomeOrderServiceComboDetailsInfo homeOrderServiceComboDetailsInfo = homeOrderServiceComboDetailsInfoMapper.selectById(comboDetailsId);
        if (homeOrderServiceComboDetailsInfo == null) {
            throw new ServiceException("该订单不存在");
        }

        String elderlyPeopleId = homeOrderServiceComboDetailsInfo.getElderlyPeopleId();

        ElderlyPeopleInfo elderlyPeopleInfo = homeCareElderlyPeopleInfoMapper.selectElderlyPeopleInfoById(elderlyPeopleId);
        if (elderlyPeopleInfo == null) {
            throw new ServiceException("获取老人基本信息异常！");
        }

        ProviderGetUserInfoVo providerGetUserInfoVo = new ProviderGetUserInfoVo();
        BeanUtil.copyProperties(elderlyPeopleInfo, providerGetUserInfoVo);
        providerGetUserInfoVo.setResidueCount(homeOrderServiceComboDetailsInfo.getTotalCount() - homeOrderServiceComboDetailsInfo.getCompletedCount());

        JSONObject jsonObject = new JSONObject();
        jsonObject.putOnce("serviceName", homeOrderServiceComboDetailsInfo.getServiceName());
        jsonObject.putOnce("chargeMode", homeOrderServiceComboDetailsInfo.getChargeMode());
        jsonObject.putOnce("price", homeOrderServiceComboDetailsInfo.getPrice());

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(jsonObject);
        providerGetUserInfoVo.setServiceList(jsonArray);

        return new TAjaxResult<>().success(providerGetUserInfoVo);
    }


}
