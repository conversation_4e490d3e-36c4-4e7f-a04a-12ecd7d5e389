package com.ruoyi.homecare.serviceProviders.service.impl;

import java.util.List;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProjectProviderIndexMapper;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProjectProviderIndex;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProjectProviderIndexService;

/**
 * 服务项目和服务商关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
@Service
public class HomeServiceProjectProviderIndexServiceImpl implements IHomeServiceProjectProviderIndexService {
    @Autowired
    private HomeServiceProjectProviderIndexMapper homeServiceProjectProviderIndexMapper;

    /**
     * 查询服务项目和服务商关联
     *
     * @param id 服务项目和服务商关联主键
     * @return 服务项目和服务商关联
     */
    @Override
    public HomeServiceProjectProviderIndex selectHomeServiceProjectProviderIndexById(Long id) {
        return homeServiceProjectProviderIndexMapper.selectHomeServiceProjectProviderIndexById(id);
    }

    /**
     * 查询服务项目和服务商关联列表
     *
     * @param homeServiceProjectProviderIndex 服务项目和服务商关联
     * @return 服务项目和服务商关联
     */
    @Override
    public List<HomeServiceProjectProviderIndex> selectHomeServiceProjectProviderIndexList(HomeServiceProjectProviderIndex homeServiceProjectProviderIndex) {
        return homeServiceProjectProviderIndexMapper.selectHomeServiceProjectProviderIndexList(homeServiceProjectProviderIndex);
    }

    /**
     * 新增服务项目和服务商关联
     *
     * @param homeServiceProjectProviderIndex 服务项目和服务商关联
     * @return 结果
     */
    @Override
    public int insertHomeServiceProjectProviderIndex(HomeServiceProjectProviderIndex homeServiceProjectProviderIndex) {
        homeServiceProjectProviderIndex.setCreateTime(DateUtils.getNowDate());
        return homeServiceProjectProviderIndexMapper.insertHomeServiceProjectProviderIndex(homeServiceProjectProviderIndex);
    }

    /**
     * 修改服务项目和服务商关联
     *
     * @param homeServiceProjectProviderIndex 服务项目和服务商关联
     * @return 结果
     */
    @Override
    public int updateHomeServiceProjectProviderIndex(HomeServiceProjectProviderIndex homeServiceProjectProviderIndex) {
        homeServiceProjectProviderIndex.setUpdateTime(DateUtils.getNowDate());
        return homeServiceProjectProviderIndexMapper.updateHomeServiceProjectProviderIndex(homeServiceProjectProviderIndex);
    }

    /**
     * 批量删除服务项目和服务商关联
     *
     * @param ids 需要删除的服务项目和服务商关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeServiceProjectProviderIndexByIds(Long[] ids) {
        return homeServiceProjectProviderIndexMapper.deleteHomeServiceProjectProviderIndexByIds(ids);
    }

    /**
     * 删除服务项目和服务商关联信息
     *
     * @param id 服务项目和服务商关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeServiceProjectProviderIndexById(Long id) {
        return homeServiceProjectProviderIndexMapper.deleteHomeServiceProjectProviderIndexById(id);
    }


    /**
     * 根据服务项目id获取服务商id
     *
     * @param projectId
     * @return
     */
    @Override
    public JSONArray getByProjectIdList(Long projectId) {
        return homeServiceProjectProviderIndexMapper.getByProjectIdList(projectId);
    }
}
