package com.ruoyi.homecare.elderlyPeople.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.homecare.utils.DictUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 老人健康管理信息对象 t_elderly_people_health_management
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@ApiModel(value = "老人健康管理信息")
public class ElderlyPeopleHealthManagement extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 慢性病
     */
    @Excel(name = "慢性病")
    @ApiModelProperty(value = "慢性病")
    private String chronicDiseases;

    /**
     * 失能情况
     */
    @Excel(name = "失能情况")
    @ApiModelProperty(value = "失能情况")
    private String incapacitationCondition;
    @ApiModelProperty(value = "失能情况label")
    private String incapacitationConditionStr;

    /**
     * 血型
     */
    @Excel(name = "血型")
    @ApiModelProperty(value = "血型")
    private String bloodType;
    @ApiModelProperty(value = "血型label")
    private String bloodTypeStr;

    /**
     * 残疾情况
     */
    @Excel(name = "残疾情况")
    @ApiModelProperty(value = "残疾情况")
    private String disabilitySituation;
    @ApiModelProperty(value = "残疾情况label")
    private String disabilitySituationStr;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 老人基础信息id
     */
    @Excel(name = "老人基础信息id")
    @ApiModelProperty(value = "老人基础信息id")
    private String userId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getChronicDiseases() {
        return chronicDiseases;
    }

    public void setChronicDiseases(String chronicDiseases) {
        this.chronicDiseases = chronicDiseases;
    }

    public String getIncapacitationCondition() {
        return incapacitationCondition;
    }

    public void setIncapacitationCondition(String incapacitationCondition) {
        this.incapacitationCondition = incapacitationCondition;
    }

    public String getBloodType() {
        return bloodType;
    }

    public void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    public String getIncapacitationConditionStr() {
        return DictUtils.selectDictLabel("incapacitation_condition", this.incapacitationCondition);
    }

    public void setIncapacitationConditionStr(String incapacitationConditionStr) {
        this.incapacitationConditionStr = incapacitationConditionStr;
    }

    public String getBloodTypeStr() {
        return DictUtils.selectDictLabel("blood_type", this.bloodType);
    }

    public void setBloodTypeStr(String bloodTypeStr) {
        this.bloodTypeStr = bloodTypeStr;
    }

    public String getDisabilitySituationStr() {
        return DictUtils.selectDictLabel("disability_situation", this.disabilitySituation);
    }

    public void setDisabilitySituationStr(String disabilitySituationStr) {
        this.disabilitySituationStr = disabilitySituationStr;
    }

    public String getDisabilitySituation() {
        return disabilitySituation;
    }

    public void setDisabilitySituation(String disabilitySituation) {
        this.disabilitySituation = disabilitySituation;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("chronicDiseases", getChronicDiseases())
                .append("incapacitationCondition", getIncapacitationCondition())
                .append("bloodType", getBloodType())
                .append("disabilitySituation", getDisabilitySituation())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .append("userId", getUserId())
                .toString();
    }
}
