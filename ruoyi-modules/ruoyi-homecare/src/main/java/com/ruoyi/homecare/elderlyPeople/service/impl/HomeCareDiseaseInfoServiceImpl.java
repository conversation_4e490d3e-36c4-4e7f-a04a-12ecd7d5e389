package com.ruoyi.homecare.elderlyPeople.service.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.homecare.elderlyPeople.domain.DiseaseInfo;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareDiseaseInfoMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareDiseaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 疾病类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@Service
public class HomeCareDiseaseInfoServiceImpl implements IHomeCareDiseaseInfoService {
    @Autowired
    private HomeCareDiseaseInfoMapper diseaseInfoMapper;

    /**
     * 查询疾病类型
     *
     * @param id 疾病类型主键
     * @return 疾病类型
     */
    @Override
    public DiseaseInfo selectDiseaseInfoById(String id) {
        return diseaseInfoMapper.selectDiseaseInfoById(id);
    }

    /**
     * 查询疾病类型列表
     *
     * @param diseaseInfo 疾病类型
     * @return 疾病类型
     */
    @Override
    public List<DiseaseInfo> selectDiseaseInfoList(DiseaseInfo diseaseInfo) {
        return diseaseInfoMapper.selectDiseaseInfoList(diseaseInfo);
    }

    /**
     * 新增疾病类型
     *
     * @param diseaseInfo 疾病类型
     * @return 结果
     */
    @Override
    public int insertDiseaseInfo(DiseaseInfo diseaseInfo) {
        diseaseInfo.setCreateTime(DateUtils.getNowDate());
        return diseaseInfoMapper.insertDiseaseInfo(diseaseInfo);
    }

    /**
     * 修改疾病类型
     *
     * @param diseaseInfo 疾病类型
     * @return 结果
     */
    @Override
    public int updateDiseaseInfo(DiseaseInfo diseaseInfo) {
        diseaseInfo.setUpdateTime(DateUtils.getNowDate());
        return diseaseInfoMapper.updateDiseaseInfo(diseaseInfo);
    }

    /**
     * 批量删除疾病类型
     *
     * @param ids 需要删除的疾病类型主键
     * @return 结果
     */
    @Override
    public int deleteDiseaseInfoByIds(String[] ids) {
        return diseaseInfoMapper.deleteDiseaseInfoByIds(ids);
    }

    /**
     * 删除疾病类型信息
     *
     * @param id 疾病类型主键
     * @return 结果
     */
    @Override
    public int deleteDiseaseInfoById(String id) {
        return diseaseInfoMapper.deleteDiseaseInfoById(id);
    }


    /**
     * 获取所有的疾病类型组成数组
     *
     * @return
     */
    @Override
    public List<JSONObject> getGroupConcatDisease() {
        return diseaseInfoMapper.getGroupConcatDisease();
    }

    /**
     * 通过类型获取疾病数值
     *
     * @param
     * @return
     */
    @Override
    public List<JSONObject> getDiseaseInfoJson() {
        List<JSONObject> list = diseaseInfoMapper.getGroupConcatDisease();
        List<JSONObject> data = new ArrayList<>();
        if (!list.isEmpty()) {
            for (JSONObject object : list) {
                JSONObject entries = new JSONObject();
                List<JSONObject> json = diseaseInfoMapper.getDiseaseInfoJson(object.getStr("type"));
                entries.set("label", object.getStr("remark"));
                entries.set("score", "");
                entries.set("arr", json);
                data.add(entries);
            }
        }

        return data;
    }
}
