package com.ruoyi.homecare.securityguard.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardBledata;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 血压信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Mapper
public interface SecurityGuardBledataMapper extends MPJBaseMapper<SecurityGuardBledata> {
    /**
     * 查询血压信息
     *
     * @param id 血压信息主键
     * @return 血压信息
     */
    public SecurityGuardBledata selectSecurityGuardBledataById(Long id);

    /**
     * 查询血压信息列表
     *
     * @param securityGuardBledata 血压信息
     * @return 血压信息集合
     */
    public List<SecurityGuardBledata> selectSecurityGuardBledataList(SecurityGuardBledata securityGuardBledata);

    /**
     * 新增血压信息
     *
     * @param securityGuardBledata 血压信息
     * @return 结果
     */
    public int insertSecurityGuardBledata(SecurityGuardBledata securityGuardBledata);

    /**
     * 修改血压信息
     *
     * @param securityGuardBledata 血压信息
     * @return 结果
     */
    public int updateSecurityGuardBledata(SecurityGuardBledata securityGuardBledata);

    /**
     * 删除血压信息
     *
     * @param id 血压信息主键
     * @return 结果
     */
    public int deleteSecurityGuardBledataById(Long id);

    /**
     * 批量删除血压信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityGuardBledataByIds(Long[] ids);
}
