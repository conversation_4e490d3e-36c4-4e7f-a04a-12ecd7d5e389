package com.ruoyi.homecare.elderlyPeople.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.homecare.elderlyPeople.domain.QuestionnaireRecords;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareQuestionnaireRecordsMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareQuestionnaireRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 调查问卷历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@Service
public class HomeCareQuestionnaireRecordsServiceImpl implements IHomeCareQuestionnaireRecordsService {
    @Autowired
    private HomeCareQuestionnaireRecordsMapper questionnaireRecordsMapper;

    /**
     * 查询调查问卷历史
     *
     * @param id 调查问卷历史主键
     * @return 调查问卷历史
     */
    @Override
    public QuestionnaireRecords selectQuestionnaireRecordsById(Long id) {
        QuestionnaireRecords questionnaireRecords = questionnaireRecordsMapper.selectQuestionnaireRecordsById(id);
        if (!questionnaireRecords.getJsonData().isEmpty()) {
            JSONArray objects = JSONUtil.parseArray(questionnaireRecords.getJsonData());
            System.out.println(objects);
            questionnaireRecords.setList(objects);
        }
        return questionnaireRecords;
    }

    /**
     * 查询调查问卷历史列表
     *
     * @param questionnaireRecords 调查问卷历史
     * @return 调查问卷历史
     */
    @Override
    public List<QuestionnaireRecords> selectQuestionnaireRecordsList(QuestionnaireRecords questionnaireRecords) {
        return questionnaireRecordsMapper.selectQuestionnaireRecordsList(questionnaireRecords);
    }

    /**
     * 新增调查问卷历史
     *
     * @param questionnaireRecords 调查问卷历史
     * @return 结果
     */
    @Override
    public int insertQuestionnaireRecords(QuestionnaireRecords questionnaireRecords) {
        questionnaireRecords.setCreateTime(DateUtils.getNowDate());
        return questionnaireRecordsMapper.insertQuestionnaireRecords(questionnaireRecords);
    }

    /**
     * 修改调查问卷历史
     *
     * @param questionnaireRecords 调查问卷历史
     * @return 结果
     */
    @Override
    public int updateQuestionnaireRecords(QuestionnaireRecords questionnaireRecords) {
        questionnaireRecords.setUpdateTime(DateUtils.getNowDate());
        return questionnaireRecordsMapper.updateQuestionnaireRecords(questionnaireRecords);
    }

    /**
     * 批量删除调查问卷历史
     *
     * @param ids 需要删除的调查问卷历史主键
     * @return 结果
     */
    @Override
    public int deleteQuestionnaireRecordsByIds(Long[] ids) {
        return questionnaireRecordsMapper.deleteQuestionnaireRecordsByIds(ids);
    }

    /**
     * 删除调查问卷历史信息
     *
     * @param id 调查问卷历史主键
     * @return 结果
     */
    @Override
    public int deleteQuestionnaireRecordsById(Long id) {
        return questionnaireRecordsMapper.deleteQuestionnaireRecordsById(id);
    }
}
