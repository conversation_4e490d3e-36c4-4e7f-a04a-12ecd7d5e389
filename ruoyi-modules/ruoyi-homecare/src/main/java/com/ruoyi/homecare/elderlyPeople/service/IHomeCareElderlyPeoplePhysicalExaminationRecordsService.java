package com.ruoyi.homecare.elderlyPeople.service;

import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeoplePhysicalExaminationRecords;

import java.util.List;

/**
 * 老人体检记录Service接口
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
public interface IHomeCareElderlyPeoplePhysicalExaminationRecordsService {
    /**
     * 查询老人体检记录
     *
     * @param id 老人体检记录主键
     * @return 老人体检记录
     */
    public ElderlyPeoplePhysicalExaminationRecords selectElderlyPeoplePhysicalExaminationRecordsById(Long id);

    /**
     * 查询老人体检记录列表
     *
     * @param elderlyPeoplePhysicalExaminationRecords 老人体检记录
     * @return 老人体检记录集合
     */
    public List<ElderlyPeoplePhysicalExaminationRecords> selectElderlyPeoplePhysicalExaminationRecordsList(ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords);

    /**
     * 新增老人体检记录
     *
     * @param elderlyPeoplePhysicalExaminationRecords 老人体检记录
     * @return 结果
     */
    public int insertElderlyPeoplePhysicalExaminationRecords(ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords);

    /**
     * 修改老人体检记录
     *
     * @param elderlyPeoplePhysicalExaminationRecords 老人体检记录
     * @return 结果
     */
    public int updateElderlyPeoplePhysicalExaminationRecords(ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords);

    /**
     * 批量删除老人体检记录
     *
     * @param ids 需要删除的老人体检记录主键集合
     * @return 结果
     */
    public int deleteElderlyPeoplePhysicalExaminationRecordsByIds(Long[] ids);

    /**
     * 删除老人体检记录信息
     *
     * @param id 老人体检记录主键
     * @return 结果
     */
    public int deleteElderlyPeoplePhysicalExaminationRecordsById(Long id);
}
