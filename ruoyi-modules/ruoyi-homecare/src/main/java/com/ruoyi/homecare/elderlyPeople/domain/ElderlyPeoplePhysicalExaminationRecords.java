package com.ruoyi.homecare.elderlyPeople.domain;

import cn.hutool.json.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 老人体检记录对象 t_elderly_people_physical_examination_records
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@ApiModel(value = "老人体检记录")
public class ElderlyPeoplePhysicalExaminationRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 老人id
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id")
    private String userId;

    /**
     * 体检医院
     */
    @Excel(name = "体检医院")
    @ApiModelProperty(value = "体检医院")
    private String medicalExaminationHospital;

    /**
     * 体检时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "体检时间")
    @Excel(name = "体检时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date physicalExaminationTime;

    /**
     * 下次体检时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "下次体检时间")
    @Excel(name = "下次体检时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nextPhysicalExaminationTime;

    /**
     * 体检结果图片
     */
    @Excel(name = "体检结果图片")
    @ApiModelProperty(value = "体检结果图片")
    private String physicalExaminationImg;

    private JSONArray imgArr;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMedicalExaminationHospital() {
        return medicalExaminationHospital;
    }

    public void setMedicalExaminationHospital(String medicalExaminationHospital) {
        this.medicalExaminationHospital = medicalExaminationHospital;
    }

    public Date getPhysicalExaminationTime() {
        return physicalExaminationTime;
    }

    public void setPhysicalExaminationTime(Date physicalExaminationTime) {
        this.physicalExaminationTime = physicalExaminationTime;
    }

    public Date getNextPhysicalExaminationTime() {
        return nextPhysicalExaminationTime;
    }

    public void setNextPhysicalExaminationTime(Date nextPhysicalExaminationTime) {
        this.nextPhysicalExaminationTime = nextPhysicalExaminationTime;
    }

    public String getPhysicalExaminationImg() {
        return physicalExaminationImg;
    }

    public void setPhysicalExaminationImg(String physicalExaminationImg) {
        this.physicalExaminationImg = physicalExaminationImg;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public JSONArray getImgArr() {
        return imgArr;
    }

    public void setImgArr(JSONArray imgArr) {
        this.imgArr = imgArr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("medicalExaminationHospital", getMedicalExaminationHospital())
                .append("physicalExaminationTime", getPhysicalExaminationTime())
                .append("nextPhysicalExaminationTime", getNextPhysicalExaminationTime())
                .append("physicalExaminationImg", getPhysicalExaminationImg())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
