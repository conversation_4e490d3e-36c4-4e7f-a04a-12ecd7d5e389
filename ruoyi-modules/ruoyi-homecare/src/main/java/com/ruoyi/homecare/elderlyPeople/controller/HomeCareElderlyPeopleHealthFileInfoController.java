package com.ruoyi.homecare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthFileInfo;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleHealthFileInfoService;
import com.ruoyi.homecare.utils.ReflectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家老人健康档案Controller
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@RestController
@RequestMapping("/homeCareElderlyPeopleHealthFileInfo")
@Api(value = "健康管理-居家老人健康档案Controller", tags = {"健康管理-居家老人健康档案"})
public class HomeCareElderlyPeopleHealthFileInfoController extends BaseController {
    @Autowired
    private IHomeCareElderlyPeopleHealthFileInfoService elderlyPeopleHealthFileInfoService;


    /**
     * 查询居家老人健康档案列表
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleHealthFileInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询居家老人健康档案")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo) {
        startPage();
        List<ElderlyPeopleHealthFileInfo> list = elderlyPeopleHealthFileInfoService.selectElderlyPeopleHealthFileInfoList(elderlyPeopleHealthFileInfo);
        return getDataTable(list);
    }

    /**
     * 导出居家老人健康档案列表
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleHealthFileInfo:export")
    @Log(platform = "2", title = "居家老人健康档案", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出居家老人健康档案列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo) {
        List<ElderlyPeopleHealthFileInfo> list = elderlyPeopleHealthFileInfoService.selectElderlyPeopleHealthFileInfoList(elderlyPeopleHealthFileInfo);
        ExcelUtil<ElderlyPeopleHealthFileInfo> util = new ExcelUtil<ElderlyPeopleHealthFileInfo>(ElderlyPeopleHealthFileInfo.class);
        util.exportExcel(response, list, "居家老人健康档案数据");
    }

    /**
     * 健康管理模块中的健康信息
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleHealthFileInfo:query")
    @ApiOperation(value = "获取健康管理模块中的健康档案")
    @GetMapping("/getDataInfoByUserId")
    public TAjaxResult<ElderlyPeopleHealthFileInfo> getDataInfoByUserId(String userId) throws ClassNotFoundException {
        ElderlyPeopleHealthFileInfo dataInfo = elderlyPeopleHealthFileInfoService.getDataInfoByUserId(userId);
        if (null != dataInfo) {
            dataInfo = (ElderlyPeopleHealthFileInfo) ReflectionUtils.nullifyStrings(dataInfo);
        }
        return new TAjaxResult<ElderlyPeopleHealthFileInfo>(dataInfo);
    }

    /**
     * 获取居家老人健康档案详细信息
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleHealthFileInfo:query")
    @ApiOperation(value = "获取居家老人健康档案详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(elderlyPeopleHealthFileInfoService.selectElderlyPeopleHealthFileInfoById(id));
    }


    @Log(platform = "2", title = "保存居家老人健康档案", businessType = BusinessType.INSERT)
    @ApiOperation(value = "保存居家老人健康档案")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo) {
        if (null == elderlyPeopleHealthFileInfo.getId() || elderlyPeopleHealthFileInfo.getId() == 0) {
            return toAjax(elderlyPeopleHealthFileInfoService.insertElderlyPeopleHealthFileInfo(elderlyPeopleHealthFileInfo));
        } else {
            return toAjax(elderlyPeopleHealthFileInfoService.updateElderlyPeopleHealthFileInfo(elderlyPeopleHealthFileInfo));
        }
    }


    /**
     * 新增居家老人健康档案
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleHealthFileInfo:add")
    @Log(platform = "2", title = "居家老人健康档案", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增居家老人健康档案")
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo) {
        return toAjax(elderlyPeopleHealthFileInfoService.insertElderlyPeopleHealthFileInfo(elderlyPeopleHealthFileInfo));
    }

    /**
     * 修改居家老人健康档案
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleHealthFileInfo:edit")
    @Log(platform = "2", title = "居家老人健康档案", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改居家老人健康档案")
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo) {
        return toAjax(elderlyPeopleHealthFileInfoService.updateElderlyPeopleHealthFileInfo(elderlyPeopleHealthFileInfo));
    }

    /**
     * 删除居家老人健康档案
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleHealthFileInfo:remove")
    @Log(platform = "2", title = "居家老人健康档案", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除居家老人健康档案")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(elderlyPeopleHealthFileInfoService.deleteElderlyPeopleHealthFileInfoByIds(ids));
    }


}
