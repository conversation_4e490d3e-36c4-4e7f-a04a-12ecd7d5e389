package com.ruoyi.homecare.serviceProviders.service;

import java.util.List;

import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex;

/**
 * 服务商和社区关联Service接口
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
public interface IHomeServiceProviderCommunityIndexService {
    /**
     * 查询服务商和社区关联
     *
     * @param id 服务商和社区关联主键
     * @return 服务商和社区关联
     */
    public HomeServiceProviderCommunityIndex selectHomeServiceProviderCommunityIndexById(Long id);

    /**
     * 查询服务商和社区关联列表
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 服务商和社区关联集合
     */
    public List<HomeServiceProviderCommunityIndex> selectHomeServiceProviderCommunityIndexList(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex);

    /**
     * 新增服务商和社区关联
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 结果
     */
    public int insertHomeServiceProviderCommunityIndex(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex);

    /**
     * 修改服务商和社区关联
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 结果
     */
    public int updateHomeServiceProviderCommunityIndex(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex);

    /**
     * 批量删除服务商和社区关联
     *
     * @param ids 需要删除的服务商和社区关联主键集合
     * @return 结果
     */
    public int deleteHomeServiceProviderCommunityIndexByIds(Long[] ids);

    /**
     * 删除服务商和社区关联信息
     *
     * @param id 服务商和社区关联主键
     * @return 结果
     */
    public int deleteHomeServiceProviderCommunityIndexById(Long id);

    /**
     * 通过服务商Id删除数据
     *
     * @param baseId
     * @return
     */
    int deleteByServiceId(Long baseId);

    String getListIdByServiceId(Long baseId);
}
