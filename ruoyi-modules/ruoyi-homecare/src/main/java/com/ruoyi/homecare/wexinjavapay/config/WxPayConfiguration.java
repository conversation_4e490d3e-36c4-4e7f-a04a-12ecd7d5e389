package com.ruoyi.homecare.wexinjavapay.config;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.ruoyi.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/7
 **/
@Configuration
@ConditionalOnClass(WxPayService.class)
@EnableConfigurationProperties(WxPayProperties.class)
@AllArgsConstructor
public class WxPayConfiguration {
    private WxPayProperties properties;

    @Bean
    @ConditionalOnMissingBean
    public WxPayService wxService() {
        WxPayConfig payConfig = new WxPayConfig();
        System.out.println(this.properties.getAppId());
        System.out.println(this.properties.getMchId());
        System.out.println(this.properties.getMchKey());
        System.out.println(this.properties.getApiV3Key());

        payConfig.setAppId("wxb7125f4f902fb0fe");
        payConfig.setMchId("1628275932");
        payConfig.setMchKey(this.properties.getMchKey());
        payConfig.setApiV3Key(this.properties.getApiV3Key());
        payConfig.setKeyPath("classpath:wxcertificate/apiclient_cert.p12");
        payConfig.setPrivateKeyPath("classpath:wxcertificate/apiclient_key.pem");
        payConfig.setPrivateCertPath("classpath:wxcertificate/apiclient_cert.pem");

        // windows
//    payConfig.setKeyPath("E:\\xmbs\\elderly\\service\\jar\\package\\wxLicisense\\apiclient_cert.p12");
//    payConfig.setPrivateKeyPath("E:\\xmbs\\elderly\\service\\jar\\package\\wxLicisense\\apiclient_key.pem");
//    payConfig.setPrivateCertPath("E:\\xmbs\\elderly\\service\\jar\\package\\wxLicisense\\apiclient_cert.pem");

//    payConfig.setSubAppId(StringUtils.trimToNull(this.properties.getSubAppId()));
//    payConfig.setSubMchId(StringUtils.trimToNull(this.properties.getSubMchId()));
//    payConfig.setKeyPath(StringUtils.trimToNull(this.properties.getKeyPath()));

        // 可以指定是否使用沙箱环境
        payConfig.setUseSandboxEnv(false);

        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }

}
