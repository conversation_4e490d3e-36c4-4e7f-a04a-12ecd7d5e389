package com.ruoyi.homecare.securityguard.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.homecare.securityguard.enums.DeviceTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 设备信息对象 t_security_guard_device_info
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
@TableName("t_security_guard_device_info")
public class SecurityGuardDeviceInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @Excel(name = "类型名称")
    private String securityGuardType;

    /**
     * 设备型号
     */
    @Excel(name = "设备型号")
    private String modelNumber;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 生产厂商,关联t_security_guard_manufacturer_info主键id
     */
    private Long manufacturerId;

    @Excel(name = "生产厂商")
    @TableField(exist = false)
    private String manufacturerName;

    public String getSecurityGuardType() {
        return securityGuardType;
    }

    public void setSecurityGuardType(String securityGuardType) {
        this.securityGuardType = securityGuardType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
        if (type != null) {
            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(type);
            this.setSecurityGuardType(deviceTypeEnum.getDescription());
        }
    }

    public String getManufacturerName() {
        return manufacturerName;
    }

    public void setManufacturerName(String manufacturerName) {
        this.manufacturerName = manufacturerName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public void setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(Long manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("modelNumber", getModelNumber())
                .append("name", getName())
                .append("manufacturerId", getManufacturerId())
                .append("manufacturerName", getManufacturerName())
                .toString();
    }
}
