package com.ruoyi.homecare.elderlyPeople.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleFamilyInfo;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleFamilyInfoMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleFamilyInfoService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 老人家属信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Service
public class HomeCareElderlyPeopleFamilyInfoServiceImpl implements IHomeCareElderlyPeopleFamilyInfoService {
    @Autowired
    private HomeCareElderlyPeopleFamilyInfoMapper elderlyPeopleFamilyInfoMapper;

    /**
     * 查询老人家属信息
     *
     * @param id 老人家属信息主键
     * @return 老人家属信息
     */
    @Override
    public ElderlyPeopleFamilyInfo selectElderlyPeopleFamilyInfoById(String id) {
        return elderlyPeopleFamilyInfoMapper.selectElderlyPeopleFamilyInfoById(id);
    }

    /**
     * 查询老人家属信息列表
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 老人家属信息
     */
    @Override
    public List<ElderlyPeopleFamilyInfo> selectElderlyPeopleFamilyInfoList(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        return elderlyPeopleFamilyInfoMapper.selectElderlyPeopleFamilyInfoList(elderlyPeopleFamilyInfo);
    }

    /**
     * 新增老人家属信息
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 结果
     */
    @Override
    public int insertElderlyPeopleFamilyInfo(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        elderlyPeopleFamilyInfo.setCreateTime(DateUtils.getNowDate());
        String id = IdUtils.fastSimpleUUID();
        elderlyPeopleFamilyInfo.setId(id);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        elderlyPeopleFamilyInfo.setCreateBy(String.valueOf(loginUser.getUserid()));
        return elderlyPeopleFamilyInfoMapper.insertElderlyPeopleFamilyInfo(elderlyPeopleFamilyInfo);
    }

    /**
     * 修改老人家属信息
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 结果
     */
    @Override
    public int updateElderlyPeopleFamilyInfo(ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        elderlyPeopleFamilyInfo.setUpdateTime(DateUtils.getNowDate());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        elderlyPeopleFamilyInfo.setUpdateBy(String.valueOf(loginUser.getUserid()));
        return elderlyPeopleFamilyInfoMapper.updateElderlyPeopleFamilyInfo(elderlyPeopleFamilyInfo);
    }

    /**
     * 批量删除老人家属信息
     *
     * @param ids 需要删除的老人家属信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleFamilyInfoByIds(String[] ids) {
        return elderlyPeopleFamilyInfoMapper.deleteElderlyPeopleFamilyInfoByIds(ids);
    }

    /**
     * 删除老人家属信息信息
     *
     * @param id 老人家属信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleFamilyInfoById(String id) {
        return elderlyPeopleFamilyInfoMapper.deleteElderlyPeopleFamilyInfoById(id);
    }
}
