package com.ruoyi.homecare.elderlyPeople.controller;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeoplePhysicalExaminationRecords;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeoplePhysicalExaminationRecordsService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.MimetypesFileTypeMap;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 居家老人体检记录Controller
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@RestController
@RequestMapping("/homeCarePhysicalExaminationRecords")
@Api(tags = "健康管理-居家老人体检记录", value = "健康管理-居家老人体检记录Controller")
public class HomeCareElderlyPeoplePhysicalExaminationRecordsController extends BaseController {
    @Autowired
    private IHomeCareElderlyPeoplePhysicalExaminationRecordsService elderlyPeoplePhysicalExaminationRecordsService;

    @Autowired
    private RemoteFileService remoteFileService;

    // 获取流文件
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询居家老人体检记录列表
     */
    //@RequiresPermissions("elderlyPeople:physicalExaminationRecords:list")
    @ApiOperation(value = "查询居家老人体检记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = false, dataTypeClass = String.class),
    })
    @GetMapping("/list")
    public TableDataInfo list(ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords) {
        startPage();
        List<ElderlyPeoplePhysicalExaminationRecords> list = elderlyPeoplePhysicalExaminationRecordsService.selectElderlyPeoplePhysicalExaminationRecordsList(elderlyPeoplePhysicalExaminationRecords);
        return getDataTable(list);
    }

    /**
     * 导出居家老人体检记录列表
     */
    //@RequiresPermissions("elderlyPeople:physicalExaminationRecords:export")
    @Log(platform = "2", title = "居家老人体检记录", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出居家老人体检记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords) {
        List<ElderlyPeoplePhysicalExaminationRecords> list = elderlyPeoplePhysicalExaminationRecordsService.selectElderlyPeoplePhysicalExaminationRecordsList(elderlyPeoplePhysicalExaminationRecords);
        ExcelUtil<ElderlyPeoplePhysicalExaminationRecords> util = new ExcelUtil<ElderlyPeoplePhysicalExaminationRecords>(ElderlyPeoplePhysicalExaminationRecords.class);
        util.exportExcel(response, list, "居家老人体检记录数据");
    }

    /**
     * 获取居家老人体检记录详细信息
     */
    //@RequiresPermissions("elderlyPeople:physicalExaminationRecords:query")
    @ApiOperation(value = "获取居家老人体检记录详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        ElderlyPeoplePhysicalExaminationRecords info = elderlyPeoplePhysicalExaminationRecordsService.selectElderlyPeoplePhysicalExaminationRecordsById(id);
        return AjaxResult.success(info);
    }

    /**
     * 新增居家老人体检记录
     */
    //@RequiresPermissions("elderlyPeople:physicalExaminationRecords:add")
    @Log(platform = "2", title = "居家老人体检记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增居家老人体检记录")
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords) {
        return toAjax(elderlyPeoplePhysicalExaminationRecordsService.insertElderlyPeoplePhysicalExaminationRecords(elderlyPeoplePhysicalExaminationRecords));
    }

    /**
     * 修改居家老人体检记录
     */
    //@RequiresPermissions("elderlyPeople:physicalExaminationRecords:edit")
    @Log(platform = "2", title = "居家老人体检记录", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改居家老人体检记录")
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords) {
        return toAjax(elderlyPeoplePhysicalExaminationRecordsService.updateElderlyPeoplePhysicalExaminationRecords(elderlyPeoplePhysicalExaminationRecords));
    }

    /**
     * 删除居家老人体检记录
     */
    //@RequiresPermissions("elderlyPeople:physicalExaminationRecords:remove")
    @Log(platform = "2", title = "居家老人体检记录", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除居家老人体检记录")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(elderlyPeoplePhysicalExaminationRecordsService.deleteElderlyPeoplePhysicalExaminationRecordsByIds(ids));
    }

    /**
     * 文件上传
     */
    @Log(platform = "2", title = "文件上传", businessType = BusinessType.UPDATE)
    @PostMapping("/filesUpload")
    @ApiOperation(value = "文件上传")
    public AjaxResult filesUpload(@RequestPart @RequestParam("files") MultipartFile[] files) throws Exception {
        if (files.length > 0) {
            List<JSONObject> list = new ArrayList<>();
            String url = "";
            for (MultipartFile file : files) {
                JSONObject json = new JSONObject();
                R<SysFile> fileResult = remoteFileService.upload(file);
                if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData())) {
                    return AjaxResult.error("文件服务异常，请联系管理员");
                }
                url += fileResult.getData().getUrl() + ",";
                if (multipartFileToFile(file)) {
                    json.set("type", "1");
                } else {
                    json.set("type", "2");
                }
                json.set("url", fileResult.getData().getUrl());
                list.add(json);
            }
            return AjaxResult.success().put("data", list);
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    /**
     * 判断是否是图片类型
     *
     * @param file
     * @return
     */
    public boolean isImage(File file) {
        MimetypesFileTypeMap mtftp = new MimetypesFileTypeMap();
        mtftp.addMimeTypes("image png tif jpg jpeg bmp");
        String mimetype = mtftp.getContentType(file);
        String type = mimetype.split("/")[0];
        return "image".equals(type);
    }

    /**
     * MultipartFile 转 File
     *
     * @param file
     * @throws Exception
     */
    public boolean multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if ("".equals(file) || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        // 返回file类型文件
//        return toFile;
        return isImage(toFile);
    }

}
