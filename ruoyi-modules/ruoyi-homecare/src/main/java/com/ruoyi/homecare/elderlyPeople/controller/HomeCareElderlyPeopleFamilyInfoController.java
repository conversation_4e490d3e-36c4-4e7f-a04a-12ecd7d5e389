package com.ruoyi.homecare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleFamilyInfo;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleFamilyInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家老人家属信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@RestController
@RequestMapping("/homeCareFamilyInfo")
@Api(value = "老人管理-居家老人家属信息controller", tags = "老人管理-居家老人家属信息")
public class HomeCareElderlyPeopleFamilyInfoController extends BaseController {
    @Autowired
    private IHomeCareElderlyPeopleFamilyInfoService elderlyPeopleFamilyInfoService;

    /**
     * 查询居家老人家属信息列表
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:list")
    @ApiOperation(value = "居家老人家属信息列表")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        startPage();
        List<ElderlyPeopleFamilyInfo> list = elderlyPeopleFamilyInfoService.selectElderlyPeopleFamilyInfoList(elderlyPeopleFamilyInfo);
        return getDataTable(list);
    }

    /**
     * 导出居家老人家属信息列表
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:export")
    @Log(platform = "2", title = "居家老人家属信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出居家老人家属信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        List<ElderlyPeopleFamilyInfo> list = elderlyPeopleFamilyInfoService.selectElderlyPeopleFamilyInfoList(elderlyPeopleFamilyInfo);
        ExcelUtil<ElderlyPeopleFamilyInfo> util = new ExcelUtil<ElderlyPeopleFamilyInfo>(ElderlyPeopleFamilyInfo.class);
        util.exportExcel(response, list, "居家老人家属信息数据");
    }

    /**
     * 获取居家老人家属信息详细信息
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:query")
    @ApiOperation(value = "获取居家老人家属信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(elderlyPeopleFamilyInfoService.selectElderlyPeopleFamilyInfoById(id));
    }

    /**
     * 新增居家老人家属信息
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:add")
    @Log(platform = "2", title = "居家老人家属信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增居家老人家属信息")
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        return toAjax(elderlyPeopleFamilyInfoService.insertElderlyPeopleFamilyInfo(elderlyPeopleFamilyInfo));
    }

    /**
     * 修改居家老人家属信息
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:edit")
    @Log(platform = "2", title = "居家老人家属信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改居家老人家属信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        return toAjax(elderlyPeopleFamilyInfoService.updateElderlyPeopleFamilyInfo(elderlyPeopleFamilyInfo));
    }

    /**
     * 删除居家老人家属信息
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:remove")
    @Log(platform = "2", title = "居家老人家属信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除居家老人家属信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(elderlyPeopleFamilyInfoService.deleteElderlyPeopleFamilyInfoByIds(ids));
    }
}
