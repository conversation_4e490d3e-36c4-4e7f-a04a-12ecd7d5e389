package com.ruoyi.homecare.securityguard.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardDeviceInfo;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardDeviceInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备信息Controller
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
@RestController
@RequestMapping("/device")
public class SecurityGuardDeviceInfoController extends BaseController {
    @Autowired
    private ISecurityGuardDeviceInfoService securityGuardDeviceInfoService;

    /**
     * 查询设备信息列表
     */
    @RequiresPermissions("securityguard:device:list")
    @GetMapping("/list")
    public TableDataInfo list(SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        startPage();
        List<SecurityGuardDeviceInfo> list = securityGuardDeviceInfoService.selectSecurityGuardDeviceInfoList(securityGuardDeviceInfo);
        return getDataTable(list);
    }

    /**
     * 导出设备信息列表
     */
    @RequiresPermissions("securityguard:device:export")
    @Log(title = "设备信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        List<SecurityGuardDeviceInfo> list = securityGuardDeviceInfoService.selectSecurityGuardDeviceInfoList(securityGuardDeviceInfo);
        ExcelUtil<SecurityGuardDeviceInfo> util = new ExcelUtil<SecurityGuardDeviceInfo>(SecurityGuardDeviceInfo.class);
        util.exportExcel(response, list, "设备信息数据");
    }

    /**
     * 获取设备信息详细信息
     */
    @RequiresPermissions("securityguard:device:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardDeviceInfoService.selectSecurityGuardDeviceInfoById(id));
    }

    /**
     * 新增设备信息
     */
    @RequiresPermissions("securityguard:device:add")
    @Log(title = "设备信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        return toAjax(securityGuardDeviceInfoService.insertSecurityGuardDeviceInfo(securityGuardDeviceInfo));
    }

    /**
     * 修改设备信息
     */
    @RequiresPermissions("securityguard:device:edit")
    @Log(title = "设备信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityGuardDeviceInfo securityGuardDeviceInfo) {
        return toAjax(securityGuardDeviceInfoService.updateSecurityGuardDeviceInfo(securityGuardDeviceInfo));
    }

    /**
     * 删除设备信息
     */
    @RequiresPermissions("securityguard:device:remove")
    @Log(title = "设备信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardDeviceInfoService.deleteSecurityGuardDeviceInfoByIds(ids));
    }
}
