package com.ruoyi.homecare.serviceProviders.domain.vo;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AppServiceProviderManagementVo {


    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty("名称")
    private String name;

    /**
     * 性质
     */
    @Excel(name = "商家性质")
    @ApiModelProperty("商家性质")
    private String nature;

    /**
     * 营业时间
     */
    @Excel(name = "营业时间")
    @ApiModelProperty("营业时间")
    private String businessHours;

    /**
     * 固定电话
     */
    @Excel(name = "固定电话")
    @ApiModelProperty("固定电话")
    private String fixedTelephone;

    /**
     * 服务范围
     */
    @Excel(name = "服务范围")
    @ApiModelProperty("服务范围")
    private String serviceType;

    /**
     * 服务范围
     */
    @Excel(name = "服务范围")
    @ApiModelProperty("服务范围")
    private String serviceTypeLabel;

    /**
     * 标签
     */
    @Excel(name = "标签")
    @ApiModelProperty("标签")
    private String label;

    /**
     * 社区id
     */
    @Excel(name = "社区id")
    @ApiModelProperty("社区id")
    private String communityId;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 已售量
     */
    @Excel(name = "已售量")
    @ApiModelProperty("已售量")
    private Integer totalSales;

    /**
     * 好评数
     */
    @Excel(name = "好评数")
    @ApiModelProperty("好评数")
    private Integer praiseNum;

    /**
     * 好评率
     */
    @Excel(name = "好评率")
    @ApiModelProperty("好评率")
    private String praiseRate;

    /**
     * 商家图片
     */
    @Excel(name = "商家图片")
    @ApiModelProperty("商家图片")
    private String merchantsPhotos;

    /**
     * 是否营业：0正在营业，1暂停休息
     */
    @Excel(name = "是否营业：0正在营业，1暂停休息")
    @ApiModelProperty("是否营业：0正在营业，1暂停休息")
    private String serviceState;

}
