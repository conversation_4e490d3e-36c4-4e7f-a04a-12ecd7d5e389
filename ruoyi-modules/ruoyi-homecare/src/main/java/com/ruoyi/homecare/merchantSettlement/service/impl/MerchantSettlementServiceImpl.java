package com.ruoyi.homecare.merchantSettlement.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.homecare.goodsOrder.mapper.HomeOrderBaseInfoMapper;
import com.ruoyi.homecare.merchantSettlement.domain.MerchantSettlement;
import com.ruoyi.homecare.merchantSettlement.mapper.MerchantSettlementMapper;
import com.ruoyi.homecare.merchantSettlement.service.MerchantSettlementService;
import com.ruoyi.homecare.merchantSettlement.vo.MSOrderInfoResVo;
import com.ruoyi.homecare.merchantSettlement.vo.MerchantSettlementListReqVo;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProviderManagementMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class MerchantSettlementServiceImpl implements MerchantSettlementService {
    @Autowired
    private MerchantSettlementMapper merchantSettlementMapper;

    @Autowired
    private HomeServiceProviderManagementMapper homeServiceProviderManagementMapper;

    @Autowired
    private HomeOrderBaseInfoMapper homeOrderBaseInfoMapper;

    @Override
    public List<MerchantSettlement> selectList(MerchantSettlementListReqVo reqVo) {
        QueryWrapper<MerchantSettlement> query = Wrappers.query();
        if (reqVo.getServiceProviderId() != null) {
            query.eq("service_provider_id", reqVo.getServiceProviderId());
        }
        if (StrUtil.isNotEmpty(reqVo.getStartMonth()) && StrUtil.isNotEmpty(reqVo.getEndMonth())) {
            query.ge("bill_month", reqVo.getStartMonth());
            query.le("bill_month", reqVo.getEndMonth());
        }
        return merchantSettlementMapper.selectList(query);
    }

    @Override
    public List<MSOrderInfoResVo> getOrderList(MerchantSettlementListReqVo reqVo) {
        List<MSOrderInfoResVo> list = merchantSettlementMapper.getOrderList(reqVo);
        return list;
    }

    @Override
    public void generateMonthBill() {
        // 查询所有商家列表

        HomeServiceProviderManagement param = new HomeServiceProviderManagement();
        List<HomeServiceProviderManagement> managements = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementList(param);
        for (HomeServiceProviderManagement management : managements) {
            Long serviceProviderId = management.getId();
            // 通过id和月份查询需要生成的账单

//            String lastMonth = DateUtil.format(DateUtil.lastMonth(), "yyyyMM");
            String lastMonth = DateUtil.format(new Date(), "yyyyMM");
//          homeOrderBaseInfoMapper.selectList();

            // 通过商家id以及月份查询该月账单情况
            MerchantSettlement merchantSettlement = merchantSettlementMapper.selectServiceProviderMonthCountInfo(serviceProviderId, lastMonth);
            merchantSettlement.setBillMonth(lastMonth);
            merchantSettlement.setBillName(management.getName() + lastMonth + "月账单");
            merchantSettlement.setCreateTime(new Date());
            merchantSettlement.setStatus(0);
            merchantSettlement.setServiceProviderId(management.getId());
            merchantSettlement.setServiceProviderName(management.getName());
            if (merchantSettlement.getRefundAmount() == null) {
                merchantSettlement.setRefundAmount(new BigDecimal("0"));
            }
            if (merchantSettlement.getBusinessNum() != null && merchantSettlement.getBusinessNum() > 0) {
                merchantSettlementMapper.insert(merchantSettlement);
            }
        }


    }

    @Override
    public int settlementBill(String id, BigDecimal settlementAmount) {
        MerchantSettlement merchantSettlement = merchantSettlementMapper.selectById(id);
        if (settlementAmount != null) {
            merchantSettlement.setSettlementAmount(settlementAmount);
        } else {
            merchantSettlement.setSettlementAmount(merchantSettlement.getCommissionAmount());
        }
        merchantSettlement.setStatus(1);
        return merchantSettlementMapper.updateById(merchantSettlement);
    }


}
