package com.ruoyi.homecare.elderlyPeople.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeoplePhysicalExaminationRecords;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeoplePhysicalExaminationRecordsMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeoplePhysicalExaminationRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 老人体检记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Service
public class HomeCareElderlyPeoplePhysicalExaminationRecordsServiceImpl implements IHomeCareElderlyPeoplePhysicalExaminationRecordsService {
    @Autowired
    private HomeCareElderlyPeoplePhysicalExaminationRecordsMapper elderlyPeoplePhysicalExaminationRecordsMapper;

    /**
     * 查询老人体检记录
     *
     * @param id 老人体检记录主键
     * @return 老人体检记录
     */
    @Override
    public ElderlyPeoplePhysicalExaminationRecords selectElderlyPeoplePhysicalExaminationRecordsById(Long id) {
        ElderlyPeoplePhysicalExaminationRecords info = elderlyPeoplePhysicalExaminationRecordsMapper.selectElderlyPeoplePhysicalExaminationRecordsById(id);
        if (null != info.getPhysicalExaminationImg() && !info.getPhysicalExaminationImg().isEmpty()) {
            JSONArray objects = JSONUtil.parseArray(info.getPhysicalExaminationImg());
            info.setImgArr(objects);
        } else {
            JSONArray objects = new JSONArray();
            info.setImgArr(objects);
        }

        return info;
    }

    /**
     * 查询老人体检记录列表
     *
     * @param elderlyPeoplePhysicalExaminationRecords 老人体检记录
     * @return 老人体检记录
     */
    @Override
    public List<ElderlyPeoplePhysicalExaminationRecords> selectElderlyPeoplePhysicalExaminationRecordsList(ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords) {
        return elderlyPeoplePhysicalExaminationRecordsMapper.selectElderlyPeoplePhysicalExaminationRecordsList(elderlyPeoplePhysicalExaminationRecords);
    }

    /**
     * 新增老人体检记录
     *
     * @param elderlyPeoplePhysicalExaminationRecords 老人体检记录
     * @return 结果
     */
    @Override
    public int insertElderlyPeoplePhysicalExaminationRecords(ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords) {
        elderlyPeoplePhysicalExaminationRecords.setCreateTime(DateUtils.getNowDate());
        JSONArray imgArr = elderlyPeoplePhysicalExaminationRecords.getImgArr();
        if (null != imgArr && !imgArr.isEmpty()) {
            elderlyPeoplePhysicalExaminationRecords.setPhysicalExaminationImg(imgArr.toString());
        }
        return elderlyPeoplePhysicalExaminationRecordsMapper.insertElderlyPeoplePhysicalExaminationRecords(elderlyPeoplePhysicalExaminationRecords);
    }

    /**
     * 修改老人体检记录
     *
     * @param elderlyPeoplePhysicalExaminationRecords 老人体检记录
     * @return 结果
     */
    @Override
    public int updateElderlyPeoplePhysicalExaminationRecords(ElderlyPeoplePhysicalExaminationRecords elderlyPeoplePhysicalExaminationRecords) {
        elderlyPeoplePhysicalExaminationRecords.setUpdateTime(DateUtils.getNowDate());
        JSONArray imgArr = elderlyPeoplePhysicalExaminationRecords.getImgArr();
        if (null != imgArr && !imgArr.isEmpty()) {
            elderlyPeoplePhysicalExaminationRecords.setPhysicalExaminationImg(imgArr.toString());
        }
        return elderlyPeoplePhysicalExaminationRecordsMapper.updateElderlyPeoplePhysicalExaminationRecords(elderlyPeoplePhysicalExaminationRecords);
    }

    /**
     * 批量删除老人体检记录
     *
     * @param ids 需要删除的老人体检记录主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeoplePhysicalExaminationRecordsByIds(Long[] ids) {
        return elderlyPeoplePhysicalExaminationRecordsMapper.deleteElderlyPeoplePhysicalExaminationRecordsByIds(ids);
    }

    /**
     * 删除老人体检记录信息
     *
     * @param id 老人体检记录主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeoplePhysicalExaminationRecordsById(Long id) {
        return elderlyPeoplePhysicalExaminationRecordsMapper.deleteElderlyPeoplePhysicalExaminationRecordsById(id);
    }
}
