package com.ruoyi.homecare.securityguard.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.homecare.securityguard.enums.PeopleDeviceTypeEnum;

import java.io.Serializable;

/**
 * 老人与设备关联信息对象 t_security_guard_devcie_people_info
 *
 * <AUTHOR>
 * @date 2023-02-06
 */
@TableName("t_security_guard_devcie_people_info")
public class SecurityGuardDevciePeopleInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备id,关联t_security_guard_device_info
     */
    private Long deviceId;

    @Excel(name = "设备名称")
    @TableField(exist = false)
    private String deviceName;

    @Excel(name = "设备型号")
    @TableField(exist = false)
    private String deviceModelNumber;

    /**
     * 老人id,关联t_home_elderly_people_info
     */
    private String peopleId;

    @Excel(name = "老人名称")
    @TableField(exist = false)
    private String peopleName;

    @Excel(name = "联系电话")
    @TableField(exist = false)
    private String peoplePhone;

    /**
     * 关联标识，各厂商设备标识规则不同，唯一标识网络下的设备。
     * 欧万：（网关mac+设备别名）
     */
    @Excel(name = "关联标识")
    private String contextid;

    /**
     * 发放方式
     */
    private String type;
    /**
     * 发放方式
     */
    @Excel(name = "发放方式")
    @TableField(exist = false)
    private String typeName;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        PeopleDeviceTypeEnum peopleDeviceTypeEnum = PeopleDeviceTypeEnum.getByValue(type);
        this.setTypeName(peopleDeviceTypeEnum.getDescription());
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceModelNumber() {
        return deviceModelNumber;
    }

    public void setDeviceModelNumber(String deviceModelNumber) {
        this.deviceModelNumber = deviceModelNumber;
    }

    public String getPeopleName() {
        return peopleName;
    }

    public void setPeopleName(String peopleName) {
        this.peopleName = peopleName;
    }

    public String getPeoplePhone() {
        return peoplePhone;
    }

    public void setPeoplePhone(String peoplePhone) {
        this.peoplePhone = peoplePhone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getPeopleId() {
        return peopleId;
    }

    public void setPeopleId(String peopleId) {
        this.peopleId = peopleId;
    }

    public String getContextid() {
        return contextid;
    }

    public void setContextid(String contextid) {
        this.contextid = contextid;
    }

    @Override
    public String toString() {
        return "SecurityGuardDevciePeopleInfo{" +
                "id=" + id +
                ", deviceId=" + deviceId +
                ", deviceName='" + deviceName + '\'' +
                ", deviceModelNumber='" + deviceModelNumber + '\'' +
                ", peopleId='" + peopleId + '\'' +
                ", peopleName='" + peopleName + '\'' +
                ", peoplePhone='" + peoplePhone + '\'' +
                ", contextid='" + contextid + '\'' +
                ", type='" + type + '\'' +
                ", typeName='" + typeName + '\'' +
                '}';
    }
}
