package com.ruoyi.homecare.serviceProviders.mapper;

import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 服务商和社区关联Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Mapper
public interface HomeServiceProviderCommunityIndexMapper {
    /**
     * 查询服务商和社区关联
     *
     * @param id 服务商和社区关联主键
     * @return 服务商和社区关联
     */
    public HomeServiceProviderCommunityIndex selectHomeServiceProviderCommunityIndexById(Long id);

    /**
     * 查询服务商和社区关联列表
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 服务商和社区关联集合
     */
    public List<HomeServiceProviderCommunityIndex> selectHomeServiceProviderCommunityIndexList(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex);

    /**
     * 新增服务商和社区关联
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 结果
     */
    public int insertHomeServiceProviderCommunityIndex(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex);

    /**
     * 修改服务商和社区关联
     *
     * @param homeServiceProviderCommunityIndex 服务商和社区关联
     * @return 结果
     */
    public int updateHomeServiceProviderCommunityIndex(HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex);

    /**
     * 删除服务商和社区关联
     *
     * @param id 服务商和社区关联主键
     * @return 结果
     */
    public int deleteHomeServiceProviderCommunityIndexById(Long id);

    /**
     * 批量删除服务商和社区关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeServiceProviderCommunityIndexByIds(Long[] ids);

    /**
     * 通过服务商Id删除数据
     *
     * @param serviceProviderId
     * @return
     */
    int deleteByServiceId(Long serviceProviderId);

    String getListIdByServiceId(Long baseId);
}
