package com.ruoyi.homecare.elderlyPeople.service;

import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleLifeStyle;

import java.util.List;

/**
 * 老人生活方式信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
public interface IHomeCareElderlyPeopleLifeStyleService {
    /**
     * 查询老人生活方式信息
     *
     * @param id 老人生活方式信息主键
     * @return 老人生活方式信息
     */
    public ElderlyPeopleLifeStyle selectElderlyPeopleLifeStyleById(String id);

    /**
     * 查询老人生活方式信息列表
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 老人生活方式信息集合
     */
    public List<ElderlyPeopleLifeStyle> selectElderlyPeopleLifeStyleList(ElderlyPeopleLifeStyle elderlyPeopleLifeStyle);

    /**
     * 新增老人生活方式信息
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 结果
     */
    public int insertElderlyPeopleLifeStyle(ElderlyPeopleLifeStyle elderlyPeopleLifeStyle);

    /**
     * 修改老人生活方式信息
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 结果
     */
    public int updateElderlyPeopleLifeStyle(ElderlyPeopleLifeStyle elderlyPeopleLifeStyle);

    /**
     * 批量删除老人生活方式信息
     *
     * @param ids 需要删除的老人生活方式信息主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleLifeStyleByIds(String[] ids);

    /**
     * 删除老人生活方式信息信息
     *
     * @param id 老人生活方式信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleLifeStyleById(String id);
}
