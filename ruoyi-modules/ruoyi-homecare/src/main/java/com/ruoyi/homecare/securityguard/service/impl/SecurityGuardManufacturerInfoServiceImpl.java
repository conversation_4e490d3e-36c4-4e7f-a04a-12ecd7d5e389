package com.ruoyi.homecare.securityguard.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

import com.ruoyi.common.core.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardDeviceInfo;
import com.ruoyi.homecare.securityguard.mapper.SecurityGuardManufacturerInfoMapper;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardManufacturerInfo;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardManufacturerInfoService;

/**
 * 厂商信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
@Service
public class SecurityGuardManufacturerInfoServiceImpl implements ISecurityGuardManufacturerInfoService {
    @Autowired
    private SecurityGuardManufacturerInfoMapper securityGuardManufacturerInfoMapper;

    /**
     * 查询厂商信息
     *
     * @param id 厂商信息主键
     * @return 厂商信息
     */
    @Override
    public SecurityGuardManufacturerInfo selectSecurityGuardManufacturerInfoById(Long id) {
        return securityGuardManufacturerInfoMapper.selectSecurityGuardManufacturerInfoById(id);
    }

    /**
     * 查询厂商信息列表
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 厂商信息
     */
    @Override
    public List<SecurityGuardManufacturerInfo> selectSecurityGuardManufacturerInfoList(SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        return securityGuardManufacturerInfoMapper.selectSecurityGuardManufacturerInfoList(securityGuardManufacturerInfo);
    }

    /**
     * 新增厂商信息
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 结果
     */
    @Transactional
    @Override
    public int insertSecurityGuardManufacturerInfo(SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        int rows = securityGuardManufacturerInfoMapper.insertSecurityGuardManufacturerInfo(securityGuardManufacturerInfo);
        insertSecurityGuardDeviceInfo(securityGuardManufacturerInfo);
        return rows;
    }

    /**
     * 修改厂商信息
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 结果
     */
    @Transactional
    @Override
    public int updateSecurityGuardManufacturerInfo(SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        securityGuardManufacturerInfoMapper.deleteSecurityGuardDeviceInfoByManufacturerId(securityGuardManufacturerInfo.getId());
        insertSecurityGuardDeviceInfo(securityGuardManufacturerInfo);
        return securityGuardManufacturerInfoMapper.updateSecurityGuardManufacturerInfo(securityGuardManufacturerInfo);
    }

    /**
     * 批量删除厂商信息
     *
     * @param ids 需要删除的厂商信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSecurityGuardManufacturerInfoByIds(Long[] ids) {
        securityGuardManufacturerInfoMapper.deleteSecurityGuardDeviceInfoByManufacturerIds(ids);
        return securityGuardManufacturerInfoMapper.deleteSecurityGuardManufacturerInfoByIds(ids);
    }

    /**
     * 删除厂商信息信息
     *
     * @param id 厂商信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSecurityGuardManufacturerInfoById(Long id) {
        securityGuardManufacturerInfoMapper.deleteSecurityGuardDeviceInfoByManufacturerId(id);
        return securityGuardManufacturerInfoMapper.deleteSecurityGuardManufacturerInfoById(id);
    }

    /**
     * 新增设备信息信息
     *
     * @param securityGuardManufacturerInfo 厂商信息对象
     */
    public void insertSecurityGuardDeviceInfo(SecurityGuardManufacturerInfo securityGuardManufacturerInfo) {
        List<SecurityGuardDeviceInfo> securityGuardDeviceInfoList = securityGuardManufacturerInfo.getSecurityGuardDeviceInfoList();
        Long id = securityGuardManufacturerInfo.getId();
        if (StringUtils.isNotNull(securityGuardDeviceInfoList)) {
            List<SecurityGuardDeviceInfo> list = new ArrayList<SecurityGuardDeviceInfo>();
            for (SecurityGuardDeviceInfo securityGuardDeviceInfo : securityGuardDeviceInfoList) {
                securityGuardDeviceInfo.setManufacturerId(id);
                list.add(securityGuardDeviceInfo);
            }
            if (list.size() > 0) {
                securityGuardManufacturerInfoMapper.batchSecurityGuardDeviceInfo(list);
            }
        }
    }
}
