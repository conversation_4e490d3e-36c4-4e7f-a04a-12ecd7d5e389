package com.ruoyi.homecare.serviceProviders.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.homecare.elderlyPeople.domain.vo.UserDataInfoResult;
import com.ruoyi.homecare.service.domain.HomeServiceProviderWorker;
import com.ruoyi.homecare.service.service.impl.HomeServiceProviderWorkerServiceImpl;
import com.ruoyi.homecare.serviceProviders.domain.HomeRegisterServiceProvider;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.domain.vo.AppServiceProviderManagementVo;
import com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProviderManagementMapper;
import com.ruoyi.homecare.serviceProviders.service.IHomeRegisterServiceProviderService;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderCommunityIndexService;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderManagementService;
import com.ruoyi.homecare.utils.SysUserUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务商管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-14
 */
@Service
public class HomeServiceProviderManagementServiceImpl implements IHomeServiceProviderManagementService {
    @Autowired
    protected RemoteUserService remoteUserService;
    @Autowired
    private HomeServiceProviderManagementMapper homeServiceProviderManagementMapper;
    @Autowired
    private IHomeServiceProviderCommunityIndexService homeServiceProviderCommunityIndexService;
    @Autowired
    private IHomeRegisterServiceProviderService homeRegisterServiceProviderService;
    @Autowired
    private HomeServiceProviderWorkerServiceImpl homeServiceProviderWorkerService;

    /**
     * 查询服务商管理
     *
     * @param id 服务商管理主键
     * @return 服务商管理
     */
    @Override
    public HomeServiceProviderManagement selectHomeServiceProviderManagementById(Long id) {
        HomeServiceProviderManagement homeServiceProviderManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(id);
//        String communityIdArr = homeServiceProviderCommunityIndexService.getListIdByServiceId(id);
//        homeServiceProviderManagement.setCommunityId(communityIdArr);
        strToList(homeServiceProviderManagement);
        return homeServiceProviderManagement;
    }

    /**
     * 根据系统用户id查询已审核成功服务商管理
     *
     * @param userId 系统用户id
     * @return
     */
    @Override
    public HomeServiceProviderManagement getServiceProviderBySysUserId(Long userId) {
        return homeServiceProviderManagementMapper.getServiceProviderBySysUserId(userId);
    }

    /**
     * 查询服务商管理列表
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 服务商管理
     */
    @Override
    public List<HomeServiceProviderManagement> selectHomeServiceProviderManagementList(HomeServiceProviderManagement homeServiceProviderManagement) {
        return homeServiceProviderManagementMapper.selectHomeServiceProviderManagementList(homeServiceProviderManagement);
    }

    /**
     * 新增服务商管理
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertHomeServiceProviderManagement(HomeServiceProviderManagement homeServiceProviderManagement) {
        listToStr(homeServiceProviderManagement);
        homeServiceProviderManagement.setCreateTime(DateUtils.getNowDate());
        SysUser sysUser = new SysUser();
        if ("0".equals(homeServiceProviderManagement.getCreateClient())) {// 平台管理员新增不需要审核
            saveIndexList(homeServiceProviderManagement.getId(), homeServiceProviderManagement.getCommunityId());
            // 注册已审核成功用户
            String serviceType = homeServiceProviderManagement.getServiceType();
            R<SysUser> loginUserR = SysUserUtils.addSysUser(homeServiceProviderManagement.getName(), homeServiceProviderManagement.getUserName(), homeServiceProviderManagement.getPassword(), "3", serviceType, homeServiceProviderManagement.getPhone());
            if (loginUserR.getCode() != 200) {
                throw new ServiceException(loginUserR.getMsg());
            }
            sysUser = loginUserR.getData();
        }
        Long userid = sysUser.getUserId();
        homeServiceProviderManagement.setSysUserId(userid);
        // 判断服务商图片是否有值，如果没有给默认图片
        if (StringUtils.isEmpty(homeServiceProviderManagement.getMerchantsPhotos())) {
            homeServiceProviderManagement.setMerchantsPhotos("http://117.159.27.170:23360/ruoyi-auth/statics/2022/07/25/cs.png");
        }
        homeServiceProviderManagement.setServiceState("1");
        homeServiceProviderManagementMapper.insertHomeServiceProviderManagement(homeServiceProviderManagement);
        return 1;
    }

    /**
     * 修改服务商管理
     *
     * @param homeServiceProviderManagement 服务商管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateHomeServiceProviderManagement(HomeServiceProviderManagement homeServiceProviderManagement) {
        listToStr(homeServiceProviderManagement);
        homeServiceProviderManagement.setUpdateTime(DateUtils.getNowDate());
        if ("1".equals(homeServiceProviderManagement.getState())) {
            saveIndexList(homeServiceProviderManagement.getId(), homeServiceProviderManagement.getCommunityId());
        }
        HomeServiceProviderManagement old = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(homeServiceProviderManagement.getId());
        if (!homeServiceProviderManagement.getServiceType().equals(old.getServiceType())) {// 判断是否改变了服务类型
            R<SysUser> rSysUser = remoteUserService.getInfoByUserId(homeServiceProviderManagement.getSysUserId());// 重新改变系统用户角色
            if (rSysUser.getCode() != 200) {
                throw new ServiceException(rSysUser.getMsg());
            }
            SysUser user = rSysUser.getData();
            List<Long> roleList = new ArrayList<>();
            String[] split = homeServiceProviderManagement.getServiceType().split(",");// 判断角色
            for (String s : split) {
                if ("service".equals(s)) {
                    roleList.add(5L);
                } else if ("goods".equals(s)) {
                    roleList.add(13L);
                } else if ("food".equals(s)) {
                    roleList.add(12L);
                }
            }
            Long[] rolesIds = roleList.toArray(new Long[roleList.size()]);
            user.setRoleIds(rolesIds);
            remoteUserService.updateCareWorker(user);
        }
        return homeServiceProviderManagementMapper.updateHomeServiceProviderManagement(homeServiceProviderManagement);
    }

    /**
     * 批量删除服务商管理
     *
     * @param ids 需要删除的服务商管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteHomeServiceProviderManagementByIds(Long[] ids) {
        for (Long id : ids) {
            HomeServiceProviderManagement homeServiceProviderManagement = selectHomeServiceProviderManagementById(id);
            Long sysUserId = homeServiceProviderManagement.getSysUserId();
            if (null == sysUserId) {
                throw new SecurityException("此信息有问题，请联系管理员");
            }
            SysUser sysUser = new SysUser();
            sysUser.setUserId(sysUserId);
            sysUser.setStatus("1");
            AjaxResult ajaxResult = remoteUserService.changeStatus(sysUser);
            if ((Integer) ajaxResult.get("code") != 200) {
                throw new SecurityException(String.valueOf(ajaxResult.get("msg")));
            }

            // 服务商工作人员删除
            HomeServiceProviderWorker homeServiceProviderWorker = new HomeServiceProviderWorker();
            homeServiceProviderWorker.setServiceProviderId(id);
            List<HomeServiceProviderWorker> serviceProviderWorkers = homeServiceProviderWorkerService.selectList(homeServiceProviderWorker);
            if (serviceProviderWorkers.size() > 0) {
                List<Long> idList = new ArrayList();
                for (HomeServiceProviderWorker serviceProviderWorker : serviceProviderWorkers) {
                    idList.add(serviceProviderWorker.getId());
                }
                homeServiceProviderWorkerService.deleteByIds(idList.toArray(new Long[idList.size()]));
            }
        }
        return homeServiceProviderManagementMapper.deleteHomeServiceProviderManagementByIds(ids);
    }

    /**
     * 删除服务商管理信息
     *
     * @param id 服务商管理主键
     * @return 结果
     */
    @Override
    public int deleteHomeServiceProviderManagementById(Long id) {
        return homeServiceProviderManagementMapper.deleteHomeServiceProviderManagementById(id);
    }

    /**
     * 获取全量服务商管理KeyValue
     *
     * @param name
     * @return
     */
    @Override
    public List<JSONObject> getServiceProviderList(String name) {
        return homeServiceProviderManagementMapper.getServiceProviderList(name);
    }

    /**
     * 管理员审核服务商
     *
     * @param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int audit(JSONObject data) {
        String id = data.getStr("id");
        String state = data.getStr("state");
        String auditCause = data.getStr("auditCause");
        HomeRegisterServiceProvider homeRegisterServiceProvider = homeRegisterServiceProviderService.selectHomeRegisterServiceProviderById(Long.parseLong(id));
        homeRegisterServiceProvider.setState(state);
        homeRegisterServiceProvider.setAuditTime(new Date());
        homeRegisterServiceProvider.setAuditCause(auditCause);
        if ("1".equals(homeRegisterServiceProvider.getState())) { // 审核通过
            // 审核通过抽出数据到审核成功的服务商表中
            HomeServiceProviderManagement homeServiceProviderManagement = new HomeServiceProviderManagement();
            BeanUtils.copyProperties(homeRegisterServiceProvider, homeServiceProviderManagement);
            homeServiceProviderManagement.setCreateClient(homeRegisterServiceProvider.getRegisterClient());
            // 判断服务商图片是否有值，如果没有默认图片
            if (StringUtils.isEmpty(homeServiceProviderManagement.getMerchantsPhotos())) {
                homeServiceProviderManagement.setMerchantsPhotos("http://117.159.27.170:23360/ruoyi-auth/statics/2022/07/25/cs.png");
            }
            homeServiceProviderManagement.setServiceState("1");// 服务状态改为暂停休息
            homeServiceProviderManagementMapper.insertHomeServiceProviderManagement(homeServiceProviderManagement);
            // 关联社区
            if (null != homeServiceProviderManagement.getCommunityId()) {
                saveIndexList(homeServiceProviderManagement.getId(), homeServiceProviderManagement.getCommunityId());
            }
            // 换注册成功角色
            R<SysUser> rSysUser = remoteUserService.getInfoByUserId(homeRegisterServiceProvider.getSysUserId());
            if (rSysUser.getCode() != 200) {
                throw new ServiceException(rSysUser.getMsg());
            }
            SysUser user = rSysUser.getData();
            List<Long> roleList = new ArrayList<>();
            String[] split = homeServiceProviderManagement.getServiceType().split(",");// 判断角色
            for (String s : split) {
                if ("service".equals(s)) {
                    roleList.add(5L);
                } else if ("goods".equals(s)) {
                    roleList.add(13L);
                } else if ("food".equals(s)) {
                    roleList.add(12L);
                }
            }
            Long[] rolesIds = roleList.toArray(new Long[roleList.size()]);
            user.setRoleIds(rolesIds);
            remoteUserService.updateCareWorker(user);
            homeRegisterServiceProvider.setSelectState("1");
        }
        return homeRegisterServiceProviderService.updateHomeRegisterServiceProvider(homeRegisterServiceProvider);
    }

    /**
     * 开启、关闭状态
     *
     * @param state
     * @param id
     * @return
     */
    @Override
    public int serviceProviderOpenClose(String state, Long id) {
        HomeServiceProviderManagement homeServiceProviderManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(id);
        homeServiceProviderManagement.setServiceState(state);
        return homeServiceProviderManagementMapper.updateHomeServiceProviderManagement(homeServiceProviderManagement);
    }

    /**
     * app通过类别标签查询服务商列表
     *
     * @param homeServiceProviderManagement
     * @return
     */
    @Override
    public List<AppServiceProviderManagementVo> getServiceProviderListInfo(AppServiceProviderManagementVo homeServiceProviderManagement) {
        return homeServiceProviderManagementMapper.getServiceProviderListInfo(homeServiceProviderManagement);
    }

    /**
     * 服务商的总销量增加
     *
     * @param id
     * @return
     */
    @Override
    public int salesSave(Long id) {
        HomeServiceProviderManagement homeServiceProviderManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(id);
        homeServiceProviderManagement.setTotalSales(homeServiceProviderManagement.getTotalSales() + 1);
        return homeServiceProviderManagementMapper.updateHomeServiceProviderManagement(homeServiceProviderManagement);
    }

    /**
     * 服务商的好评数增加
     *
     * @param id
     * @param score 评分分数
     * @return
     */
    @Override
    public int praiseSave(Long id, Integer score) {
        HomeServiceProviderManagement homeServiceProviderManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(id);
        if (score > 4) {
            homeServiceProviderManagement.setPraiseNum(homeServiceProviderManagement.getPraiseNum() + 1);
        }
        return homeServiceProviderManagementMapper.updateHomeServiceProviderManagement(homeServiceProviderManagement);
    }

    @Override
    public int updateBanFlag(Long id, Integer banFlag) {
        HomeServiceProviderManagement homeServiceProviderManagement = homeServiceProviderManagementMapper.selectHomeServiceProviderManagementById(id);
        if (homeServiceProviderManagement.getBanFlag().equals(banFlag)) {
            throw new ServiceException("状态异常，请刷新后重试");
        }
        if (banFlag == 0) {
            homeServiceProviderManagement.setBanFlag(0);
        } else {
            homeServiceProviderManagement.setServiceState("1");
            homeServiceProviderManagement.setBanFlag(1);
        }
        return homeServiceProviderManagementMapper.updateHomeServiceProviderManagement(homeServiceProviderManagement);
    }

    /**
     * 服务商和社区关联表
     *
     * @param serviceProviderId
     * @param CommunityIds
     * @return
     */
    public int saveIndexList(Long serviceProviderId, String CommunityIds) {
        String[] communityIdArr = CommunityIds.split(",");
        homeServiceProviderCommunityIndexService.deleteByServiceId(serviceProviderId);
        for (String CommunityId : communityIdArr) {
            HomeServiceProviderCommunityIndex homeServiceProviderCommunityIndex = new HomeServiceProviderCommunityIndex();
            homeServiceProviderCommunityIndex.setServiceProviderId(serviceProviderId);
            homeServiceProviderCommunityIndex.setCommunityId(Long.parseLong(CommunityId));
            homeServiceProviderCommunityIndexService.insertHomeServiceProviderCommunityIndex(homeServiceProviderCommunityIndex);
        }
        return 1;
    }

    /**
     * 商家评价信息
     *
     * @return
     */
    @Override
    public JSONObject getServiceProviderEvaluateInfo() {
        UserDataInfoResult userDataInfoResult = SysUserUtils.getInfoBySysUserId(null);
        Long serviceProviderId = userDataInfoResult.getServiceProviderId();
        if (null == serviceProviderId) {
            throw new ServiceException("商家信息异常");
        }
        JSONObject jsonObject = getProviderEvaluateInfo(serviceProviderId);
        return jsonObject;
    }


    /**
     * 管理端服务商管理-商家评价信息
     *
     * @param id
     * @return
     */
    @Override
    public JSONObject getAdminServiceProviderEvaluateInfo(Long id) {
        if (null == id) {
            throw new ServiceException("商家信息异常");
        }
        JSONObject jsonObject = getProviderEvaluateInfo(id);
        return jsonObject;
    }

    /**
     * 根据服务商id获取服务商评价信息
     *
     * @param id
     * @return
     */
    public JSONObject getProviderEvaluateInfo(Long id) {
        JSONObject jsonObject = new JSONObject();
        HomeServiceProviderManagement homeServiceProviderManagement = selectHomeServiceProviderManagementById(id);
        if (null == homeServiceProviderManagement) {
            throw new ServiceException("商家信息异常");
        }
        String serviceType = homeServiceProviderManagement.getServiceType();
        String[] split = serviceType.split(",");
        JSONArray jsonArray = new JSONArray();
        for (String type : split) {// 组装类型
            if ("goods".equals(type)) {
                JSONObject object = new JSONObject();
                object.set("value", 1);
                object.set("label", "商品类");
                jsonArray.add(object);
            } else if ("food".equals(type)) {
                JSONObject object = new JSONObject();
                object.set("value", 2);
                object.set("label", "餐品类");
                jsonArray.add(object);
            } else if ("service".equals(type)) {
                JSONObject object = new JSONObject();
                object.set("value", 3);
                object.set("label", "服务类");
                jsonArray.add(object);
            }
        }
        // 算出星级
        Integer totalOrderNum = homeServiceProviderManagement.getTotalOrderNum();
        Integer evaluateNum = homeServiceProviderManagement.getEvaluateNum();
        Integer value = (totalOrderNum - evaluateNum) * 5;
        Integer zValue = value + homeServiceProviderManagement.getEvaluateNumValue();
        String format = "5";
        if (zValue > 0) {
            double num = zValue / totalOrderNum;
            format = String.format("%.1f", num);// 保留一位小数 （四舍五入）
        }
        jsonObject.set("typeLabel", jsonArray);
        jsonObject.set("praiseRate", homeServiceProviderManagement.getPraiseRate());
        jsonObject.set("evaluate", format);
        return jsonObject;
    }


    private void listToStr(HomeServiceProviderManagement homeServiceProviderManagement) {
        List<String> labelList = homeServiceProviderManagement.getLabelList();
        homeServiceProviderManagement.setLabel(CollUtil.join(labelList, ","));

        List<String> businessLicenseList = homeServiceProviderManagement.getBusinessLicenseList();
        homeServiceProviderManagement.setBusinessLicense(CollUtil.join(businessLicenseList, ","));

        List<String> contractAttachmentList = homeServiceProviderManagement.getContractAttachmentList();
        homeServiceProviderManagement.setContractAttachment(CollUtil.join(contractAttachmentList, ","));

        List<String> serviceTypeList = homeServiceProviderManagement.getServiceTypeList();
        homeServiceProviderManagement.setServiceType(CollUtil.join(serviceTypeList, ","));

        List<String> merchantsPhotosList = homeServiceProviderManagement.getMerchantsPhotosList();
        homeServiceProviderManagement.setMerchantsPhotos(CollUtil.join(merchantsPhotosList, ","));

        List<Integer> communityIdList = homeServiceProviderManagement.getCommunityIdList();
        homeServiceProviderManagement.setCommunityId(CollUtil.join(communityIdList, ","));
    }


    private void strToList(HomeServiceProviderManagement homeServiceProviderManagement) {


        if (StrUtil.isEmpty(homeServiceProviderManagement.getServiceType())) {
            homeServiceProviderManagement.setServiceTypeList(new ArrayList<>());
        } else {
            List<String> serviceTypeList = StrUtil.split(homeServiceProviderManagement.getServiceType(), ",");
            homeServiceProviderManagement.setServiceTypeList(serviceTypeList);
        }


        if (StrUtil.isEmpty(homeServiceProviderManagement.getLabel())) {
            homeServiceProviderManagement.setLabelList(new ArrayList<>());
        } else {
            List<String> labelList = StrUtil.split(homeServiceProviderManagement.getLabel(), ",");
            homeServiceProviderManagement.setLabelList(labelList);
        }


        if (StrUtil.isEmpty(homeServiceProviderManagement.getBusinessLicense())) {
            homeServiceProviderManagement.setBusinessLicenseList(new ArrayList<>());
        } else {
            List<String> businessLicenseList = StrUtil.split(homeServiceProviderManagement.getBusinessLicense(), ",");
            homeServiceProviderManagement.setBusinessLicenseList(businessLicenseList);
        }


        if (StrUtil.isEmpty(homeServiceProviderManagement.getContractAttachment())) {
            homeServiceProviderManagement.setContractAttachmentList(new ArrayList<>());
        } else {
            List<String> businessLicenseList = StrUtil.split(homeServiceProviderManagement.getContractAttachment(), ",");
            homeServiceProviderManagement.setContractAttachmentList(businessLicenseList);
        }


        if (StrUtil.isEmpty(homeServiceProviderManagement.getMerchantsPhotos())) {
            homeServiceProviderManagement.setMerchantsPhotosList(new ArrayList<>());
        } else {
            List<String> businessLicenseList = StrUtil.split(homeServiceProviderManagement.getMerchantsPhotos(), ",");
            homeServiceProviderManagement.setMerchantsPhotosList(businessLicenseList);
        }


        if (StrUtil.isEmpty(homeServiceProviderManagement.getCommunityId())) {
            homeServiceProviderManagement.setCommunityIdList(new ArrayList<>());
        } else {
            List<Integer> communityIdList = Arrays.stream(homeServiceProviderManagement.getCommunityId().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            homeServiceProviderManagement.setCommunityIdList(communityIdList);
        }


    }

}
