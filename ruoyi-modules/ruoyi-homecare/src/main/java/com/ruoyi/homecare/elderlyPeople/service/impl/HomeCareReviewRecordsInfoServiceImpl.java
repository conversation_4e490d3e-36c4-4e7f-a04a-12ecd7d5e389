package com.ruoyi.homecare.elderlyPeople.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.homecare.elderlyPeople.domain.CapabilityAssessmentInfo;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.homecare.elderlyPeople.domain.ReviewRecordsInfo;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareCapabilityAssessmentInfoMapper;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleInfoMapper;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareReviewRecordsInfoMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareReviewRecordsInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 能力评估复核Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@Service
public class HomeCareReviewRecordsInfoServiceImpl implements IHomeCareReviewRecordsInfoService {
    @Autowired
    private HomeCareReviewRecordsInfoMapper reviewRecordsInfoMapper;

    @Autowired
    private HomeCareCapabilityAssessmentInfoMapper capabilityAssessmentInfoMapper;

    @Autowired
    private HomeCareElderlyPeopleInfoMapper homeCareElderlyPeopleInfoMapper;

    /**
     * 查询能力评估复核
     *
     * @param id 能力评估复核主键
     * @return 能力评估复核
     */
    @Override
    public ReviewRecordsInfo selectReviewRecordsInfoById(Long id) {
        return reviewRecordsInfoMapper.selectReviewRecordsInfoById(id);
    }

    /**
     * 查询能力评估复核列表
     *
     * @param reviewRecordsInfo 能力评估复核
     * @return 能力评估复核
     */
    @Override
    public List<ReviewRecordsInfo> selectReviewRecordsInfoList(ReviewRecordsInfo reviewRecordsInfo) {
        return reviewRecordsInfoMapper.selectReviewRecordsInfoList(reviewRecordsInfo);
    }

    /**
     * 新增能力评估复核
     *
     * @param reviewRecordsInfo 能力评估复核
     * @return 结果
     */
    @Override
    public int insertReviewRecordsInfo(ReviewRecordsInfo reviewRecordsInfo) {
        reviewRecordsInfo.setCreateTime(DateUtils.getNowDate());
        return reviewRecordsInfoMapper.insertReviewRecordsInfo(reviewRecordsInfo);
    }

    /**
     * 修改能力评估复核
     *
     * @param reviewRecordsInfo 能力评估复核
     * @return 结果
     */
    @Override
    public int updateReviewRecordsInfo(ReviewRecordsInfo reviewRecordsInfo) {
        reviewRecordsInfo.setUpdateTime(DateUtils.getNowDate());
        return reviewRecordsInfoMapper.updateReviewRecordsInfo(reviewRecordsInfo);
    }

    /**
     * 批量删除能力评估复核
     *
     * @param ids 需要删除的能力评估复核主键
     * @return 结果
     */
    @Override
    public int deleteReviewRecordsInfoByIds(Long[] ids) {
        return reviewRecordsInfoMapper.deleteReviewRecordsInfoByIds(ids);
    }

    /**
     * 删除能力评估复核信息
     *
     * @param id 能力评估复核主键
     * @return 结果
     */
    @Override
    public int deleteReviewRecordsInfoById(Long id) {
        return reviewRecordsInfoMapper.deleteReviewRecordsInfoById(id);
    }

    /**
     * 复核
     *
     * @param reviewRecordsInfo
     * @return
     */
    @Override
    public int save(ReviewRecordsInfo reviewRecordsInfo) {
        capabilityAssessmentInfoMapper.updateReviewById(reviewRecordsInfo.getBaseId(), reviewRecordsInfo.getPreliminaryAssessmentLevel());
        // 改变老人失能情况基础信息
        CapabilityAssessmentInfo capabilityAssessmentInfo = capabilityAssessmentInfoMapper.selectCapabilityAssessmentInfoById(reviewRecordsInfo.getBaseId());
        String userId = capabilityAssessmentInfo.getUserId();
        ElderlyPeopleInfo elderlyPeopleInfo = homeCareElderlyPeopleInfoMapper.selectElderlyPeopleInfoById(userId);
        elderlyPeopleInfo.setDisability(reviewRecordsInfo.getPreliminaryAssessmentLevel());
        homeCareElderlyPeopleInfoMapper.updateElderlyPeopleInfo(elderlyPeopleInfo);
        ReviewRecordsInfo baseInfo = reviewRecordsInfoMapper.getBaseInfo(reviewRecordsInfo.getBaseId());
        int i = 0;
        if (null != baseInfo) {
            reviewRecordsInfo.setId(baseInfo.getId());
            i = reviewRecordsInfoMapper.updateReviewRecordsInfo(reviewRecordsInfo);
        } else {
            i = reviewRecordsInfoMapper.insertReviewRecordsInfo(reviewRecordsInfo);
        }
        return i;
    }
}
