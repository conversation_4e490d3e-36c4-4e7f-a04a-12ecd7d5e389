package com.ruoyi.homecare.securityguard.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardSensor;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardSensorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 拉绳传感器报警信息Controller
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@RestController
@RequestMapping("/sensor")
public class SecurityGuardSensorController extends BaseController {
    @Autowired
    private ISecurityGuardSensorService securityGuardSensorService;

    /**
     * 查询拉绳传感器报警信息列表
     */
    @RequiresPermissions("securityguard:sensor:list")
    @GetMapping("/list")
    public TableDataInfo list(SecurityGuardSensor securityGuardSensor) {
        startPage();
        List<SecurityGuardSensor> list = securityGuardSensorService.selectSecurityGuardSensorList(securityGuardSensor);
        return getDataTable(list);
    }

    /**
     * 导出拉绳传感器报警信息列表
     */
    @RequiresPermissions("securityguard:sensor:export")
    @Log(title = "拉绳传感器报警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecurityGuardSensor securityGuardSensor) {
        List<SecurityGuardSensor> list = securityGuardSensorService.selectSecurityGuardSensorList(securityGuardSensor);
        ExcelUtil<SecurityGuardSensor> util = new ExcelUtil<SecurityGuardSensor>(SecurityGuardSensor.class);
        util.exportExcel(response, list, "拉绳传感器报警信息数据");
    }

    /**
     * 获取拉绳传感器报警信息详细信息
     */
    @RequiresPermissions("securityguard:sensor:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityGuardSensorService.selectSecurityGuardSensorById(id));
    }

    /**
     * 新增拉绳传感器报警信息
     */
    @RequiresPermissions("securityguard:sensor:add")
    @Log(title = "拉绳传感器报警信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityGuardSensor securityGuardSensor) {
        return toAjax(securityGuardSensorService.insertSecurityGuardSensor(securityGuardSensor));
    }

    /**
     * 修改拉绳传感器报警信息
     */
    @RequiresPermissions("securityguard:sensor:edit")
    @Log(title = "拉绳传感器报警信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityGuardSensor securityGuardSensor) {
        return toAjax(securityGuardSensorService.updateSecurityGuardSensor(securityGuardSensor));
    }

    /**
     * 删除拉绳传感器报警信息
     */
    @RequiresPermissions("securityguard:sensor:remove")
    @Log(title = "拉绳传感器报警信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityGuardSensorService.deleteSecurityGuardSensorByIds(ids));
    }
}
