package com.ruoyi.homecare.elderlyPeople.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.homecare.utils.DictUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 用药提醒对象 t_home_medication_remind
 *
 * <AUTHOR>
 * @date 2022-06-15
 */
@ApiModel(value = "用药提醒")
public class HomeMedicationRemind extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 老人id
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id")
    private String userId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始时间")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date beginDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束时间")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 重复频次
     */
    @Excel(name = "重复频次")
    @ApiModelProperty(value = "重复频次")
    private String frequency;

    /**
     * 重复频次
     */
    @Excel(name = "重复频次Label")
    @ApiModelProperty(value = "重复频次Label")
    private String frequencyLabel;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    @Excel(name = "发送时间")
    private String sendTime;

    /**
     * 发送内容
     */
    @Excel(name = "发送内容")
    @ApiModelProperty(value = "发送内容")
    private String sendContent;

    /**
     * 老人名称
     */
    @Excel(name = "老人名称")
    @ApiModelProperty(value = "老人名称")
    private String userName;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    @ApiModelProperty(value = "手机号")
    private String phone;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getSendContent() {
        return sendContent;
    }

    public void setSendContent(String sendContent) {
        this.sendContent = sendContent;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getFrequencyLabel() {
        {
            if (StringUtils.isEmpty(frequency)) {
                return "";
            }
            return getLabel(this.frequency, "week");
        }
    }

    public void setFrequencyLabel(String frequencyLabel) {
        this.frequencyLabel = frequencyLabel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("beginDate", getBeginDate())
                .append("endDate", getEndDate())
                .append("frequency", getFrequency())
                .append("sendTime", getSendTime())
                .append("sendContent", getSendContent())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }

    private String getLabel(String value, String type) {
        String[] split = value.split(",");
        String data = "";
        for (int i = 0; i < split.length; i++) {
            if (i == 0) {
                data += DictUtils.selectDictLabel(type, split[i]);
            } else {
                data += "、" + DictUtils.selectDictLabel(type, split[i]);
            }
        }
        return data;
    }
}
