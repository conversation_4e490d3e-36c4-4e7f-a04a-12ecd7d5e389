package com.ruoyi.homecare.serviceOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceInfo;
import com.ruoyi.homecare.serviceOrder.vo.HomeAdminOrderServiceListVo;
import com.ruoyi.homecare.serviceOrder.vo.HomeProviderOrderServiceListVo;
import com.ruoyi.homecare.serviceOrder.vo.HomeServiceOrderBaseInfoAdminVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description home_order_service_infoMapper
 * @date 2022-07-14
 */
@Mapper
public interface HomeOrderServiceInfoMapper extends BaseMapper<HomeOrderServiceInfo> {

    @Select(
            "<script>select t0.* from home_order_service_info t0 " +
                    // add here if need left join
                    "where 1=1" +
                    "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
                    "<when test='orderId!=null and orderId!=&apos;&apos; '> and t0.order_id=#{orderId}</when> " +
                    "<when test='serviceId!=null and serviceId!=&apos;&apos; '> and t0.service_id=#{serviceId}</when> " +
                    "<when test='serviceName!=null and serviceName!=&apos;&apos; '> and t0.service_name=#{serviceName}</when> " +
                    "<when test='price!=null and price!=&apos;&apos; '> and t0.price=#{price}</when> " +
                    "<when test='img!=null and img!=&apos;&apos; '> and t0.img=#{img}</when> " +
                    "<when test='standard!=null and standard!=&apos;&apos; '> and t0.standard=#{standard}</when> " +
                    "<when test='number!=null and number!=&apos;&apos; '> and t0.number=#{number}</when> " +
                    "<when test='workOrderId!=null and workOrderId!=&apos;&apos; '> and t0.work_order_id=#{workOrderId}</when> " +
                    "<when test='workOrderStatus!=null and workOrderStatus!=&apos;&apos; '> and t0.work_order_status=#{workOrderStatus}</when> " +
                    "<when test='workerId!=null and workerId!=&apos;&apos; '> and t0.worker_id=#{workerId}</when> " +
                    "<when test='workerPhone!=null and workerPhone!=&apos;&apos; '> and t0.worker_phone=#{workerPhone}</when> " +
                    "<when test='workerImg!=null and workerImg!=&apos;&apos; '> and t0.worker_img=#{workerImg}</when> " +
                    "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
                    "<when test='updateTime!=null and updateTime!=&apos;&apos; '> and t0.update_time=#{updateTime}</when> " +
                    // add here if need page limit
                    //" limit ${page},${limit} " +
                    " </script>")
    List<HomeOrderServiceInfo> pageAll(HomeOrderServiceInfo queryParamDTO, int page, int limit);

    @Select("<script>select count(1) from home_order_service_info t0 " +
            // add here if need left join
            "where 1=1" +
            "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
            "<when test='orderId!=null and orderId!=&apos;&apos; '> and t0.order_id=#{orderId}</when> " +
            "<when test='serviceId!=null and serviceId!=&apos;&apos; '> and t0.service_id=#{serviceId}</when> " +
            "<when test='serviceName!=null and serviceName!=&apos;&apos; '> and t0.service_name=#{serviceName}</when> " +
            "<when test='price!=null and price!=&apos;&apos; '> and t0.price=#{price}</when> " +
            "<when test='img!=null and img!=&apos;&apos; '> and t0.img=#{img}</when> " +
            "<when test='standard!=null and standard!=&apos;&apos; '> and t0.standard=#{standard}</when> " +
            "<when test='number!=null and number!=&apos;&apos; '> and t0.number=#{number}</when> " +
            "<when test='workOrderId!=null and workOrderId!=&apos;&apos; '> and t0.work_order_id=#{workOrderId}</when> " +
            "<when test='workOrderStatus!=null and workOrderStatus!=&apos;&apos; '> and t0.work_order_status=#{workOrderStatus}</when> " +
            "<when test='workerId!=null and workerId!=&apos;&apos; '> and t0.worker_id=#{workerId}</when> " +
            "<when test='workerPhone!=null and workerPhone!=&apos;&apos; '> and t0.worker_phone=#{workerPhone}</when> " +
            "<when test='workerImg!=null and workerImg!=&apos;&apos; '> and t0.worker_img=#{workerImg}</when> " +
            "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
            "<when test='updateTime!=null and updateTime!=&apos;&apos; '> and t0.update_time=#{updateTime}</when> " +
            " </script>")
    int countAll(HomeOrderServiceInfo queryParamDTO);

    @Select("<script>" +
            "SELECT\n" +
            "\tb.name AS workName,\n" +
            "\tc.price AS price,\n" +
            "\td.name AS providerName,\n" +
            "\te.type as type ,\n" +
            "CASE\n" +
            "\t\tWHEN e.type = '3' THEN\n" +
            "\tCASE\n" +
            "\t\t\tWHEN e.STATUS = '0' THEN\n" +
            "\t\t\t'已关闭' \n" +
            "\t\t\tWHEN e.STATUS = '1' THEN\n" +
            "\t\t\t'待支付' \n" +
            "\t\t\tWHEN e.STATUS = '2' THEN\n" +
            "\t\t\t'已付款' \n" +
            "\t\t\tWHEN e.STATUS = '3' THEN\n" +
            "\t\t\t'已接单' \n" +
            "\t\t\tWHEN e.STATUS = '4' THEN\n" +
            "\t\t\t'已完成' \n" +
            "\t\t\tWHEN e.STATUS = '5' THEN\n" +
            "\t\t\t'已评价' \n" +
            "\t\t\tWHEN e.STATUS = '6' THEN\n" +
            "\t\t\t'退款' ELSE '--' \n" +
            "\t\tEND  ELSE '' \n" +
            "\tEND AS statusLabel,\n" +
            "\t a.* \n" +
            "FROM\n" +
            "\tt_home_order_service_work AS a\n" +
            "\tLEFT JOIN t_home_service_provider_worker AS b ON a.worker_id = b.id\n" +
            "\tLEFT JOIN t_home_service_project AS c ON a.service_id = c.id\n" +
            "\tLEFT JOIN t_home_service_provider_management AS d ON d.id = a.service_provider_id\n" +
            "\tLEFT JOIN t_home_order_base_info AS e ON e.id = a.order_id " +
            "        <where>\n" +
            "            <if test=\"workName != '' and workName != null \">\n" +
            "                and b.name like concat('%',#{workName},'%')\n" +
            "            </if>\n" +
            "            <if test=\"beginTime != null and endTime != null \">\n" +
            "               and DATE_FORMAT(a.start_time,'%Y-%m-%d') between DATE_FORMAT(#{beginTime},'%Y-%m-%d') and DATE_FORMAT(#{endTime},'%Y-%m-%d')\n" +
            "            </if>\n" +
            "        </where>\n" +
            "        order by a.create_time desc " +
            "</script>")
    Page<HomeAdminOrderServiceListVo> getOrderServiceInfoAll(Page page,
                                                             @Param("workName") String workName,
                                                             @Param("beginTime") Date beginTime,
                                                             @Param("endTime") Date endTime);

    /**
     * 根据订单id获取工单详情的基础信息
     *
     * @param orderId
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "\tb.NAME AS providerName,\n" +
            "\tc.pay_way AS payWay,\n" +
            "\tc.total_price as totalPrice,\n" +
            "\tc.order_way as orderWay,\n" +
            "\tc.type, \n" +
            "CASE\n" +
            "\t\tWHEN c.pay_way = '1' THEN\n" +
            "\t\t'余额付款' ELSE '微信付款' \n" +
            "\tEND AS payWayLabel,\n" +
            "\ta.* \n" +
            "FROM\n" +
            "\tt_home_order_service_work AS a\n" +
            "\tLEFT JOIN t_home_service_provider_management AS b ON a.service_id = b.id\n" +
            "\tLEFT JOIN t_home_order_base_info AS c ON c.id = a.order_id\n" +
            "\twhere a.order_id = #{orderId}" +
            "</script>")
    List<HomeServiceOrderBaseInfoAdminVo> getWorkOrderBaseInfo(String orderId);

    /**
     * 服务商PC分页查询服务订单列表
     *
     * @param page
     * @param id
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "\tb.user_name,\n" +
            "\ta.id,\n" +
            "\ta.create_time,\n" +
            "\ta.name as elderlyName,\n" +
            "\ta.phone as elderlyPhone,\n" +
            "\tc.service_project as serviceName,\n" +
            "\tb.`status`,\n" +
            "\ta.`status` as workOrderStatus,\n" +
            "\ta.service_type," +
            "\ta.order_id," +
            "CASE\n" +
            "\t\tWHEN b.type = '3' THEN\n" +
            "\tCASE\n" +
            "\t\t\tWHEN b.STATUS = '0' THEN\n" +
            "\t\t\t'已关闭' \n" +
            "\t\t\tWHEN b.STATUS = '1' THEN\n" +
            "\t\t\t'待支付' \n" +
            "\t\t\tWHEN b.STATUS = '2' THEN\n" +
            "\t\t\t'已付款' \n" +
            "\t\t\tWHEN b.STATUS = '3' THEN\n" +
            "\t\t\t'已接单' \n" +
            "\t\t\tWHEN b.STATUS = '4' THEN\n" +
            "\t\t\t'已完成' \n" +
            "\t\t\tWHEN b.STATUS = '5' THEN\n" +
            "\t\t\t'已评价' \n" +
            "\t\t\tWHEN b.STATUS = '6' THEN\n" +
            "\t\t\t'退款' ELSE '--' \n" +
            "\t\tEND ELSE '' \n" +
            "\tEND AS statusLabel,\n" +
            "\tm.name AS providerName \n" +
            "FROM\n" +
            "\tt_home_order_service_work AS a\n" +
            "\tLEFT JOIN t_home_order_base_info AS b ON b.id = a.order_id\n" +
            "\tLEFT JOIN t_home_service_project AS c ON a.service_id = c.id\n" +
            "\tLEFT JOIN t_home_service_provider_management AS m ON m.id = a.service_provider_id" +
            "        <where>\n" +
            "            <if test=\"serviceProviderId != '' and serviceProviderId != null \">\n" +
            "                and a.service_provider_id = #{serviceProviderId}\n" +
            "            </if>\n" +
            "            <if test=\"id != '' and id != null \">\n" +
            "                and a.id like concat('%',#{id},'%')\n" +
            "            </if>\n" +
            "        </where>\n" +
            "        order by a.create_time desc " +
            "</script>")
    Page<HomeProviderOrderServiceListVo> getProviderOrderServiceInfoAll(Page page,
                                                                        @Param("serviceProviderId") Long serviceProviderId,
                                                                        @Param("id") String id);
}
