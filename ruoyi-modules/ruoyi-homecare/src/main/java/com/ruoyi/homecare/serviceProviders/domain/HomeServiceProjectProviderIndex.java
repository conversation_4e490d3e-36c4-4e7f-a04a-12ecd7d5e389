package com.ruoyi.homecare.serviceProviders.domain;

import java.math.BigDecimal;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 服务项目和服务商关联对象 t_home_service_project_provider_index
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
public class HomeServiceProjectProviderIndex extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 商家id
     */
    @Excel(name = "商家id")
    private Long providerId;

    /**
     * 服务项目id
     */
    @Excel(name = "服务项目id")
    private Long serviceProjectId;

    /**
     * 市场价,原价
     */
    @Excel(name = "市场价,原价")
    private BigDecimal marketingPrice;

    /**
     * 单价
     */
    @Excel(name = "单价")
    private BigDecimal price;

    /**
     * 库存
     */
    @Excel(name = "库存")
    private Long stock;

    /**
     * 运费
     */
    @Excel(name = "运费")
    private BigDecimal freight;

    /**
     * 包装费
     */
    @Excel(name = "包装费")
    private BigDecimal packing;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    public Long getServiceProjectId() {
        return serviceProjectId;
    }

    public void setServiceProjectId(Long serviceProjectId) {
        this.serviceProjectId = serviceProjectId;
    }

    public BigDecimal getMarketingPrice() {
        return marketingPrice;
    }

    public void setMarketingPrice(BigDecimal marketingPrice) {
        this.marketingPrice = marketingPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getStock() {
        return stock;
    }

    public void setStock(Long stock) {
        this.stock = stock;
    }

    public BigDecimal getFreight() {
        return freight;
    }

    public void setFreight(BigDecimal freight) {
        this.freight = freight;
    }

    public BigDecimal getPacking() {
        return packing;
    }

    public void setPacking(BigDecimal packing) {
        this.packing = packing;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("providerId", getProviderId())
                .append("serviceProjectId", getServiceProjectId())
                .append("createTime", getCreateTime())
                .append("marketingPrice", getMarketingPrice())
                .append("price", getPrice())
                .append("stock", getStock())
                .append("freight", getFreight())
                .append("packing", getPacking())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
