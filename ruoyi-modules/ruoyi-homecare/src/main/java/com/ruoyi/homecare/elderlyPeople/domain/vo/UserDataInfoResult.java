package com.ruoyi.homecare.elderlyPeople.domain.vo;

import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.homecare.goods.domain.HomeGoodsBaseInfo;
import com.ruoyi.homecare.goods.domain.HomeGoodsCategory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName UserDataInfoResult
 * @Description
 * <AUTHOR>
 * @Date 2022/7/18 10:35
 */
@Data
@ApiModel(value = "通过系统id获取相对应的用户信息")
public class UserDataInfoResult extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 老人信息
     */
    @ApiModelProperty(value = "老人信息")
    private String userId;

    /**
     * 老人家属id
     */
    @ApiModelProperty(value = "老人家属id")
    private String familyId;

    /**
     * 志愿者id
     */
    @ApiModelProperty(value = "志愿者id")
    private Long volunteerId;

    /**
     * 服务商id
     */
    @ApiModelProperty(value = "服务商id")
    private Long serviceProviderId;

    /**
     * 服务商工作人员id
     */
    @ApiModelProperty(value = "服务商工作人员id")
    private Long workId;


}
