package com.ruoyi.homecare.securityguard.service;

import java.util.List;

import com.ruoyi.homecare.securityguard.domain.SecurityGuardManufacturerInfo;

/**
 * 厂商信息Service接口
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
public interface ISecurityGuardManufacturerInfoService {
    /**
     * 查询厂商信息
     *
     * @param id 厂商信息主键
     * @return 厂商信息
     */
    public SecurityGuardManufacturerInfo selectSecurityGuardManufacturerInfoById(Long id);

    /**
     * 查询厂商信息列表
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 厂商信息集合
     */
    public List<SecurityGuardManufacturerInfo> selectSecurityGuardManufacturerInfoList(SecurityGuardManufacturerInfo securityGuardManufacturerInfo);

    /**
     * 新增厂商信息
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 结果
     */
    public int insertSecurityGuardManufacturerInfo(SecurityGuardManufacturerInfo securityGuardManufacturerInfo);

    /**
     * 修改厂商信息
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 结果
     */
    public int updateSecurityGuardManufacturerInfo(SecurityGuardManufacturerInfo securityGuardManufacturerInfo);

    /**
     * 批量删除厂商信息
     *
     * @param ids 需要删除的厂商信息主键集合
     * @return 结果
     */
    public int deleteSecurityGuardManufacturerInfoByIds(Long[] ids);

    /**
     * 删除厂商信息信息
     *
     * @param id 厂商信息主键
     * @return 结果
     */
    public int deleteSecurityGuardManufacturerInfoById(Long id);
}
