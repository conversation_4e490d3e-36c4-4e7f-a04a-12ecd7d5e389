package com.ruoyi.homecare.securityguard.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.homecare.config.MsgProducer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "健康监测数据接收controller", value = "健康监测数据接收controller")
@RequestMapping(value = "/security/guard/")
public class SecurityGuardController extends BaseController {

    @Autowired
    private MsgProducer firstSender;

    @PostMapping("/receiveOWONInfo")
    @ApiOperation("健康监测数据接收")
    public R<String> receiveOWONInfo(@RequestBody JSONObject param) {
        firstSender.send(IdUtil.fastUUID(), param.toString());
        return R.ok();
    }


}
