package com.ruoyi.homecare.elderlyPeople.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.homecare.utils.DictUtils;
import io.seata.common.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "老人信息列表")
public class ElderlyPeopleInfoVo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "老人id")
    private String id;
    @ApiModelProperty(value = "老人名称")
    private String userName;
    @ApiModelProperty(value = "入住状态")
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private String state;
    @ApiModelProperty(value = "入住状态label")
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private String stateStr;
    @ApiModelProperty(value = "入住日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date liveDate;
    @ApiModelProperty(value = "入住日期开始只做查询")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date liveDateBegin;
    @ApiModelProperty(value = "入住日期结束只做查询")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date liveDateEnd;
    @ApiModelProperty(value = "合同到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiredDate;
    @ApiModelProperty(value = "合同到期日期状态")
    private Boolean expiredState;
    @ApiModelProperty(value = "合同到期日期开始只做查询")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiredDateBegin;
    @ApiModelProperty(value = "合同到期日期结束只做查询")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiredDateEnd;
    @ApiModelProperty(value = "套餐名称")
    private String comboName;
    @ApiModelProperty(value = "账户余额")
    private BigDecimal amount;
    @ApiModelProperty(value = "账户余额状态")
    private Boolean amountState;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getStateStr() {
        if (StringUtils.isBlank(this.state)) {
            return this.state;
        }
        return DictUtils.selectDictLabel("live_state", this.state);
    }

    public void setStateStr(String stateStr) {
        this.stateStr = stateStr;
    }
}
