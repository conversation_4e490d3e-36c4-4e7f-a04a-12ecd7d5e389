package com.ruoyi.homecare.securityguard.service;

import cn.hutool.json.JSONObject;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardSensor;

import java.util.List;

/**
 * 拉绳传感器报警信息Service接口
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
public interface ISecurityGuardSensorService {
    /**
     * 查询拉绳传感器报警信息
     *
     * @param id 拉绳传感器报警信息主键
     * @return 拉绳传感器报警信息
     */
    public SecurityGuardSensor selectSecurityGuardSensorById(Long id);

    /**
     * 查询拉绳传感器报警信息列表
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 拉绳传感器报警信息集合
     */
    public List<SecurityGuardSensor> selectSecurityGuardSensorList(SecurityGuardSensor securityGuardSensor);

    /**
     * 新增拉绳传感器报警信息
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 结果
     */
    public int insertSecurityGuardSensor(SecurityGuardSensor securityGuardSensor);

    /**
     * 修改拉绳传感器报警信息
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 结果
     */
    public int updateSecurityGuardSensor(SecurityGuardSensor securityGuardSensor);

    /**
     * 批量删除拉绳传感器报警信息
     *
     * @param ids 需要删除的拉绳传感器报警信息主键集合
     * @return 结果
     */
    public int deleteSecurityGuardSensorByIds(Long[] ids);

    /**
     * 删除拉绳传感器报警信息信息
     *
     * @param id 拉绳传感器报警信息主键
     * @return 结果
     */
    public int deleteSecurityGuardSensorById(Long id);

    int saveMsg(JSONObject argument, String mac);
}
