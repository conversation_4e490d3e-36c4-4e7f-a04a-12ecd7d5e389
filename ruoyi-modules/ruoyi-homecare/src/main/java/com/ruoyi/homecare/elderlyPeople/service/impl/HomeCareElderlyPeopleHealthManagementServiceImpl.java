package com.ruoyi.homecare.elderlyPeople.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthManagement;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleHealthManagementMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareElderlyPeopleHealthManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 老人健康管理信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Service
public class HomeCareElderlyPeopleHealthManagementServiceImpl implements IHomeCareElderlyPeopleHealthManagementService {
    @Autowired
    private HomeCareElderlyPeopleHealthManagementMapper elderlyPeopleHealthManagementMapper;

    /**
     * 查询老人健康管理信息
     *
     * @param id 老人健康管理信息主键
     * @return 老人健康管理信息
     */
    @Override
    public ElderlyPeopleHealthManagement selectElderlyPeopleHealthManagementById(String id) {
        return elderlyPeopleHealthManagementMapper.selectElderlyPeopleHealthManagementById(id);
    }

    /**
     * 查询老人健康管理信息列表
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 老人健康管理信息
     */
    @Override
    public List<ElderlyPeopleHealthManagement> selectElderlyPeopleHealthManagementList(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        return elderlyPeopleHealthManagementMapper.selectElderlyPeopleHealthManagementList(elderlyPeopleHealthManagement);
    }

    /**
     * 新增老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 结果
     */
    @Override
    public int insertElderlyPeopleHealthManagement(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        elderlyPeopleHealthManagement.setCreateTime(DateUtils.getNowDate());
        String id = IdUtils.fastSimpleUUID();
        elderlyPeopleHealthManagement.setId(id);
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleHealthManagement.setCreateBy(String.valueOf(userId));
        return elderlyPeopleHealthManagementMapper.insertElderlyPeopleHealthManagement(elderlyPeopleHealthManagement);
    }

    @Override
    public int save(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        if (StringUtils.isBlank(elderlyPeopleHealthManagement.getId())) {
            return insertElderlyPeopleHealthManagement(elderlyPeopleHealthManagement);
        } else {
            return updateElderlyPeopleHealthManagement(elderlyPeopleHealthManagement);
        }
    }

    /**
     * 修改老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 结果
     */
    @Override
    public int updateElderlyPeopleHealthManagement(ElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        elderlyPeopleHealthManagement.setUpdateTime(DateUtils.getNowDate());
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleHealthManagement.setUpdateBy(String.valueOf(userId));
        return elderlyPeopleHealthManagementMapper.updateElderlyPeopleHealthManagement(elderlyPeopleHealthManagement);
    }

    /**
     * 批量删除老人健康管理信息
     *
     * @param ids 需要删除的老人健康管理信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleHealthManagementByIds(String[] ids) {
        return elderlyPeopleHealthManagementMapper.deleteElderlyPeopleHealthManagementByIds(ids);
    }

    /**
     * 删除老人健康管理信息信息
     *
     * @param id 老人健康管理信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleHealthManagementById(String id) {
        return elderlyPeopleHealthManagementMapper.deleteElderlyPeopleHealthManagementById(id);
    }
}
