package com.ruoyi.homecare.serviceProviders.mapper;

import com.ruoyi.homecare.serviceProviders.domain.HomeGoodsServiceProviderIndex;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 商品和服务商关联Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
@Mapper
public interface HomeGoodsServiceProviderIndexMapper {
    /**
     * 查询商品和服务商关联
     *
     * @param id 商品和服务商关联主键
     * @return 商品和服务商关联
     */
    public HomeGoodsServiceProviderIndex selectHomeGoodsServiceProviderIndexById(Long id);

    /**
     * 查询商品和服务商关联列表
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 商品和服务商关联集合
     */
    public List<HomeGoodsServiceProviderIndex> selectHomeGoodsServiceProviderIndexList(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex);

    /**
     * 新增商品和服务商关联
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 结果
     */
    public int insertHomeGoodsServiceProviderIndex(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex);

    /**
     * 修改商品和服务商关联
     *
     * @param homeGoodsServiceProviderIndex 商品和服务商关联
     * @return 结果
     */
    public int updateHomeGoodsServiceProviderIndex(HomeGoodsServiceProviderIndex homeGoodsServiceProviderIndex);

    /**
     * 删除商品和服务商关联
     *
     * @param id 商品和服务商关联主键
     * @return 结果
     */
    public int deleteHomeGoodsServiceProviderIndexById(Long id);

    /**
     * 批量删除商品和服务商关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeGoodsServiceProviderIndexByIds(Long[] ids);
}
