<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.securityguard.mapper.SecurityGuardDeviceInfoMapper">
    <resultMap type="SecurityGuardDeviceInfo" id="SecurityGuardDeviceInfoResult">
        <result property="id" column="id"/>
        <result property="modelNumber" column="model_number"/>
        <result property="name" column="name"/>
        <result property="manufacturerId" column="manufacturer_id"/>
    </resultMap>

    <sql id="selectSecurityGuardDeviceInfoVo">
        select id, type,model_number, name, manufacturer_id
        from t_security_guard_device_info
    </sql>

    <select id="selectSecurityGuardDeviceInfoList" parameterType="SecurityGuardDeviceInfo"
            resultType="SecurityGuardDeviceInfo">
        select sgdi.id,
        sgdi.type,
        sgdi.model_number as modelNumber,
        sgdi.name,
        sgdi.manufacturer_id as manufacturerId,
        sgmi.name as manufacturerName
        from t_security_guard_device_info sgdi
        left join t_security_guard_manufacturer_info sgmi on sgdi.manufacturer_id = sgmi.id
        <where>
            <if test="id != null">
                and sgdi.id = #{id}
            </if>
            <if test="modelNumber != null  and modelNumber != ''">
                and sgdi.model_number like concat('%', #{modelNumber}, '%')
            </if>
            <if test="name != null  and name != ''">
                and sgdi.name like concat('%', #{name}, '%')
            </if>
            <if test="manufacturerId != null">
                and sgdi.manufacturer_id = #{manufacturerId}
            </if>
        </where>
    </select>

    <select id="selectSecurityGuardDeviceInfoById" parameterType="Long" resultMap="SecurityGuardDeviceInfoResult">
        <include refid="selectSecurityGuardDeviceInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSecurityGuardDeviceInfo" parameterType="SecurityGuardDeviceInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_security_guard_device_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modelNumber != null and modelNumber != ''">
                model_number,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="type != null and type != ''">
                type,
            </if>
            <if test="manufacturerId != null">
                manufacturer_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modelNumber != null and modelNumber != ''">
                #{modelNumber},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="type != null and type != ''">
                #{type},
            </if>
            <if test="manufacturerId != null">
                #{manufacturerId},
            </if>
        </trim>
    </insert>

    <update id="updateSecurityGuardDeviceInfo" parameterType="SecurityGuardDeviceInfo">
        update t_security_guard_device_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelNumber != null and modelNumber != ''">
                model_number = #{modelNumber},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="manufacturerId != null">
                manufacturer_id = #{manufacturerId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityGuardDeviceInfoById" parameterType="Long">
        delete
        from t_security_guard_device_info
        where id = #{id}
    </delete>

    <delete id="deleteSecurityGuardDeviceInfoByIds" parameterType="String">
        delete
        from t_security_guard_device_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
