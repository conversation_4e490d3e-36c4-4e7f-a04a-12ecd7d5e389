<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.securityguard.mapper.SecurityGuardSensorMapper">

    <resultMap type="SecurityGuardSensor" id="SecurityGuardSensorResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="zoneType" column="zone_type"/>
        <result property="zoneId" column="zone_id"/>
        <result property="ep" column="ep"/>
        <result property="ieee" column="ieee"/>
        <result property="status" column="status"/>
        <result property="mac" column="mac"/>
        <result property="createTime" column="create_time"/>
        <result property="devType" column="dev_type"/>
        <result property="contextid" column="contextid"/>
    </resultMap>

    <sql id="selectSecurityGuardSensorVo">
        select id, name, zone_type, zone_id, ep, ieee, status, mac, create_time,dev_type,contextid from
        t_security_guard_sensor
    </sql>

    <select id="selectSecurityGuardSensorList" parameterType="SecurityGuardSensor"
            resultMap="SecurityGuardSensorResult">
        <include refid="selectSecurityGuardSensorVo"/>
        <where>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')

            </if>
            <if test="devType != null">

                and dev_type = #{devType}
            </if>
        </where>

        order by create_time desc
    </select>

    <select id="selectSecurityGuardSensorById" parameterType="Long" resultMap="SecurityGuardSensorResult">
        <include refid="selectSecurityGuardSensorVo"/>
        where id = #{id}
    </select>

    <insert id="insertSecurityGuardSensor" parameterType="SecurityGuardSensor">
        insert into t_security_guard_sensor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="zoneType != null">zone_type,</if>
            <if test="zoneId != null">zone_id,</if>
            <if test="ep != null">ep,</if>
            <if test="ieee != null">ieee,</if>
            <if test="status != null">status,</if>
            <if test="mac != null">mac,</if>
            <if test="createTime != null">create_time,</if>
            <if test="devType != null">dev_type,</if>
            <if test="contextid != null">contextid,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="zoneType != null">#{zoneType},</if>
            <if test="zoneId != null">#{zoneId},</if>
            <if test="ep != null">#{ep},</if>
            <if test="ieee != null">#{ieee},</if>
            <if test="status != null">#{status},</if>
            <if test="mac != null">#{mac},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="devType != null">#{devType},</if>
            <if test="contextid != null">#{contextid},</if>
        </trim>
    </insert>

    <update id="updateSecurityGuardSensor" parameterType="SecurityGuardSensor">
        update t_security_guard_sensor
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="zoneType != null">zone_type = #{zoneType},</if>
            <if test="zoneId != null">zone_id = #{zoneId},</if>
            <if test="ep != null">ep = #{ep},</if>
            <if test="ieee != null">ieee = #{ieee},</if>
            <if test="status != null">status = #{status},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="devType != null">dev_type = #{devType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityGuardSensorById" parameterType="Long">
        delete from t_security_guard_sensor where id = #{id}
    </delete>

    <delete id="deleteSecurityGuardSensorByIds" parameterType="String">
        delete from t_security_guard_sensor where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
