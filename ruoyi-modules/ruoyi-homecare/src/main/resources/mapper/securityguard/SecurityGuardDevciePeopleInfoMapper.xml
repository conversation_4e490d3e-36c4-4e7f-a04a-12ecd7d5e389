<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.securityguard.mapper.SecurityGuardDevciePeopleInfoMapper">
    <resultMap type="SecurityGuardDevciePeopleInfo" id="SecurityGuardDevciePeopleInfoResult">
        <result property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="peopleId" column="people_id"/>
        <result property="contextid" column="contextid"/>
        <result property="type" column="type"/>
    </resultMap>

    <sql id="selectSecurityGuardDevciePeopleInfoVo">
        select id, device_id, people_id, contextid, type
        from t_security_guard_devcie_people_info
    </sql>

    <select id="selectSecurityGuardDevciePeopleInfoList" parameterType="SecurityGuardDevciePeopleInfo"
            resultType="SecurityGuardDevciePeopleInfo">
        select sgdpi.id,
        sgdpi.device_id as deviceId,
        sgdpi.people_id as peopleId,
        sgdpi.contextid,
        sgdpi.type,
        sgdi.name as deviceName,
        sgdi.model_number as deviceModelNumber,
        hepi.name as peopleName,
        hepi.phone as peoplePhone
        from t_security_guard_devcie_people_info sgdpi
        left join t_security_guard_device_info sgdi on sgdpi.device_id = sgdi.id
        left join t_home_elderly_people_info hepi on sgdpi.people_id = hepi.id
        <where>
            <if test="deviceId != null">
                and sgdpi.device_id = #{deviceId}
            </if>
            <if test="peopleId != null  and peopleId != ''">
                and sgdpi.people_id = #{peopleId}
            </if>
            <if test="contextid != null  and contextid != ''">
                and sgdpi.contextid = #{contextid}
            </if>
        </where>
    </select>

    <select id="selectSecurityGuardDevciePeopleInfoById" parameterType="Long"
            resultMap="SecurityGuardDevciePeopleInfoResult">
        <include refid="selectSecurityGuardDevciePeopleInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSecurityGuardDevciePeopleInfo" parameterType="SecurityGuardDevciePeopleInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_security_guard_devcie_people_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">
                device_id,
            </if>
            <if test="peopleId != null and peopleId != ''">
                people_id,
            </if>
            <if test="contextid != null and contextid != ''">
                contextid,
            </if>
            <if test="type != null and type != ''">
                type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">
                #{deviceId},
            </if>
            <if test="peopleId != null and peopleId != ''">
                #{peopleId},
            </if>
            <if test="contextid != null and contextid != ''">
                #{contextid},
            </if>
            <if test="type != null and type != ''">
                #{type},
            </if>
        </trim>
    </insert>

    <update id="updateSecurityGuardDevciePeopleInfo" parameterType="SecurityGuardDevciePeopleInfo">
        update t_security_guard_devcie_people_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">
                device_id = #{deviceId},
            </if>
            <if test="peopleId != null and peopleId != ''">
                people_id = #{peopleId},
            </if>
            <if test="contextid != null and contextid != ''">
                contextid = #{contextid},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityGuardDevciePeopleInfoById" parameterType="Long">
        delete
        from t_security_guard_devcie_people_info
        where id = #{id}
    </delete>

    <delete id="deleteSecurityGuardDevciePeopleInfoByIds" parameterType="String">
        delete
        from t_security_guard_devcie_people_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
