<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.securityguard.mapper.SecurityGuardManufacturerInfoMapper">

    <resultMap type="SecurityGuardManufacturerInfo" id="SecurityGuardManufacturerInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="contacts" column="contacts"/>
        <result property="number" column="number"/>
        <result property="address" column="address"/>
    </resultMap>

    <resultMap id="SecurityGuardManufacturerInfoSecurityGuardDeviceInfoResult" type="SecurityGuardManufacturerInfo"
               extends="SecurityGuardManufacturerInfoResult">
        <collection property="securityGuardDeviceInfoList" notNullColumn="sub_id" javaType="java.util.List"
                    resultMap="SecurityGuardDeviceInfoResult"/>
    </resultMap>

    <resultMap type="SecurityGuardDeviceInfo" id="SecurityGuardDeviceInfoResult">
        <result property="id" column="sub_id"/>
        <result property="modelNumber" column="sub_model_number"/>
        <result property="name" column="sub_name"/>
        <result property="manufacturerId" column="sub_manufacturer_id"/>
    </resultMap>

    <sql id="selectSecurityGuardManufacturerInfoVo">
        select id, name, contacts, number, address from t_security_guard_manufacturer_info
    </sql>

    <select id="selectSecurityGuardManufacturerInfoList" parameterType="SecurityGuardManufacturerInfo"
            resultMap="SecurityGuardManufacturerInfoResult">
        <include refid="selectSecurityGuardManufacturerInfoVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="contacts != null  and contacts != ''">and contacts like concat('%', #{contacts}, '%')</if>
            <if test="number != null ">and number like concat('%', #{number}, '%')</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
        </where>
    </select>

    <select id="selectSecurityGuardManufacturerInfoById" parameterType="Long"
            resultMap="SecurityGuardManufacturerInfoSecurityGuardDeviceInfoResult">
        select a.id, a.name, a.contacts, a.number, a.address,
        b.id as sub_id, b.model_number as sub_model_number, b.name as sub_name, b.manufacturer_id as sub_manufacturer_id
        from t_security_guard_manufacturer_info a
        left join t_security_guard_device_info b on b.manufacturer_id = a.id
        where a.id = #{id}
    </select>

    <insert id="insertSecurityGuardManufacturerInfo" parameterType="SecurityGuardManufacturerInfo">
        insert into t_security_guard_manufacturer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="contacts != null">contacts,</if>
            <if test="number != null">number,</if>
            <if test="address != null">address,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="contacts != null">#{contacts},</if>
            <if test="number != null">#{number},</if>
            <if test="address != null">#{address},</if>
        </trim>
    </insert>

    <update id="updateSecurityGuardManufacturerInfo" parameterType="SecurityGuardManufacturerInfo">
        update t_security_guard_manufacturer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="contacts != null">contacts = #{contacts},</if>
            <if test="number != null">number = #{number},</if>
            <if test="address != null">address = #{address},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityGuardManufacturerInfoById" parameterType="Long">
        delete from t_security_guard_manufacturer_info where id = #{id}
    </delete>

    <delete id="deleteSecurityGuardManufacturerInfoByIds" parameterType="String">
        delete from t_security_guard_manufacturer_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSecurityGuardDeviceInfoByManufacturerIds" parameterType="String">
        delete from t_security_guard_device_info where manufacturer_id in
        <foreach item="manufacturerId" collection="array" open="(" separator="," close=")">
            #{manufacturerId}
        </foreach>
    </delete>

    <delete id="deleteSecurityGuardDeviceInfoByManufacturerId" parameterType="Long">
        delete from t_security_guard_device_info where manufacturer_id = #{manufacturerId}
    </delete>

    <insert id="batchSecurityGuardDeviceInfo">
        insert into t_security_guard_device_info( id, model_number, name, manufacturer_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.modelNumber}, #{item.name}, #{item.manufacturerId})
        </foreach>
    </insert>
</mapper>
