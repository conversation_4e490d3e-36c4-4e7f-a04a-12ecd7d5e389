<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.securityguard.mapper.SecurityGuardBledataMapper">
    <resultMap type="SecurityGuardBledata" id="SecurityGuardBledataResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="zoneType" column="zone_type"/>
        <result property="zoneId" column="zone_id"/>
        <result property="ep" column="ep"/>
        <result property="ieee" column="ieee"/>
        <result property="mac" column="mac"/>
        <result property="createTime" column="create_time"/>
        <result property="sbp" column="SBP"/>
        <result property="dbp" column="DBP"/>
        <result property="sphygmus" column="Sphygmus"/>
        <result property="glucose" column="glucose"/>
        <result property="glucoseParse" column="glucose_parse"/>
        <result property="devType" column="dev_type"/>
    </resultMap>

    <sql id="selectSecurityGuardBledataVo">
        select id,
        name,
        zone_type,
        zone_id,
        ep,
        ieee,
        mac,
        create_time,
        SBP,
        DBP,
        Sphygmus,
        glucose,
        glucose_parse,
        dev_type,
        contextid
        from t_security_guard_bledata
    </sql>

    <select id="selectSecurityGuardBledataList" parameterType="SecurityGuardBledata"
            resultMap="SecurityGuardBledataResult">
        <include refid="selectSecurityGuardBledataVo"/>
        <where>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="devType != null">
                and dev_type = #{devType}
            </if>
        </where>
    </select>

    <select id="selectSecurityGuardBledataById" parameterType="Long" resultMap="SecurityGuardBledataResult">
        <include refid="selectSecurityGuardBledataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSecurityGuardBledata" parameterType="SecurityGuardBledata">
        insert into t_security_guard_bledata
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="zoneType != null">
                zone_type,
            </if>
            <if test="zoneId != null">
                zone_id,
            </if>
            <if test="ep != null">
                ep,
            </if>
            <if test="ieee != null">
                ieee,
            </if>
            <if test="mac != null">
                mac,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="sbp != null">
                SBP,
            </if>
            <if test="dbp != null">
                DBP,
            </if>
            <if test="sphygmus != null">
                Sphygmus,
            </if>
            <if test="devType != null">
                dev_type,
            </if>
            <if test="glucose != null">
                glucose,
            </if>
            <if test="glucoseParse != null">
                glucose_parse,
            </if>
            <if test="contextid != null">
                contextid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="zoneType != null">
                #{zoneType},
            </if>
            <if test="zoneId != null">
                #{zoneId},
            </if>
            <if test="ep != null">
                #{ep},
            </if>
            <if test="ieee != null">
                #{ieee},
            </if>
            <if test="mac != null">
                #{mac},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="sbp != null">
                #{sbp},
            </if>
            <if test="dbp != null">
                #{dbp},
            </if>
            <if test="sphygmus != null">
                #{sphygmus},
            </if>
            <if test="devType != null">
                #{devType},
            </if>
            <if test="glucose != null">
                #{glucose},
            </if>
            <if test="glucoseParse != null">
                #{glucoseParse},
            </if>
            <if test="contextid != null">
                #{contextid},
            </if>
        </trim>
    </insert>

    <update id="updateSecurityGuardBledata" parameterType="SecurityGuardBledata">
        update t_security_guard_bledata
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">
                name = #{name},
            </if>
            <if test="zoneType != null">
                zone_type = #{zoneType},
            </if>
            <if test="zoneId != null">
                zone_id = #{zoneId},
            </if>
            <if test="ep != null">
                ep = #{ep},
            </if>
            <if test="ieee != null">
                ieee = #{ieee},
            </if>
            <if test="mac != null">
                mac = #{mac},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="sbp != null">
                SBP = #{sbp},
            </if>
            <if test="dbp != null">
                DBP = #{dbp},
            </if>
            <if test="sphygmus != null">
                Sphygmus = #{sphygmus},
            </if>
            <if test="devType != null">
                dev_type = #{devType},
            </if>
            <if test="glucose != null">
                glucose = #{glucose},
            </if>
            <if test="glucoseParse != null">
                glucose_parse = #{glucoseParse},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityGuardBledataById" parameterType="Long">
        delete
        from t_security_guard_bledata
        where id = #{id}
    </delete>

    <delete id="deleteSecurityGuardBledataByIds" parameterType="String">
        delete
        from t_security_guard_bledata where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
