<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeAssociationInfoMapper">

    <resultMap type="HomeAssociationInfo" id="HomeAssociationInfoResult">
        <result property="id" column="id"/>
        <result property="img" column="img"/>
        <result property="name" column="name"/>
        <result property="firstContacts" column="first_contacts"/>
        <result property="firstContactsPhone" column="first_contacts_phone"/>
        <result property="secondContacts" column="second_contacts"/>
        <result property="secondContactsPhone" column="second_contacts_phone"/>
        <result property="streetId" column="street_id"/>
        <result property="serviceField" column="service_field"/>
        <result property="address" column="address"/>
        <result property="teamIntroduce" column="team_introduce"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectHomeAssociationInfoVo">
        SELECT
        a.id,
        a.img,
        a.name,
        a.first_contacts,
        a.second_contacts,
        b.name as firstContactsName,
        CONCAT(b.name,'(',b.phone,')') as firstLabel,
        a.first_contacts_phone,
        c.name as secondContactsName,
        CONCAT(c.name,'(',c.phone,')') as secondLabel,
        a.second_contacts_phone,
        a.street_id,
        a.service_field,
        a.address,
        a.team_introduce,
        a.status,
        a.del_flag,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.remark
        FROM
        t_home_association_info as a
        left join t_home_volunteer_base_info as b on a.first_contacts = b.id
        left join t_home_volunteer_base_info as c on a.second_contacts = c.id
    </sql>

    <select id="selectHomeAssociationInfoList" parameterType="HomeAssociationInfo"
            resultMap="HomeAssociationInfoResult">
        <include refid="selectHomeAssociationInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="firstContacts != null  and firstContacts != ''">and a.first_contacts = #{firstContacts}</if>
            <if test="secondContacts != null  and secondContacts != ''">and a.second_contacts = #{secondContacts}</if>
            <if test="streetId != null ">and a.street_id = #{streetId}</if>
            <if test="serviceField != null  and serviceField != ''">and a.service_field = #{serviceField}</if>
            <if test="address != null  and address != ''">and a.address like concat('%', #{address}, '%')</if>
            <if test="teamIntroduce != null  and teamIntroduce != ''">and a.team_introduce = #{teamIntroduce}</if>
            <if test="status != null ">and a.status = #{status}</if>
        </where>
    </select>

    <select id="getAdminPcList" parameterType="HomeAssociationInfo" resultType="HomeAssociationInfoVo">
        SELECT
        a.id,
        a.img,
        a.name,
        a.first_contacts,
        a.second_contacts,
        b.name as firstContactsName,
        a.first_contacts_phone as firstContactsPhone,
        c.name as secondContactsName,
        a.second_contacts_phone as secondContactsPhone,
        IFNULL(d.num,0) as num,
        a.street_id,
        a.service_field,
        a.address,
        IFNULL(e.time,0) as serviceDuration,
        a.team_introduce,
        a.status,
        a.del_flag,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.remark
        FROM
        t_home_association_info AS a
        LEFT JOIN t_home_volunteer_base_info AS b ON a.first_contacts = b.id
        LEFT JOIN t_home_volunteer_base_info AS c ON a.second_contacts = c.id
        left join ( select count(*) as num ,association_id from t_home_association_volunteer_index where del_flag = '0'
        and status = 3 group by association_id ) AS d ON a.id = d.association_id
        LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
        t_home_public_welfare_activities GROUP BY event_promoter ) AS e ON a.first_contacts = e.event_promoter
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="firstContacts != null  and firstContacts != ''">and a.first_contacts = #{firstContacts}</if>
            <if test="secondContacts != null  and secondContacts != ''">and a.second_contacts = #{secondContacts}</if>
            <if test="streetId != null ">and a.street_id = #{streetId}</if>
            <if test="serviceField != null  and serviceField != ''">and a.service_field = #{serviceField}</if>
            <if test="address != null  and address != ''">and a.address like concat('%', #{address}, '%')</if>
            <if test="teamIntroduce != null  and teamIntroduce != ''">and a.team_introduce = #{teamIntroduce}</if>
            <if test="status != null ">and a.status = #{status}</if>
        </where>
    </select>

    <select id="selectHomeAssociationInfoById" parameterType="Long" resultMap="HomeAssociationInfoResult">
        <include refid="selectHomeAssociationInfoVo"/>
        where a.id = #{id}
    </select>

    <select id="selectHomeAssociationInfoByFirstContacts" parameterType="Long" resultMap="HomeAssociationInfoResult">
        <include refid="selectHomeAssociationInfoVo"/>
        where a.first_contacts = #{firstContacts} and a.status != 2 and a.del_flag = '0'
    </select>

    <select id="selectHomeAssociationInfoApprovedByFirstContacts" parameterType="Long"
            resultMap="HomeAssociationInfoResult">
        <include refid="selectHomeAssociationInfoVo"/>
        where a.first_contacts = #{firstContacts} and a.status > 2 and a.del_flag = '0'
    </select>

    <select id="selectHomeAssociationInfoByFirstContactsAll" parameterType="Long" resultMap="HomeAssociationInfoResult">
        <include refid="selectHomeAssociationInfoVo"/>
        where a.first_contacts = #{firstContacts} and a.del_flag = '0'
    </select>

    <insert id="insertHomeAssociationInfo" parameterType="HomeAssociationInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_home_association_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="img != null">img,</if>
            <if test="name != null">name,</if>
            <if test="firstContacts != null">first_contacts,</if>
            <if test="firstContacts != null">first_contacts_phone,</if>
            <if test="secondContacts != null">second_contacts,</if>
            <if test="secondContacts != null">second_contacts_phone,</if>
            <if test="streetId != null">street_id,</if>
            <if test="serviceField != null">service_field,</if>
            <if test="address != null">address,</if>
            <if test="teamIntroduce != null">team_introduce,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="img != null">#{img},</if>
            <if test="name != null">#{name},</if>
            <if test="firstContacts != null">#{firstContacts},</if>
            <if test="firstContacts != null">#{firstContactsPhone},</if>
            <if test="secondContacts != null">#{secondContacts},</if>
            <if test="secondContacts != null">#{secondContactsPhone},</if>
            <if test="streetId != null">#{streetId},</if>
            <if test="serviceField != null">#{serviceField},</if>
            <if test="address != null">#{address},</if>
            <if test="teamIntroduce != null">#{teamIntroduce},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeAssociationInfo" parameterType="HomeAssociationInfo">
        update t_home_association_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="img != null">img = #{img},</if>
            <if test="name != null">name = #{name},</if>
            <if test="firstContacts != null">first_contacts = #{firstContacts},</if>
            <if test="firstContactsPhone != null">first_contacts_phone = #{firstContactsPhone},</if>
            <if test="secondContacts != null">second_contacts = #{secondContacts},</if>
            <if test="secondContactsPhone != null">second_contacts_Phone = #{secondContactsPhone},</if>
            <if test="streetId != null">street_id = #{streetId},</if>
            <if test="serviceField != null">service_field = #{serviceField},</if>
            <if test="address != null">address = #{address},</if>
            <if test="teamIntroduce != null">team_introduce = #{teamIntroduce},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeAssociationInfoById" parameterType="Long">
        delete from t_home_association_info where id = #{id}
    </delete>

    <delete id="deleteHomeAssociationInfoByIds" parameterType="String">
        update t_home_association_info set del_flag = '1',status = '5' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getAdminPcAssociationMembersList" parameterType="HomeAssociationVolunteerIndexParam"
            resultType="HomeAssociationVolunteerVo">
        SELECT
        b.name,
        b.phone,
        b.id_card_num,
        b.political_status,
        b.service_time,
        b.img,
        a.`status`,
        a.create_time as createTime,
        a.id
        FROM
        t_home_association_volunteer_index AS a
        LEFT JOIN t_home_volunteer_base_info AS b on a.volunteer_id = b.id
        <where>
            a.del_flag = '0' and a.association_id = #{associationId}
            <if test="name != null and name != '' ">
                and b.name like concat('%',#{name},'%')
            </if>
            <if test="status != null and status != '' ">
                and a.status = #{status}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="getAppAssociationInfo" parameterType="Long" resultMap="HomeAssociationInfoResult">
        <include refid="selectHomeAssociationInfoVo"/>
        where a.del_flag = '0' and a.first_contacts = #{volunteerId} order by a.create_time desc limit 1
    </select>

    <select id="getAppAssociationRecommendList" resultType="HomeAppAssociationVolunteerVo"
            parameterType="HomeAppAssociationVolunteerParam">
        SELECT
        main.*
        FROM
        (
        SELECT
        a.id,
        a.name,
        IFNULL(e.time,0) as serviceDuration,
        IFNULL( c.num, 0 ) as num,
        d.NAME AS streetName,
        IFNULL( b.status, 0 ) AS status
        FROM
        t_home_association_info AS a
        LEFT JOIN ( SELECT * FROM t_home_association_volunteer_index WHERE volunteer_id = #{volunteerId} ) AS b ON a.id
        = b.association_id
        LEFT JOIN ( select count(*) as num ,association_id from t_home_association_volunteer_index where del_flag = '0'
        and status = 3 group by association_id ) AS c ON a.id = c.association_id
        LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
        t_home_public_welfare_activities GROUP BY event_promoter ) AS e ON a.first_contacts = e.event_promoter
        LEFT JOIN t_home_street_info AS d ON d.id = a.street_id
        WHERE
        a.STATUS > 2
        AND a.del_flag = '0') as main
        <where>
            <if test="name != null and name != '' ">
                and main.name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="getAppJoinedAssociationList" resultType="HomeAppAssociationVolunteerVo"
            parameterType="HomeAppAssociationVolunteerParam">
        SELECT
        d.name AS streetName,
        a.id,
        b.name,
        IFNULL( e.time, 0 ) serviceDuration,
        IFNULL( c.num, 0 ) AS num,
        IFNULL( a.status, 0 ) AS `status`
        FROM
        t_home_association_volunteer_index AS a
        LEFT JOIN t_home_association_info AS b ON a.association_id = b.id
        LEFT JOIN ( SELECT count(*) AS num, association_id FROM t_home_association_volunteer_index WHERE del_flag = '0'
        AND STATUS = 3 GROUP BY association_id ) AS c ON a.association_id = c.association_id
        LEFT JOIN t_home_street_info AS d ON d.id = b.street_id
        LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
        t_home_public_welfare_activities GROUP BY event_promoter ) AS e ON b.first_contacts = e.event_promoter
        <where>
            <if test="name != null and name != '' ">
                and b.name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="getFirstContactsList" resultType="cn.hutool.json.JSONObject" parameterType="String">

        SELECT
        a.name as name,
        a.id as value,
        CONCAT(a.name,'(',a.phone,')') selectLabel,
        a.phone
        FROM
        (select name,id,phone,create_time from t_home_volunteer_base_info where status = 3 and del_flag = '0' ) AS a
        LEFT JOIN (select first_contacts,status from t_home_association_info where status in(1,3,4) and del_flag = '0')
        AS b ON a.id = b.first_contacts
        where b.status is null
        <if test="name != null and name != '' ">
            and a.name like concat('%',#{name},'%')
        </if>
        order by a.create_time desc

    </select>


    <select id="getAppAssociationCreateList" resultType="HomeAppAssociationVolunteerVo"
            parameterType="HomeAppAssociationVolunteerParam">
        <choose>
            <when test="flag==1">

                select
                main.*
                from
                (
                SELECT
                a.id,
                a.name,
                a.img,
                ifnull( b.status, 0 ) AS status,
                d.name AS streetName,
                IFNULL( e.num, 0 ) as num,
                IFNULL( c.time, 0 ) AS time ,
                a.create_time as createTime,
                '1' as flag
                FROM
                t_home_association_info AS a
                LEFT JOIN ( SELECT id, status AS status FROM t_home_association_info WHERE first_contacts =
                #{volunteerId} AND status != 2 ) AS b ON a.id = b.id
                LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
                t_home_public_welfare_activities GROUP BY event_promoter ) AS c ON a.first_contacts = c.event_promoter
                LEFT JOIN t_home_street_info AS d ON d.id = a.street_id
                LEFT JOIN ( select count(*) as num ,association_id from t_home_association_volunteer_index where
                del_flag = '0' and status = 3 group by association_id ) AS e ON a.id = e.association_id
                WHERE
                a.`status` != 2
                AND a.del_flag = 0
                ) as main
                <where>
                    <if test="name != null and name != '' ">
                        and main.name like concat('%',#{name},'%')
                    </if>
                </where>
                order by main.status desc,main.createTime desc

            </when>

            <otherwise>

                SELECT
                main.*
                FROM
                (
                SELECT
                a.id,
                a.name,
                a.img,
                IFNULL( c.time, 0 ) AS time ,
                IFNULL( e.num, 0 ) as num,
                d.NAME AS streetName,
                IFNULL( b.status, 0 ) AS status,
                a.create_time as createTime,
                b.id as indexId,
                '2' as flag
                FROM
                t_home_association_info AS a
                LEFT JOIN ( SELECT * FROM t_home_association_volunteer_index WHERE volunteer_id = #{volunteerId} and
                del_flag = '0' and status in ('1','3')) AS b ON a.id = b.association_id
                LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
                t_home_public_welfare_activities GROUP BY event_promoter ) AS c ON a.first_contacts = c.event_promoter
                LEFT JOIN ( select count(*) as num ,association_id from t_home_association_volunteer_index where
                del_flag = '0' and status = 3 group by association_id ) AS e ON a.id = e.association_id
                LEFT JOIN t_home_street_info AS d ON d.id = a.street_id
                WHERE
                a.status > 2
                AND a.del_flag = '0') as main
                <where>
                    <if test="name != null and name != '' ">
                        and main.name like concat('%',#{name},'%')
                    </if>
                </where>
                order by main.status desc,main.createTime desc

            </otherwise>
        </choose>
    </select>


    <select id="getAppMyAssociationCreateInfo" resultType="HomeAppAssociationVolunteerVo"
            parameterType="HomeAppAssociationVolunteerParam">
        <choose>
            <when test="flag==1">
                SELECT
                a.id,
                a.name,
                a.img,
                a.status AS status,
                d.name AS streetName,
                IFNULL( e.num, 0 ) as num,
                IFNULL( c.time, 0 ) AS time ,
                a.create_time as createTime,
                '1' as flag

                FROM t_home_association_info as a
                LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
                t_home_public_welfare_activities GROUP BY event_promoter ) AS c ON a.first_contacts = c.event_promoter
                LEFT JOIN t_home_street_info AS d ON d.id = a.street_id
                LEFT JOIN ( select count(*) as num ,association_id from t_home_association_volunteer_index where
                del_flag = '0' and status = 3 group by association_id ) AS e ON a.id = e.association_id
                WHERE first_contacts = #{volunteerId} AND status != 2

            </when>

            <otherwise>

                SELECT
                a.id,
                a.name,
                a.img,
                b.id as indexId,
                IFNULL( c.time, 0 ) AS time ,
                IFNULL( e.num, 0 ) as num,
                d.NAME AS streetName,
                IFNULL( b.status, 0 ) AS status,
                a.create_time as createTime,
                '2' as flag
                FROM
                ( SELECT * FROM t_home_association_volunteer_index WHERE volunteer_id = #{volunteerId} and del_flag =
                '0' and status in('1','3') ) AS b
                left join t_home_association_info AS a ON a.id = b.association_id
                LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
                t_home_public_welfare_activities GROUP BY event_promoter ) AS c ON a.first_contacts = c.event_promoter
                LEFT JOIN ( select count(*) as num ,association_id from t_home_association_volunteer_index where
                del_flag = '0' and status = 3 group by association_id ) AS e ON a.id = e.association_id
                LEFT JOIN t_home_street_info AS d ON d.id = a.street_id

            </otherwise>
        </choose>
    </select>


    <select id="getAppAssociationInfoById" parameterType="Long" resultType="AssociationInfoVo">
        SELECT
        main.*
        FROM
        (
        SELECT
        a.id,
        a.name,
        a.img,
        IFNULL( c.time, 0 ) AS time,
        IFNULL( e.num, 0 ) AS num,
        IFNULL(c.frequency,0) as frequency,
        d.name AS streetName,
        a.status,
        a.team_introduce,
        a.create_time AS createTime
        FROM
        t_home_association_info AS a
        LEFT JOIN ( SELECT count(id) as frequency, sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time,
        event_promoter FROM t_home_public_welfare_activities GROUP BY event_promoter ) AS c ON a.first_contacts =
        c.event_promoter
        LEFT JOIN ( SELECT count(*) AS num, association_id FROM t_home_association_volunteer_index WHERE del_flag = '0'
        AND STATUS = 3 GROUP BY association_id ) AS e ON a.id = e.association_id
        LEFT JOIN t_home_street_info AS d ON d.id = a.street_id
        ) AS main
        where main.id = #{id}
    </select>
    <select id="getAppMyAssociationMembersList" parameterType="HomeAssociationVolunteerIndexParam"
            resultType="HomeAssociationVolunteerVo">
        SELECT
        b.name,
        b.phone,
        b.id_card_num,
        b.political_status,
        b.service_time,
        b.img,
        a.`status`,
        a.create_time as createTime,
        a.id
        FROM
        t_home_association_volunteer_index AS a
        LEFT JOIN t_home_volunteer_base_info AS b on a.volunteer_id = b.id
        <where>
            a.del_flag = '0' and a.association_id = #{associationId} and a.status in (1,2,3)
            <if test="name != null and name != '' ">
                and b.name like concat('%',#{name},'%')
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>
