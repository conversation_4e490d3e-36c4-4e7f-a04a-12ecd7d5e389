<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomePublicWelfareActivitiesMapper">

    <resultMap type="com.ruoyi.homecare.volunteers.domain.HomePublicWelfareActivities"
               id="HomePublicWelfareActivitiesResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="eventPromoter" column="event_promoter"/>
        <result property="eventContact" column="event_contact"/>
        <result property="eventContactPhone" column="event_contact_phone"/>
        <result property="activeState" column="active_state"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="requiredNumber" column="required_number"/>
        <result property="whetherNeedEgister" column="whether_need_egister"/>
        <result property="earnedTimeCoin" column="earned_time_coin"/>
        <result property="realEarnedTimeCoin" column="real_earned_time_coin"/>
        <result property="registrationEndTime" column="registration_end_time"/>
        <result property="inviteParticipants" column="invite_participants"/>
        <result property="activitySite" column="activity_site"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="state" column="state"/>
        <result property="exchangeState" column="exchange_state"/>
        <result property="signIn" column="signIn"/>
        <result property="num" column="num"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="eventPromoterName" column="eventPromoterName"/>
        <result property="address" column="address"/>
    </resultMap>

    <sql id="selectHomePublicWelfareActivitiesVo">
        SELECT
        a.id,
        a.name,
        a.event_promoter,
        c.name as eventPromoterName,
        a.event_contact,
        d.name as eventContactName,
        a.event_contact_phone,
        a.active_state,
        a.begin_time,
        a.end_time,
        a.state,
        a.exchange_state,
        a.required_number,
        a.whether_need_egister,
        a.earned_time_coin,
        a.real_earned_time_coin,
        a.registration_end_time,
        a.invite_participants,
        a.activity_site,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.img,
        a.address,
        a.audit_status,
        a.latitude,
        a.longitude,
        a.remark,
        IFNULL(b.num,0) as num,
        IFNULL(b.signIn,0) as signIn
        FROM
        t_home_public_welfare_activities AS a
        LEFT JOIN (select count(id) as num ,sum(sign_in) as signIn,activity_id from t_home_activity_apply_index where
        del_flag = '0' GROUP BY activity_id) AS b ON a.id = b.activity_id
        left join t_home_volunteer_base_info as c on c.id = a.event_promoter
        left join t_home_volunteer_base_info as d on d.id = a.event_contact
    </sql>

    <select id="selectHomePublicWelfareActivitiesList"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomePublicWelfareActivities"
            resultMap="HomePublicWelfareActivitiesResult">
        <include refid="selectHomePublicWelfareActivitiesVo"/>
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="eventPromoter != null  and eventPromoter != ''">and a.event_promoter = #{eventPromoter}</if>
            <if test="eventContact != null  and eventContact != ''">and a.event_contact like concat('%',
                #{eventContact}, '%')
            </if>
            <if test="eventContactPhone != null  and eventContactPhone != ''">and a.event_contact_phone like concat('%',
                #{eventContactPhone}, '%')
            </if>
            <if test="activeState != null  and activeState != ''">and a.active_state = #{activeState}</if>
            <if test="params.beginBeginTime != null and params.beginBeginTime != '' and params.endBeginTime != null and params.endBeginTime != ''">
                and a.begin_time between #{params.beginBeginTime} and #{params.endBeginTime}
            </if>
            <if test="endTime != null ">and a.end_time = #{endTime}</if>
            <if test="exchangeState != null and exchangeState != ''">and a.exchange_state = #{exchangeState}</if>
            <if test="requiredNumber != null  and requiredNumber != ''">and a.required_number = #{requiredNumber}</if>
            <if test="whetherNeedEgister != null  and whetherNeedEgister != ''">and a.whether_need_egister =
                #{whetherNeedEgister}
            </if>
            <if test="earnedTimeCoin != null  and earnedTimeCoin != ''">and a.earned_time_coin = #{earnedTimeCoin}</if>
            <if test="params.beginRegistrationEndTime != null and params.beginRegistrationEndTime != '' and params.endRegistrationEndTime != null and params.endRegistrationEndTime != ''">
                and registration_end_time between #{params.beginRegistrationEndTime} and
                #{params.endRegistrationEndTime}
            </if>
            <if test="inviteParticipants != null  and inviteParticipants != ''">and a.invite_participants =
                #{inviteParticipants}
            </if>
            <if test="activitySite != null  and activitySite != ''">and a.activity_site = #{activitySite}</if>
            <if test="auditStatus != null  and auditStatus != ''">and a.audit_status = #{auditStatus}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHomePublicWelfareActivitiesById" parameterType="Long"
            resultMap="HomePublicWelfareActivitiesResult">
        <include refid="selectHomePublicWelfareActivitiesVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertHomePublicWelfareActivities"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomePublicWelfareActivities" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_home_public_welfare_activities
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="eventPromoter != null">event_promoter,</if>
            <if test="state != null">state,</if>
            <if test="exchangeState != null">exchange_state,</if>
            <if test="eventContact != null">event_contact,</if>
            <if test="eventContactPhone != null">event_contact_phone,</if>
            <if test="activeState != null">active_state,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="requiredNumber != null">required_number,</if>
            <if test="whetherNeedEgister != null">whether_need_egister,</if>
            <if test="earnedTimeCoin != null">earned_time_coin,</if>
            <if test="realEarnedTimeCoin != null">real_earned_time_coin,</if>
            <if test="registrationEndTime != null">registration_end_time,</if>
            <if test="inviteParticipants != null">invite_participants,</if>
            <if test="activitySite != null">activity_site,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="img != null and img != '' ">img,</if>
            <if test="auditStatus != null and auditStatus != '' ">audit_status,</if>
            <if test="longitude != null and longitude != '' ">longitude,</if>
            <if test="latitude != null and latitude != '' ">latitude,</if>
            <if test="address != null and address != '' ">address,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="eventPromoter != null">#{eventPromoter},</if>
            <if test="state != null">#{state},</if>
            <if test="exchangeState != null">#{exchangeState},</if>
            <if test="eventContact != null">#{eventContact},</if>
            <if test="eventContactPhone != null">#{eventContactPhone},</if>
            <if test="activeState != null">#{activeState},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="requiredNumber != null">#{requiredNumber},</if>
            <if test="whetherNeedEgister != null">#{whetherNeedEgister},</if>
            <if test="earnedTimeCoin != null">#{earnedTimeCoin},</if>
            <if test="realEarnedTimeCoin != null">#{realEarnedTimeCoin},</if>
            <if test="registrationEndTime != null">#{registrationEndTime},</if>
            <if test="inviteParticipants != null">#{inviteParticipants},</if>
            <if test="activitySite != null">#{activitySite},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="img != null and img != '' ">#{img},</if>
            <if test="auditStatus != null and auditStatus != '' ">#{auditStatus},</if>
            <if test="longitude != null and longitude != '' ">#{longitude},</if>
            <if test="latitude != null and latitude != '' ">#{latitude},</if>
            <if test="address != null and address != '' ">#{address},</if>
        </trim>
    </insert>

    <update id="updateHomePublicWelfareActivities"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomePublicWelfareActivities">
        update t_home_public_welfare_activities
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="state != null">state = #{state},</if>
            <if test="exchangeState != null">exchange_state = #{exchangeState},</if>
            <if test="eventPromoter != null">event_promoter = #{eventPromoter},</if>
            <if test="eventContact != null">event_contact = #{eventContact},</if>
            <if test="eventContactPhone != null">event_contact_phone = #{eventContactPhone},</if>
            <if test="activeState != null">active_state = #{activeState},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="requiredNumber != null">required_number = #{requiredNumber},</if>
            <if test="whetherNeedEgister != null">whether_need_egister = #{whetherNeedEgister},</if>
            <if test="earnedTimeCoin != null">earned_time_coin = #{earnedTimeCoin},</if>
            <if test="realEarnedTimeCoin != null">real_earned_time_coin = #{realEarnedTimeCoin},</if>
            <if test="registrationEndTime != null">registration_end_time = #{registrationEndTime},</if>
            <if test="inviteParticipants != null">invite_participants = #{inviteParticipants},</if>
            <if test="activitySite != null">activity_site = #{activitySite},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="img != null and img != '' ">img = #{img},</if>
            <if test="auditStatus != null and auditStatus != '' ">audit_status = #{auditStatus},</if>
            <if test="longitude != null and longitude != '' ">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != '' ">latitude = #{latitude},</if>
            <if test="address != null and address != '' ">address = #{address},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomePublicWelfareActivitiesById" parameterType="Long">
        delete from t_home_public_welfare_activities where id = #{id}
    </delete>

    <delete id="deleteHomePublicWelfareActivitiesByIds" parameterType="String">
        update t_home_public_welfare_activities set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getInvolvedActivitiesList"
            parameterType="com.ruoyi.homecare.volunteers.domain.vo.HomeAppPublicWelfareActivitiesVo"
            resultType="com.ruoyi.homecare.volunteers.domain.vo.HomeAppPublicWelfareActivitiesVo">
        SELECT
        aa.id,
        aa.name,
        aa.event_promoter,
        aa.event_contact,
        ee.name as eventContactName,
        aa.event_contact_phone,
        aa.active_state,
        aa.begin_time,
        aa.end_time,
        aa.state,
        aa.latitude,
        aa.longitude,
        aa.exchange_state,
        aa.required_number,
        aa.whether_need_egister,
        aa.earned_time_coin,
        aa.real_earned_time_coin,
        aa.registration_end_time,
        aa.invite_participants,
        aa.activity_site,
        aa.create_time,
        aa.address,
        aa.create_by,
        aa.update_time,
        aa.update_by,
        aa.del_flag,
        aa.img,
        aa.audit_status,
        ifnull(cc.num,0) as num,
        aa.remark
        FROM
        t_home_public_welfare_activities aa
        LEFT JOIN ( SELECT b.activity_id FROM t_home_public_welfare_activities a LEFT JOIN t_home_activity_apply_index b
        ON a.id = b.activity_id WHERE b.del_flag = '0' and b.volunteer_id = #{volunteerId} ) bb
        on aa.id = bb.activity_id
        LEFT JOIN ( SELECT count( id ) num, activity_id as aId FROM t_home_activity_apply_index WHERE del_flag = '0'
        GROUP BY aId ) AS cc ON aa.id = cc.aId
        left join t_home_volunteer_base_info as ee on ee.id = aa.event_contact
        <where>
            aa.del_flag = '0'
            <choose>
                <when test="selectFlag != null and selectFlag != ''">
                    and activity_id is not null
                </when>
                <otherwise>
                    and aa.audit_status = '3' and activity_id is null
                </otherwise>
            </choose>
            <if test="name != null and name != '' ">
                and aa.name like concat('%',#{name},'%')
            </if>
        </where>
        order by aa.active_state , aa.begin_time desc
    </select>
    <select id="getAppActivityInfo" parameterType="Long"
            resultType="com.ruoyi.homecare.volunteers.domain.vo.HomeAppPublicWelfareActivitiesVo">
        SELECT
        aa.id,
        aa.name,
        aa.event_promoter,
        dd.name as eventPromoterName,
        aa.event_contact,
        ee.name as eventContactName,
        aa.event_contact_phone as event_contact_phone,
        aa.active_state,
        aa.begin_time,
        aa.end_time,
        aa.state,
        aa.latitude,
        aa.longitude,
        aa.exchange_state,
        aa.required_number,
        aa.whether_need_egister,
        aa.earned_time_coin,
        aa.real_earned_time_coin,
        aa.registration_end_time,
        aa.invite_participants,
        aa.activity_site,
        aa.create_time,
        aa.create_by,
        aa.update_time,
        aa.update_by,
        aa.del_flag,
        aa.remark,
        aa.img,
        aa.address,
        aa.audit_status,
        ifnull(cc.num,0) as num,
        bb.indexId as indexId,
        bb.sign_in as signInState
        FROM
        t_home_public_welfare_activities aa
        LEFT JOIN (
        SELECT
        b.activity_id,
        b.sign_in,
        b.id as indexId
        FROM
        t_home_public_welfare_activities a
        LEFT JOIN t_home_activity_apply_index b ON a.id = b.activity_id
        WHERE
        b.del_flag = '0'
        AND b.volunteer_id = #{volunteerId}
        ) bb ON aa.id = bb.activity_id
        LEFT JOIN ( SELECT count( id ) num, activity_id as aId FROM t_home_activity_apply_index WHERE del_flag = '0'
        GROUP BY aId ) AS cc ON aa.id = cc.aId
        left join t_home_volunteer_base_info as dd on dd.id = aa.event_promoter
        left join t_home_volunteer_base_info as ee on ee.id = aa.event_contact
        where aa.id = #{id}
    </select>

    <select id="getMyIssuedList"
            parameterType="com.ruoyi.homecare.volunteers.domain.vo.HomeAppPublicWelfareActivitiesVo"
            resultType="com.ruoyi.homecare.volunteers.domain.vo.HomeAppPublicWelfareActivitiesVo">
        SELECT
        a.id,
        a.name,
        a.event_promoter,
        c.name as eventPromoterName,
        a.event_contact,
        d.name as eventContactName,
        a.event_contact_phone,
        a.active_state,
        a.begin_time,
        a.end_time,
        a.state,
        a.exchange_state,
        a.required_number,
        a.whether_need_egister,
        a.earned_time_coin,
        a.real_earned_time_coin,
        a.registration_end_time,
        a.invite_participants,
        a.activity_site,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.img,
        a.address,
        a.audit_status,
        a.latitude,
        a.longitude,
        a.remark,
        IFNULL(b.num,0) as num,
        IFNULL(b.signIn,0) as signIn
        FROM
        t_home_public_welfare_activities AS a
        LEFT JOIN (select count(id) as num ,sum(sign_in) as signIn,activity_id from t_home_activity_apply_index where
        del_flag = '0' GROUP BY activity_id) AS b ON a.id = b.activity_id
        left join t_home_volunteer_base_info as c on c.id = a.event_promoter
        left join t_home_volunteer_base_info as d on d.id = a.event_contact
        <where>
            a.del_flag = '0' AND a.event_promoter = #{volunteerId}
            <if test="name != null and name != '' ">
                and a.name like concat('%',#{name},'%')
            </if>
        </where>
        order by a.create_time desc
    </select>


</mapper>
