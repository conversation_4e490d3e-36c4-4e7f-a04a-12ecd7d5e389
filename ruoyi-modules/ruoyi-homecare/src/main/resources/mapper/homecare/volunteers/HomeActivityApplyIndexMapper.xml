<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeActivityApplyIndexMapper">

    <resultMap type="com.ruoyi.homecare.volunteers.domain.HomeActivityApplyIndex" id="HomeActivityApplyIndexResult">
        <result property="id" column="id"/>
        <result property="activityId" column="activity_id"/>
        <result property="volunteerId" column="volunteer_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="signIn" column="sign_in"/>
        <result property="signInImg" column="sign_in_img"/>
    </resultMap>

    <sql id="selectHomeActivityApplyIndexVo">
        select id, activity_id, volunteer_id,sign_in,sign_in_img, create_time,create_by, update_time, update_by,
        del_flag, remark from t_home_activity_apply_index
    </sql>

    <select id="selectHomeActivityApplyIndexList"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomeActivityApplyIndex"
            resultMap="HomeActivityApplyIndexResult">
        <include refid="selectHomeActivityApplyIndexVo"/>
        <where>
            del_flag = '0'
            <if test="activityId != null ">and activity_id = #{activityId}</if>
            <if test="volunteerId != null ">and volunteer_id = #{volunteerId}</if>
            <if test="signIn != null ">and sign_in = #{signIn}</if>
        </where>
    </select>

    <select id="selectHomeActivityApplyIndexById" parameterType="Long" resultMap="HomeActivityApplyIndexResult">
        <include refid="selectHomeActivityApplyIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeActivityApplyIndex"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomeActivityApplyIndex" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_home_activity_apply_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="volunteerId != null">volunteer_id,</if>
            <if test="signIn != null and singIn != ''">sign_in,</if>
            <if test="signInImg != null">sign_in_img,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="volunteerId != null">#{volunteerId},</if>
            <if test="signIn != null and singIn != ''">#{signIn},</if>
            <if test="signInImg != null">#{signInImg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeActivityApplyIndex"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomeActivityApplyIndex">
        update t_home_activity_apply_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="volunteerId != null">volunteer_id = #{volunteerId},</if>
            <if test="signIn != null">sign_in = #{signIn},</if>
            <if test="signInImg != null">sign_in_img = #{signInImg},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeActivityApplyIndexById" parameterType="Long">
        update t_home_activity_apply_index set del_flag= '1' where id = #{id}
    </delete>

    <delete id="deleteHomeActivityApplyIndexByIds" parameterType="String">
        update t_home_activity_apply_index set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getRegisteredList" resultType="cn.hutool.json.JSONObject" parameterType="String">
        SELECT
        a.id,
        a.activity_id as activityId,
        a.volunteer_id as volunteerId,
        a.remark,
        b.name,
        b.age,
        b.sex
        FROM
        t_home_activity_apply_index AS a
        LEFT JOIN t_home_volunteer_base_info AS b ON a.volunteer_id = b.id
        <where>
            a.del_flag = '0' and a.activity_id = #{activityId}
            <if test="name != '' and name != null">
                and b.name like concat('%',#{name},'%')
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="getNumberByActivityId" resultType="Long" parameterType="Long">
        select count(id) as num from t_home_activity_apply_index where activity_id = #{activityId} and del_flag = '0'
    </select>

</mapper>
