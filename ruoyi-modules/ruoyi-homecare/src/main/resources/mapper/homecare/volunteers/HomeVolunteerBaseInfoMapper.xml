<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeVolunteerBaseInfoMapper">

    <resultMap type="com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo" id="HomeVolunteerBaseInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCardNum" column="id_card_num"/>
        <result property="phone" column="phone"/>
        <result property="nation" column="nation"/>
        <result property="age" column="age"/>
        <result property="sex" column="sex"/>
        <result property="occupation" column="occupation"/>
        <result property="password" column="password"/>
        <result property="address" column="address"/>
        <result property="serviceArea" column="service_area"/>
        <result property="serviceSkills" column="service_skills"/>
        <result property="dateBirth" column="date_birth"/>
        <result property="img" column="img"/>
        <result property="cumulativeServicesNumber" column="cumulative_services_number"/>
        <result property="cumulativeServicesHours" column="cumulative_services_hours"/>
        <result property="cumulativeTimeCoin" column="cumulative_time_coin"/>
        <result property="currentTimeCoin" column="current_time_coin"/>
        <result property="favorableRating" column="favorable_rating"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="street" column="street"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="serviceTime" column="service_time"/>
        <result property="status" column="status"/>
        <result property="education_level" column="educationLevel"/>
    </resultMap>

    <sql id="selectHomeVolunteerBaseInfoVo">
        select id, name, id_card_num, phone, status, education_level, political_status, service_time, street, nation,
        sys_user_id, age, sex, occupation, password, address, service_area, service_skills, date_birth, img,
        cumulative_services_number, cumulative_services_hours, cumulative_time_coin, current_time_coin,
        favorable_rating, create_time, create_by, update_time, update_by, del_flag, remark from
        t_home_volunteer_base_info
    </sql>

    <select id="selectHomeVolunteerBaseInfoList"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo"
            resultMap="HomeVolunteerBaseInfoResult">
        <include refid="selectHomeVolunteerBaseInfoVo"/>
        <where>
            del_flag = '0' and status = 3
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="idCardNum != null  and idCardNum != ''">and id_card_num like concat('%', #{idCardNum}, '%')</if>
            <if test="phone != null  and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="nation != null  and nation != ''">and nation = #{nation}</if>
            <if test="age != null  and age != ''">and age = #{age}</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="occupation != null  and occupation != ''">and occupation = #{occupation}</if>
            <if test="password != null  and password != ''">and password = #{password}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="serviceArea != null  and serviceArea != ''">and service_area = #{serviceArea}</if>
            <if test="serviceSkills != null  and serviceSkills != ''">and service_skills = #{serviceSkills}</if>
            <if test="dateBirth != null ">and date_birth = #{dateBirth}</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="cumulativeServicesNumber != null ">and cumulative_services_number = #{cumulativeServicesNumber}
            </if>
            <if test="cumulativeServicesHours != null ">and cumulative_services_hours = #{cumulativeServicesHours}</if>
            <if test="cumulativeTimeCoin != null ">and cumulative_time_coin = #{cumulativeTimeCoin}</if>
            <if test="currentTimeCoin != null ">and current_time_coin = #{currentTimeCoin}</if>
            <if test="street != null ">and street = #{street}</if>
            <if test="favorableRating != null  and favorableRating != ''">and favorable_rating = #{favorableRating}</if>
            <if test="sysUserId != null  and sysUserId != ''">and sys_user_id = #{sysUserId}</if>
            <if test="politicalStatus != null  and politicalStatus != ''">and political_status = #{politicalStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="educationLevel != null  and educationLevel != ''">and education_level = #{educationLevel}</if>
        </where>
        order by create_time desc
    </select>

    <select id="listUnapproved" parameterType="com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo"
            resultMap="HomeVolunteerBaseInfoResult">
        <include refid="selectHomeVolunteerBaseInfoVo"/>
        <where>
            del_flag = '0'
            <choose>
                <when test=" status != null and status != ''  ">
                    and status = #{status}
                </when>
                <otherwise>
                    and status != 3
                </otherwise>
            </choose>

            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="idCardNum != null  and idCardNum != ''">and id_card_num like concat('%', #{idCardNum}, '%')</if>
            <if test="phone != null  and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="nation != null  and nation != ''">and nation = #{nation}</if>
            <if test="age != null  and age != ''">and age = #{age}</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="occupation != null  and occupation != ''">and occupation = #{occupation}</if>
            <if test="password != null  and password != ''">and password = #{password}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="serviceArea != null  and serviceArea != ''">and service_area = #{serviceArea}</if>
            <if test="serviceSkills != null  and serviceSkills != ''">and service_skills = #{serviceSkills}</if>
            <if test="dateBirth != null ">and date_birth = #{dateBirth}</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="cumulativeServicesNumber != null ">and cumulative_services_number = #{cumulativeServicesNumber}
            </if>
            <if test="cumulativeServicesHours != null ">and cumulative_services_hours = #{cumulativeServicesHours}</if>
            <if test="cumulativeTimeCoin != null ">and cumulative_time_coin = #{cumulativeTimeCoin}</if>
            <if test="currentTimeCoin != null ">and current_time_coin = #{currentTimeCoin}</if>
            <if test="street != null ">and street = #{street}</if>
            <if test="favorableRating != null  and favorableRating != ''">and favorable_rating = #{favorableRating}</if>
            <if test="sysUserId != null  and sysUserId != ''">and sys_user_id = #{sysUserId}</if>
            <if test="politicalStatus != null  and politicalStatus != ''">and political_status = #{politicalStatus}</if>
            <if test="educationLevel != null  and educationLevel != ''">and education_level = #{educationLevel}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHomeVolunteerBaseInfoById" parameterType="Long" resultMap="HomeVolunteerBaseInfoResult">
        <include refid="selectHomeVolunteerBaseInfoVo"/>
        where id = #{id}
    </select>

    <select id="getInfoByPhonePassWord" parameterType="String" resultMap="HomeVolunteerBaseInfoResult">
        <include refid="selectHomeVolunteerBaseInfoVo"/>
        where phone = #{phone} and password = #{password} order by create_time desc limit 1
    </select>

    <select id="checkVolunteerPhoneUnique" parameterType="String" resultMap="HomeVolunteerBaseInfoResult">
        <include refid="selectHomeVolunteerBaseInfoVo"/>
        where phone = #{phone} and status != 2 and del_flag = '0' order by create_time desc limit 1
    </select>

    <select id="getHomeVolunteerBaseInfoBySysUserId" parameterType="Long" resultMap="HomeVolunteerBaseInfoResult">
        <include refid="selectHomeVolunteerBaseInfoVo"/>
        where sys_user_id = #{sysUserId} and del_flag='0'
    </select>

    <insert id="insertHomeVolunteerBaseInfo" parameterType="com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_volunteer_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="idCardNum != null">id_card_num,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="phone != null">phone,</if>
            <if test="nation != null">nation,</if>
            <if test="age != null">age,</if>
            <if test="sex != null">sex,</if>
            <if test="occupation != null">occupation,</if>
            <if test="password != null">password,</if>
            <if test="address != null">address,</if>
            <if test="serviceArea != null">service_area,</if>
            <if test="serviceSkills != null">service_skills,</if>
            <if test="dateBirth != null">date_birth,</if>
            <if test="img != null">img,</if>
            <if test="cumulativeServicesNumber != null">cumulative_services_number,</if>
            <if test="cumulativeServicesHours != null">cumulative_services_hours,</if>
            <if test="cumulativeTimeCoin != null">cumulative_time_coin,</if>
            <if test="currentTimeCoin != null">current_time_coin,</if>
            <if test="favorableRating != null">favorable_rating,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="politicalStatus != null">political_status,</if>
            <if test="street != null">street,</if>
            <if test="serviceTime != null">service_time,</if>
            <if test="status != null">status,</if>
            <if test="educationLevel != null">education_level,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="idCardNum != null">#{idCardNum},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="nation != null">#{nation},</if>
            <if test="age != null">#{age},</if>
            <if test="sex != null">#{sex},</if>
            <if test="occupation != null">#{occupation},</if>
            <if test="password != null">#{password},</if>
            <if test="address != null">#{address},</if>
            <if test="serviceArea != null">#{serviceArea},</if>
            <if test="serviceSkills != null">#{serviceSkills},</if>
            <if test="dateBirth != null">#{dateBirth},</if>
            <if test="img != null">#{img},</if>
            <if test="cumulativeServicesNumber != null">#{cumulativeServicesNumber},</if>
            <if test="cumulativeServicesHours != null">#{cumulativeServicesHours},</if>
            <if test="cumulativeTimeCoin != null">#{cumulativeTimeCoin},</if>
            <if test="currentTimeCoin != null">#{currentTimeCoin},</if>
            <if test="favorableRating != null">#{favorableRating},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="politicalStatus != null">#{politicalStatus},</if>
            <if test="street != null">#{street},</if>
            <if test="serviceTime != null">#{serviceTime},</if>
            <if test="status != null">#{status},</if>
            <if test="educationLevel != null">#{educationLevel},</if>
        </trim>
    </insert>

    <update id="updateHomeVolunteerBaseInfo" parameterType="com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo">
        update t_home_volunteer_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="idCardNum != null">id_card_num = #{idCardNum},</if>
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="age != null">age = #{age},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="occupation != null">occupation = #{occupation},</if>
            <if test="password != null">password = #{password},</if>
            <if test="address != null">address = #{address},</if>
            <if test="serviceArea != null">service_area = #{serviceArea},</if>
            <if test="serviceSkills != null">service_skills = #{serviceSkills},</if>
            <if test="dateBirth != null">date_birth = #{dateBirth},</if>
            <if test="img != null">img = #{img},</if>
            <if test="cumulativeServicesNumber != null">cumulative_services_number = #{cumulativeServicesNumber},</if>
            <if test="cumulativeServicesHours != null">cumulative_services_hours = #{cumulativeServicesHours},</if>
            <if test="cumulativeTimeCoin != null">cumulative_time_coin = #{cumulativeTimeCoin},</if>
            <if test="currentTimeCoin != null">current_time_coin = #{currentTimeCoin},</if>
            <if test="favorableRating != null">favorable_rating = #{favorableRating},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
            <if test="street != null">street = #{street},</if>
            <if test="serviceTime != null">service_time = #{serviceTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="educationLevel != null">education_level = #{educationLevel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeVolunteerBaseInfoById" parameterType="Long">
        delete from t_home_volunteer_base_info where id = #{id}
    </delete>

    <delete id="deleteHomeVolunteerBaseInfoByIds" parameterType="String">
        update t_home_volunteer_base_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getVolunteerApplyList" parameterType="com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo"
            resultMap="HomeVolunteerBaseInfoResult">
        select
        main.name,
        main.nation,
        main.age,
        main.id,
        main.address,
        main.phone,
        main.sex,
        main.service_skills,
        main.occupation,
        main.create_time,
        main.flag,
        main.indexId,
        main.del_flag
        from (
        SELECT
        a.name,
        a.nation,
        a.id,
        a.age,
        a.address,
        a.phone,
        a.sex,
        a.service_skills,
        a.occupation,
        a.create_time,
        IFNULL( b.flag, 0 ) AS flag,
        IFNULL( b.id, '' ) AS indexId,
        a.del_flag
        FROM
        t_home_volunteer_base_info AS a
        LEFT JOIN ( SELECT id, activity_id, volunteer_id, '1' AS flag FROM t_home_activity_apply_index WHERE del_flag =
        '0' AND activity_id = #{activityId} ) AS b ON a.id = b.volunteer_id where a.status = '3') as main
        <where>
            main.del_flag = '0'
            <if test="flag != '' and flag != null ">
                and main.flag = #{flag}
            </if>
            <if test="name != '' and name != null">
                and main.name like concat('%',#{name},'%')
            </if>
        </where>
        order by main.create_time desc
    </select>

    <select id="getVolunteerList" resultType="cn.hutool.json.JSONObject">
        select id as value , name as label,current_time_coin as currentTimeCoin,phone as
        phone,CONCAT(name,'(',phone,')') as selectLabel from t_home_volunteer_base_info where del_flag = '0' and status
        = '3'
        <if test="name != null and name != '' ">
            and name like concat('%',#{name},'%')
        </if>
        order by create_time desc
    </select>

    <select id="getVolunteerPlaceOrderList" resultType="cn.hutool.json.JSONObject"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo">
        select id,name from t_home_volunteer_base_info
        <where>
            del_flag = '0' and status = '3'
            <if test="serviceArea != null and serviceArea != ''">
                and service_area = #{serviceArea}
            </if>
            <if test="serviceSkills != null and serviceArea != ''">
                and FIND_IN_SET(#{serviceSkills},service_skills)
            </if>
            <if test="id != null">
                and id != #{id}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="getPointRankingList" resultType="cn.hutool.json.JSONObject"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomePublicWelfareActivities">
        SELECT
        main.*
        FROM
        (
        SELECT
        @b := @b + 1 AS no,
        a.id,
        a.img,
        a.name,
        a.phone,
        ifnull(a.service_time,'') as serviceTime,
        ifnull(a.cumulative_services_hours,'') as cumulativeServicesHours,
        ifnull(a.cumulative_time_coin,'') as cumulativeTimeCoin,
        ifnull(a.current_time_coin,'') as currentTimeCoin
        FROM
        t_home_volunteer_base_info AS a,
        ( SELECT @b := 0 ) AS b
        WHERE
        a.del_flag = '0' and a.status = '3'
        ORDER BY
        a.cumulative_time_coin DESC

        ) AS main
        <where>
            <if test="name != null and name != ''">
                main.name like concat('%',#{name},'%')
            </if>
        </where>

    </select>

    <select id="getPublicWelfareRankingList" resultType="cn.hutool.json.JSONObject"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomePublicWelfareActivities">
        SELECT
        main.*
        FROM
        (
        SELECT
        @b := @b + 1 AS no,
        a.id,
        a.name,
        ifnull(a.img,'') as img,
        a.phone,
        ifnull(a.service_time,'') as serviceTime,
        ifnull(a.cumulative_services_hours,'') as cumulativeServicesHours,
        ifnull(a.cumulative_time_coin,'') as cumulativeTimeCoin,
        ifnull(a.current_time_coin,'') as currentTimeCoin
        FROM
        t_home_volunteer_base_info AS a,
        ( SELECT @b := 0 ) AS b
        WHERE
        a.del_flag = '0' and a.status = '3'
        ORDER BY
        a.cumulative_services_hours DESC
        ) AS main
    </select>

    <select id="getCurrentTimeCoinPointRankingInfo" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        SELECT
        main.*
        FROM
        (
        SELECT
        @b := @b + 1 AS no,
        a.id,
        a.name,
        a.phone,
        ifnull(a.service_time,'') as serviceTime,
        ifnull(a.cumulative_services_hours,'') as cumulativeServicesHours,
        ifnull(a.cumulative_time_coin,'') as cumulativeTimeCoin,
        ifnull(a.current_time_coin,'') as currentTimeCoin
        FROM
        t_home_volunteer_base_info AS a,
        ( SELECT @b := 0 ) AS b
        WHERE
        a.del_flag = '0' and a.status = '3'
        ORDER BY
        a.current_time_coin DESC
        )
        AS main
        where main.id = #{id}
    </select>

    <select id="getAppAssociationRankingList" resultType="cn.hutool.json.JSONObject"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo">
        SELECT
        @b := @b + 1 AS no,
        a.id,
        a.name,
        a.time AS cumulativeServicesHours,
        a.img,
        a.remark
        FROM
        (
        SELECT
        IFNULL( c.time, 0 ) AS time,
        a.*
        FROM
        t_home_association_info AS a
        LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
        t_home_public_welfare_activities GROUP BY event_promoter ) AS c ON a.first_contacts = c.event_promoter
        WHERE
        a.del_flag = '0'
        AND a.STATUS > 2
        ORDER BY
        time DESC
        ) AS a,
        ( SELECT @b := 0 ) AS b
    </select>

    <select id="getAppAssociationRankingInfo" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        SELECT
        main.*
        FROM
        (
        SELECT
        @b := @b + 1 AS no,
        a.id,
        a.name,
        a.time AS time,
        a.img,
        a.remark
        FROM
        (
        SELECT
        IFNULL( c.time, 0 ) AS time,
        a.*
        FROM
        t_home_association_info AS a
        LEFT JOIN ( SELECT sum( TIMESTAMPDIFF( HOUR, begin_time, end_time )) time, event_promoter FROM
        t_home_public_welfare_activities GROUP BY event_promoter ) AS c ON a.first_contacts = c.event_promoter
        WHERE
        a.del_flag = '0'
        AND a.STATUS > 2
        ORDER BY
        time DESC
        ) AS a,
        ( SELECT @b := 0 ) AS b
        ) AS main
        WHERE
        main.id = #{id}
    </select>


    <select id="getAppMyPublicWelfareRankingInfo" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        SELECT
        main.*
        FROM
        (
        SELECT
        @b := @b + 1 AS no,
        a.id,
        a.name,
        a.phone,
        ifnull(a.img,'') as img,
        ifnull(a.service_time,'') as serviceTime,
        ifnull(a.cumulative_services_hours,'') as cumulativeServicesHours,
        ifnull(a.cumulative_time_coin,'') as cumulativeTimeCoin,
        ifnull(a.current_time_coin,'') as currentTimeCoin
        FROM
        t_home_volunteer_base_info AS a,
        ( SELECT @b := 0 ) AS b
        WHERE
        a.del_flag = '0' and a.status = '3'
        ORDER BY
        a.cumulative_services_hours DESC
        ) AS main where main.id = #{id}
    </select>


</mapper>
