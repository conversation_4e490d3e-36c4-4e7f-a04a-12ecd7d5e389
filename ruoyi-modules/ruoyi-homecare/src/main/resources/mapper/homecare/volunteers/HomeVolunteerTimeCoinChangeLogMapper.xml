<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeVolunteerTimeCoinChangeLogMapper">

    <resultMap type="HomeVolunteerTimeCoinChangeLog" id="HomeVolunteerTimeCoinChangeLogResult">
        <result property="id" column="id"/>
        <result property="volunteerId" column="volunteer_id"/>
        <result property="type" column="type"/>
        <result property="changeType" column="change_type"/>
        <result property="pointRanking" column="point_ranking"/>
        <result property="lastAmount" column="last_amount"/>
        <result property="changedAmount" column="changed_amount"/>
        <result property="amount" column="amount"/>
        <result property="changedDescribe" column="changed_describe"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="originalNumber" column="original_number"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="changedRanking" column="changed_ranking"/>
    </resultMap>

    <sql id="selectHomeVolunteerTimeCoinChangeLogVo">
        SELECT
        a.id,
        a.volunteer_id,
        a.type,
        a.change_type,
        a.point_ranking,
        a.original_number,
        a.last_amount,
        a.changed_describe,
        a.changed_amount,
        a.amount,
        a.changed_ranking,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        b.name,
        b.phone,
        a.remark
        FROM
        t_home_volunteer_time_coin_change_log as a
        left join t_home_volunteer_base_info as b on a.volunteer_id = b.id
    </sql>

    <select id="selectHomeVolunteerTimeCoinChangeLogList" parameterType="HomeVolunteerTimeCoinChangeLog"
            resultMap="HomeVolunteerTimeCoinChangeLogResult">
        <include refid="selectHomeVolunteerTimeCoinChangeLogVo"/>
        <where>
            a.del_flag = '0'
            <if test="volunteerId != null ">and a.volunteer_id = #{volunteerId}</if>
            <if test="type != null  and type != ''">and a.type = #{type}</if>
            <if test="changeType != null  and changeType != ''">and a.change_type = #{changeType}</if>
            <if test="pointRanking != null ">and a.point_ranking = #{pointRanking}</if>
            <if test="lastAmount != null ">and a.last_amount = #{lastAmount}</if>
            <if test="changedAmount != null ">and a.changed_amount = #{changedAmount}</if>
            <if test="amount != null ">and a.amount = #{amount}</if>
            <if test="originalNumber != null ">and a.original_number = #{originalNumber}</if>
            <if test="changedDescribe != null  and changedDescribe != ''">and a.changed_describe like
                concat('%',#{changedDescribe},'%')
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and a.create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectHomeVolunteerTimeCoinChangeLogById" parameterType="Long"
            resultMap="HomeVolunteerTimeCoinChangeLogResult">
        <include refid="selectHomeVolunteerTimeCoinChangeLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeVolunteerTimeCoinChangeLog" parameterType="HomeVolunteerTimeCoinChangeLog"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_volunteer_time_coin_change_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="volunteerId != null">volunteer_id,</if>
            <if test="type != null">type,</if>
            <if test="changeType != null">change_type,</if>
            <if test="pointRanking != null">point_ranking,</if>
            <if test="lastAmount != null">last_amount,</if>
            <if test="changedAmount != null">changed_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="changedDescribe != null">changed_describe,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="originalNumber != null">original_number,</if>
            <if test="changedRanking != null">changed_ranking,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="volunteerId != null">#{volunteerId},</if>
            <if test="type != null">#{type},</if>
            <if test="changeType != null">#{changeType},</if>
            <if test="pointRanking != null">#{pointRanking},</if>
            <if test="lastAmount != null">#{lastAmount},</if>
            <if test="changedAmount != null">#{changedAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="changedDescribe != null">#{changedDescribe},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="originalNumber != null">#{originalNumber},</if>
            <if test="changedRanking != null">#{changedRanking},</if>
        </trim>
    </insert>

    <update id="updateHomeVolunteerTimeCoinChangeLog" parameterType="HomeVolunteerTimeCoinChangeLog">
        update t_home_volunteer_time_coin_change_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="volunteerId != null">volunteer_id = #{volunteerId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="changeType != null">change_type = #{changeType},</if>
            <if test="pointRanking != null">point_ranking = #{pointRanking},</if>
            <if test="lastAmount != null">last_amount = #{lastAmount},</if>
            <if test="changedAmount != null">changed_amount = #{changedAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="changedDescribe != null">changed_describe = #{changedDescribe},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="originalNumber != null">original_number = #{originalNumber},</if>
            <if test="changedRanking != null">changed_ranking = #{changedRanking},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeVolunteerTimeCoinChangeLogById" parameterType="Long">
        delete from t_home_volunteer_time_coin_change_log where id = #{id}
    </delete>

    <delete id="deleteHomeVolunteerTimeCoinChangeLogByIds" parameterType="String">
        delete from t_home_volunteer_time_coin_change_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
