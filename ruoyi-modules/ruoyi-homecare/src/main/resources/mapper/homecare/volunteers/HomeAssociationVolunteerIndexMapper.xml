<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeAssociationVolunteerIndexMapper">

    <resultMap type="HomeAssociationVolunteerIndex" id="HomeAssociationVolunteerIndexResult">
        <result property="id" column="id"/>
        <result property="associationId" column="association_id"/>
        <result property="volunteerId" column="volunteer_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeAssociationVolunteerIndexVo">
        select id, association_id, volunteer_id, status, create_time, create_by, update_time, update_by, del_flag,
        remark from t_home_association_volunteer_index
    </sql>

    <select id="selectHomeAssociationVolunteerIndexList" parameterType="HomeAssociationVolunteerIndex"
            resultMap="HomeAssociationVolunteerIndexResult">
        <include refid="selectHomeAssociationVolunteerIndexVo"/>
        <where>
            <if test="associationId != null ">and association_id = #{associationId}</if>
            <if test="volunteerId != null ">and volunteer_id = #{volunteerId}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
    </select>

    <select id="selectHomeAssociationVolunteerIndexByVolunteerId" parameterType="Long"
            resultMap="HomeAssociationVolunteerIndexResult">
        <include refid="selectHomeAssociationVolunteerIndexVo"/>
        where volunteer_id = #{volunteerId} and del_flag = '0' and status in('1','3')
    </select>

    <select id="getAssociationVolunteerIndexByVolunteerId" parameterType="Long"
            resultMap="HomeAssociationVolunteerIndexResult">
        <include refid="selectHomeAssociationVolunteerIndexVo"/>
        where volunteer_id = #{volunteerId} and del_flag = '0' and status = '3'
    </select>

    <select id="selectHomeAssociationVolunteerIndexById" parameterType="Long"
            resultMap="HomeAssociationVolunteerIndexResult">
        <include refid="selectHomeAssociationVolunteerIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeAssociationVolunteerIndex" parameterType="HomeAssociationVolunteerIndex"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_association_volunteer_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="associationId != null">association_id,</if>
            <if test="volunteerId != null">volunteer_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="associationId != null">#{associationId},</if>
            <if test="volunteerId != null">#{volunteerId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeAssociationVolunteerIndex" parameterType="HomeAssociationVolunteerIndex">
        update t_home_association_volunteer_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="associationId != null">association_id = #{associationId},</if>
            <if test="volunteerId != null">volunteer_id = #{volunteerId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeAssociationVolunteerIndexById" parameterType="Long">
        delete from t_home_association_volunteer_index where id = #{id}
    </delete>

    <delete id="deleteHomeAssociationVolunteerIndexByIds" parameterType="String">
        delete from t_home_association_volunteer_index where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="dissolutionVolunteerByAssociationId" parameterType="Long">
        update t_home_association_volunteer_index set status = '5' where status in(1,3) and del_flag = '0' and
        association_id = #{associationId}
    </update>

</mapper>
