<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeServicePlaceOrderVolunteerInfoMapper">

    <resultMap type="HomeServicePlaceOrderVolunteerInfo" id="HomeServicePlaceOrderVolunteerInfoResult">
        <result property="id" column="id"/>
        <result property="serviceOrderId" column="service_order_id"/>
        <result property="state" column="state"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="beginImg" column="begin_img"/>
        <result property="img" column="img"/>
        <result property="endImg" column="end_img"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="evaluate" column="evaluate"/>
    </resultMap>

    <sql id="selectHomeServicePlaceOrderVolunteerInfoVo">
        select id, service_order_id, state,evaluate, begin_time, end_time,begin_img, img,end_img, create_time,
        create_by, update_time, update_by, del_flag, remark from t_home_service_place_order_volunteer_info
    </sql>

    <select id="selectHomeServicePlaceOrderVolunteerInfoList" parameterType="HomeServicePlaceOrderVolunteerInfo"
            resultMap="HomeServicePlaceOrderVolunteerInfoResult">
        <include refid="selectHomeServicePlaceOrderVolunteerInfoVo"/>
        <where>
            <if test="serviceOrderId != null ">and service_order_id = #{serviceOrderId}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
            <if test="beginTime != null ">and begin_time = #{beginTime}</if>
            <if test="endTime != null ">and end_time = #{endTime}</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="evaluate != null  and evaluate != ''">and evaluate = #{evaluate}</if>
        </where>
    </select>

    <select id="selectHomeServicePlaceOrderVolunteerInfoById" parameterType="Long"
            resultMap="HomeServicePlaceOrderVolunteerInfoResult">
        <include refid="selectHomeServicePlaceOrderVolunteerInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeServicePlaceOrderVolunteerInfo" parameterType="HomeServicePlaceOrderVolunteerInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_service_place_order_volunteer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceOrderId != null">service_order_id,</if>
            <if test="state != null">state,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="img != null">img,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="evaluate != null">evaluate,</if>
            <if test="beginImg != null">begin_img,</if>
            <if test="endImg != null">end_img,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceOrderId != null">#{serviceOrderId},</if>
            <if test="state != null">#{state},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="img != null">#{img},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="evaluate != null">#{evaluate},</if>
            <if test="beginImg != null">#{beginImg},</if>
            <if test="endImg != null">#{endImg},</if>
        </trim>
    </insert>

    <update id="updateHomeServicePlaceOrderVolunteerInfo" parameterType="HomeServicePlaceOrderVolunteerInfo">
        update t_home_service_place_order_volunteer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceOrderId != null">service_order_id = #{serviceOrderId},</if>
            <if test="state != null">state = #{state},</if>
            <if test="evaluate != null">evaluate = #{evaluate},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="img != null">img = #{img},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="beginImg != null">begin_img = #{beginImg},</if>
            <if test="endImg != null">end_img = #{endImg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeServicePlaceOrderVolunteerInfoById" parameterType="Long">
        delete from t_home_service_place_order_volunteer_info where id = #{id}
    </delete>

    <delete id="deleteHomeServicePlaceOrderVolunteerInfoByIds" parameterType="String">
        delete from t_home_service_place_order_volunteer_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
