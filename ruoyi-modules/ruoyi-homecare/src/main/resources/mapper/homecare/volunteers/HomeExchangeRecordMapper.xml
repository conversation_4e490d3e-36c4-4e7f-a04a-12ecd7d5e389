<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeExchangeRecordMapper">

    <resultMap type="com.ruoyi.homecare.volunteers.domain.HomeExchangeRecord" id="HomeExchangeRecordResult">
        <result property="id" column="id"/>
        <result property="volunteerId" column="volunteer_id"/>
        <result property="volunteerName" column="volunteer_name"/>
        <result property="giftId" column="gift_id"/>
        <result property="giftName" column="gift_name"/>
        <result property="timeCoin" column="time_coin"/>
        <result property="exchangeTime" column="exchange_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="surplusTimeCoin" column="surplus_time_coin"/>
    </resultMap>

    <sql id="selectHomeExchangeRecordVo">
        select id, volunteer_id, volunteer_name, gift_id, gift_name,surplus_time_coin,before_time_coin, time_coin,
        exchange_time, create_time, create_by, update_time, update_by, del_flag, remark from t_home_exchange_record
    </sql>

    <select id="selectHomeExchangeRecordList" parameterType="com.ruoyi.homecare.volunteers.domain.HomeExchangeRecord"
            resultMap="HomeExchangeRecordResult">
        <include refid="selectHomeExchangeRecordVo"/>
        <where>
            del_flag = '0'
            <if test="volunteerId != null ">and volunteer_id = #{volunteerId}</if>
            <if test="volunteerName != null  and volunteerName != ''">and volunteer_name like concat('%',
                #{volunteerName}, '%')
            </if>
            <if test="giftId != null ">and gift_id = #{giftId}</if>
            <if test="giftName != null  and giftName != ''">and gift_name like concat('%', #{giftName}, '%')</if>
            <if test="timeCoin != null  and timeCoin != ''">and time_coin = #{timeCoin}</if>
            <if test="params.beginExchangeTime != null and params.beginExchangeTime != '' and params.endExchangeTime != null and params.endExchangeTime != ''">
                and exchange_time between #{params.beginExchangeTime} and #{params.endExchangeTime}
            </if>
        </where>
        order by exchange_time desc
    </select>

    <select id="selectHomeExchangeRecordById" parameterType="Long" resultMap="HomeExchangeRecordResult">
        <include refid="selectHomeExchangeRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeExchangeRecord" parameterType="com.ruoyi.homecare.volunteers.domain.HomeExchangeRecord"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_exchange_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="volunteerId != null">volunteer_id,</if>
            <if test="volunteerName != null">volunteer_name,</if>
            <if test="giftId != null">gift_id,</if>
            <if test="giftName != null">gift_name,</if>
            <if test="timeCoin != null">time_coin,</if>
            <if test="exchangeTime != null">exchange_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="beforeTimeCoin != null">before_time_coin,</if>
            <if test="surplusTimeCoin != null">surplus_time_coin,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="volunteerId != null">#{volunteerId},</if>
            <if test="volunteerName != null">#{volunteerName},</if>
            <if test="giftId != null">#{giftId},</if>
            <if test="giftName != null">#{giftName},</if>
            <if test="timeCoin != null">#{timeCoin},</if>
            <if test="exchangeTime != null">#{exchangeTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="beforeTimeCoin != null">#{beforeTimeCoin},</if>
            <if test="surplusTimeCoin != null">#{surplusTimeCoin},</if>
        </trim>
    </insert>

    <update id="updateHomeExchangeRecord" parameterType="com.ruoyi.homecare.volunteers.domain.HomeExchangeRecord">
        update t_home_exchange_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="volunteerId != null">volunteer_id = #{volunteerId},</if>
            <if test="volunteerName != null">volunteer_name = #{volunteerName},</if>
            <if test="giftId != null">gift_id = #{giftId},</if>
            <if test="giftName != null">gift_name = #{giftName},</if>
            <if test="timeCoin != null">time_coin = #{timeCoin},</if>
            <if test="exchangeTime != null">exchange_time = #{exchangeTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="beforeTimeCoin != null">before_time_coin = #{beforeTimeCoin},</if>
            <if test="surplusTimeCoin != null">surplus_time_coin = #{surplusTimeCoin},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeExchangeRecordById" parameterType="Long">
        delete from t_home_exchange_record where id = #{id}
    </delete>

    <delete id="deleteHomeExchangeRecordByIds" parameterType="String">
        update t_home_exchange_record set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
