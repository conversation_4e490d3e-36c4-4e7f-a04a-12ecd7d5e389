<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeServicePlaceOrderVolunteerMapper">

    <resultMap type="HomeServicePlaceOrderVolunteer" id="HomeServicePlaceOrderVolunteerResult">
        <result property="id" column="id"/>
        <result property="serviceDate" column="service_date"/>
        <result property="serviceId" column="service_id"/>
        <result property="userId" column="user_id"/>
        <result property="volunteerId" column="volunteer_id"/>
        <result property="serviceAddress" column="service_address"/>
        <result property="phone" column="phone"/>
        <result property="flag" column="flag"/>
        <result property="communityId" column="community_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="orderNumber" column="order_number"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeServicePlaceOrderVolunteerVo">
        SELECT
        a.id,
        a.service_date,
        a.service_id,
        b.`name` as serviceName,
        a.user_id,
        u.name as userName,
        a.order_number,
        a.volunteer_id,
        a.service_address,
        a.phone,
        a.flag,
        a.community_id,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_home_service_place_order_volunteer as a
        left join t_home_elderly_people_info as u on a.user_id = u.id
        left join t_home_time_coin_settings as b on a.service_id = b.id
    </sql>

    <select id="selectHomeServicePlaceOrderVolunteerList" parameterType="HomeServicePlaceOrderVolunteer"
            resultMap="HomeServicePlaceOrderVolunteerResult">
        <include refid="selectHomeServicePlaceOrderVolunteerVo"/>
        <where>
            a.del_flag = '0'
            <if test="serviceDate != null ">and a.service_date = #{serviceDate}</if>
            <if test="serviceId != null ">and a.service_id = #{serviceId}</if>
            <if test="userId != null  and userId != ''">and a.user_id = #{userId}</if>
            <if test="volunteerId != null ">and a.volunteer_id = #{volunteerId}</if>
            <if test="serviceAddress != null  and serviceAddress != ''">and a.service_address = #{serviceAddress}</if>
            <if test="phone != null  and phone != ''">and a.phone = #{phone}</if>
            <if test="flag != null  and flag != ''">and a.flag = #{flag}</if>
            <if test="communityId != null ">and a.community_id = #{communityId}</if>
            <if test="orderNumber != null and orderNumber != ''">and a.order_number = #{orderNumber}</if>
        </where>
    </select>

    <select id="getGrabOrderList" parameterType="HomeServicePlaceOrderVolunteer"
            resultMap="HomeServicePlaceOrderVolunteerResult">
        <include refid="selectHomeServicePlaceOrderVolunteerVo"/>
        <where>
            a.del_flag = '0'
            <if test="serviceDate != null ">and a.service_date = #{serviceDate}</if>
            <if test="serviceId != null ">and a.service_id = #{serviceId}</if>
            <if test="userId != null  and userId != ''">and a.user_id != #{userId}</if>
            <if test="volunteerId != null ">and a.volunteer_id = #{volunteerId}</if>
            <if test="serviceAddress != null  and serviceAddress != ''">and a.service_address = #{serviceAddress}</if>
            <if test="phone != null  and phone != ''">and a.phone = #{phone}</if>
            <if test="flag != null  and flag != ''">and a.flag = #{flag}</if>
            <if test="communityId != null ">and a.community_id = #{communityId}</if>
            <if test="orderNumber != null and orderNumber != ''">and a.order_number = #{orderNumber}</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectHomeServicePlaceOrderVolunteerById" parameterType="Long"
            resultMap="HomeServicePlaceOrderVolunteerResult">
        <include refid="selectHomeServicePlaceOrderVolunteerVo"/>
        where a.id = #{id} for update
    </select>

    <insert id="insertHomeServicePlaceOrderVolunteer" parameterType="HomeServicePlaceOrderVolunteer"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_service_place_order_volunteer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceDate != null">service_date,</if>
            <if test="orderNumber != null">order_number,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="volunteerId != null">volunteer_id,</if>
            <if test="serviceAddress != null">service_address,</if>
            <if test="phone != null">phone,</if>
            <if test="flag != null">flag,</if>
            <if test="communityId != null">community_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceDate != null">#{serviceDate},</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="volunteerId != null">#{volunteerId},</if>
            <if test="serviceAddress != null">#{serviceAddress},</if>
            <if test="phone != null">#{phone},</if>
            <if test="flag != null">#{flag},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeServicePlaceOrderVolunteer" parameterType="HomeServicePlaceOrderVolunteer">
        update t_home_service_place_order_volunteer
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceDate != null">service_date = #{serviceDate},</if>
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="volunteerId != null">volunteer_id = #{volunteerId},</if>
            <if test="serviceAddress != null">service_address = #{serviceAddress},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeServicePlaceOrderVolunteerById" parameterType="Long">
        delete from t_home_service_place_order_volunteer where id = #{id}
    </delete>

    <delete id="deleteHomeServicePlaceOrderVolunteerByIds" parameterType="String">
        delete from t_home_service_place_order_volunteer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getNeedAccomplishList" resultType="HomeServicePlaceOrderVolunteer"
            parameterType="HomeServicePlaceOrderVolunteer">
        SELECT
        a.id,
        a.service_date,
        a.service_id,
        u.name AS userName,
        a.order_number,
        a.volunteer_id,
        d.name as serviceName,
        a.service_address,
        a.phone,
        a.flag,
        a.community_id,
        b.img as img,
        b.begin_time as beginTime,
        b.end_time as endTime,
        IFNULL(b.state,0) as state,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_home_service_place_order_volunteer AS a
        LEFT JOIN t_home_service_place_order_volunteer_info AS b ON a.id = b.service_order_id
        left join t_home_elderly_people_info as u on a.user_id = u.id
        left join t_home_time_coin_settings as d on d.id = a.service_id
        <where>
            a.del_flag = '0' and IFNULL( b.state, 0 ) != '2'
            <if test="userName != null and userName != '' ">
                and u.name like concat('%',#{userName},'%')
            </if>
            <if test="volunteerId != null and volunteerId != '' ">
                and a.volunteer_id = #{volunteerId}
            </if>
        </where>
        order by a.service_date asc
    </select>
    <select id="getAccomplishList" resultType="HomeServicePlaceOrderVolunteer"
            parameterType="HomeServicePlaceOrderVolunteer">
        SELECT
        a.id,
        a.service_date,
        a.service_id,
        u.name AS userName,
        a.order_number,
        a.volunteer_id,
        d.name as serviceName,
        a.service_address,
        a.phone,
        a.flag,
        a.community_id,
        b.img as img,
        b.begin_time as beginTime,
        b.end_time as endTime,
        IFNULL(b.state,0) as state,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        b.begin_time,
        b.end_time,
        a.remark
        FROM
        t_home_service_place_order_volunteer AS a
        LEFT JOIN t_home_service_place_order_volunteer_info AS b ON a.id = b.service_order_id
        left join t_home_elderly_people_info as u on a.user_id = u.id
        left join t_home_time_coin_settings as d on d.id = a.service_id
        <where>
            a.del_flag = '0'and IFNULL( b.state, 0 ) = '2'
            <if test="userName != null and userName != '' ">
                and u.name like concat('%',#{userName},'%')
            </if>
            <if test="volunteerId != null and volunteerId != '' ">
                and a.volunteer_id = #{volunteerId}
            </if>
        </where>
        order by b.end_time desc
    </select>

    <select id="getVolunteerServiceListByUserId" resultType="HomeServicePlaceOrderVolunteer"
            parameterType="HomeServicePlaceOrderVolunteer">
        SELECT
        a.id,
        a.service_date,
        a.service_id,
        a.order_number,
        a.volunteer_id,
        ifnull(c.name,'') as volunteerName,
        d.name as serviceName,
        a.service_address,
        a.user_id,
        a.phone,
        a.flag,
        a.community_id,
        b.img as img,
        b.begin_time as beginTime,
        b.end_time as endTime,
        IFNULL(b.state,0) as state,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        b.begin_time,
        b.end_time,
        a.remark
        FROM
        t_home_service_place_order_volunteer AS a
        LEFT JOIN t_home_service_place_order_volunteer_info AS b ON a.id = b.service_order_id
        left join t_home_time_coin_settings as d on d.id = a.service_id
        left join t_home_volunteer_base_info as c on a.volunteer_id = c.id
        <where>
            a.del_flag = '0'
            <if test="userId != null and userId != '' ">
                and a.user_id = #{userId}
            </if>
            <if test="volunteerName != null and volunteerName != '' ">
                and c.name like concat('%',#{volunteerName},'%')
            </if>
        </where>
        ORDER BY
        b.begin_time desc ,a.create_time desc
    </select>
    <select id="getNeedAccomplishInfo" parameterType="Long"
            resultType="com.ruoyi.homecare.volunteers.domain.vo.HomeServicePlaceOrderVolunteerVo">
        SELECT
        a.id,
        b.id as infoId,
        a.service_date,
        a.service_id,
        u.name AS userName,
        d.name as serviceName,
        a.order_number,
        a.volunteer_id,
        c.name as volunteerName,
        a.service_address,
        a.phone,
        a.flag,
        a.community_id,
        b.begin_img as beginImg,
        b.img as img,
        b.end_img as endImg,
        b.begin_time as beginTime,
        b.end_time as endTime,
        IFNULL(b.state,0) as state,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_home_service_place_order_volunteer AS a
        LEFT JOIN t_home_service_place_order_volunteer_info AS b ON a.id = b.service_order_id
        left join t_home_volunteer_base_info as c on c.id = a.volunteer_id
        left join t_home_elderly_people_info as u on a.user_id = u.id
        left join t_home_time_coin_settings as d on d.id = a.service_id
        <where>
            a.id = #{id}
        </where>
    </select>

</mapper>
