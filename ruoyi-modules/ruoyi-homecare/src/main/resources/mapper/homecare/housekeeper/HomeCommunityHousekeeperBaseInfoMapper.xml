<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.housekeeper.mapper.HomeCommunityHousekeeperBaseInfoMapper">

    <resultMap type="com.ruoyi.homecare.housekeeper.domain.HomeCommunityHousekeeperBaseInfo"
               id="HomeCommunityHousekeeperBaseInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="idCardNum" column="id_card_num"/>
        <result property="phone" column="phone"/>
        <result property="dateBirth" column="date_birth"/>
        <result property="age" column="age"/>
        <result property="nation" column="nation"/>
        <result property="educationLevel" column="education_level"/>
        <result property="courierStation" column="courier_station"/>
        <result property="position" column="position"/>
        <result property="serviceTime" column="service_time"/>
        <result property="serviceSkills" column="service_skills"/>
        <result property="address" column="address"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="img" column="img"/>
    </resultMap>

    <sql id="selectHomeCommunityHousekeeperBaseInfoVo">
        SELECT
        a.id,
        a.name,
        a.sex,
        a.id_card_num,
        a.phone,
        a.date_birth,
        a.age,
        a.nation,
        a.education_level,
        a.courier_station,
        a.position,
        a.service_time,
        a.service_skills,
        a.address,
        a.img,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        b.name as courierName,
        a.remark
        FROM
        t_home_community_housekeeper_base_info as a left join t_home_courier_station as b on a.courier_station = b.id
    </sql>

    <select id="selectHomeCommunityHousekeeperBaseInfoList"
            parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCommunityHousekeeperBaseInfo"
            resultMap="HomeCommunityHousekeeperBaseInfoResult">
        <include refid="selectHomeCommunityHousekeeperBaseInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="sex != null ">and a.sex = #{sex}</if>
            <if test="idCardNum != null  and idCardNum != ''">and a.id_card_num like concat('%', #{idCardNum}, '%')</if>
            <if test="phone != null  and phone != ''">and a.phone like concat('%', #{phone}, '%')</if>
            <if test="dateBirth != null ">and a.date_birth = #{dateBirth}</if>
            <if test="age != null  and age != ''">and a.age = #{age}</if>
            <if test="nation != null  and nation != ''">and a.nation = #{nation}</if>
            <if test="educationLevel != null  and educationLevel != ''">and a.education_level = #{educationLevel}</if>
            <if test="courierStation != null ">and a.courier_station = #{courierStation}</if>
            <if test="position != null  and position != ''">and a.position = #{position}</if>
            <if test="serviceTime != null ">and a.service_time = #{serviceTime}</if>
            <if test="serviceSkills != null  and serviceSkills != ''">and a.service_skills = #{serviceSkills}</if>
            <if test="address != null  and address != ''">and a.address = #{address}</if>
        </where>
    </select>

    <select id="selectHomeCommunityHousekeeperBaseInfoById" parameterType="Long"
            resultMap="HomeCommunityHousekeeperBaseInfoResult">
        <include refid="selectHomeCommunityHousekeeperBaseInfoVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertHomeCommunityHousekeeperBaseInfo"
            parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCommunityHousekeeperBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_community_housekeeper_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="sex != null">sex,</if>
            <if test="img != null">img,</if>
            <if test="idCardNum != null">id_card_num,</if>
            <if test="phone != null">phone,</if>
            <if test="dateBirth != null">date_birth,</if>
            <if test="age != null">age,</if>
            <if test="nation != null">nation,</if>
            <if test="educationLevel != null">education_level,</if>
            <if test="courierStation != null">courier_station,</if>
            <if test="position != null">position,</if>
            <if test="serviceTime != null">service_time,</if>
            <if test="serviceSkills != null">service_skills,</if>
            <if test="address != null">address,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="img != null">#{img},</if>
            <if test="idCardNum != null">#{idCardNum},</if>
            <if test="phone != null">#{phone},</if>
            <if test="dateBirth != null">#{dateBirth},</if>
            <if test="age != null">#{age},</if>
            <if test="nation != null">#{nation},</if>
            <if test="educationLevel != null">#{educationLevel},</if>
            <if test="courierStation != null">#{courierStation},</if>
            <if test="position != null">#{position},</if>
            <if test="serviceTime != null">#{serviceTime},</if>
            <if test="serviceSkills != null">#{serviceSkills},</if>
            <if test="address != null">#{address},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeCommunityHousekeeperBaseInfo"
            parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCommunityHousekeeperBaseInfo">
        update t_home_community_housekeeper_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="img != null">img = #{img},</if>
            <if test="idCardNum != null">id_card_num = #{idCardNum},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="dateBirth != null">date_birth = #{dateBirth},</if>
            <if test="age != null">age = #{age},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="educationLevel != null">education_level = #{educationLevel},</if>
            <if test="courierStation != null">courier_station = #{courierStation},</if>
            <if test="position != null">position = #{position},</if>
            <if test="serviceTime != null">service_time = #{serviceTime},</if>
            <if test="serviceSkills != null">service_skills = #{serviceSkills},</if>
            <if test="address != null">address = #{address},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeCommunityHousekeeperBaseInfoById" parameterType="Long">
        delete from t_home_community_housekeeper_base_info where id = #{id}
    </delete>

    <delete id="deleteHomeCommunityHousekeeperBaseInfoByIds" parameterType="String">
        update t_home_community_housekeeper_base_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
