<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.housekeeper.mapper.HomeCommunityBaseInfoMapper">

    <resultMap type="com.ruoyi.homecare.housekeeper.domain.HomeCommunityBaseInfo" id="HomeCommunityBaseInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="address" column="address"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeCommunityBaseInfoVo">
        select id, name, address, create_time, create_by, update_time, update_by, del_flag, remark from
        t_home_community_base_info
    </sql>

    <select id="selectHomeCommunityBaseInfoList"
            parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCommunityBaseInfo"
            resultMap="HomeCommunityBaseInfoResult">
        <include refid="selectHomeCommunityBaseInfoVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHomeCommunityBaseInfoById" parameterType="Long" resultMap="HomeCommunityBaseInfoResult">
        <include refid="selectHomeCommunityBaseInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeCommunityBaseInfo" parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCommunityBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_community_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="address != null">address,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="address != null">#{address},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeCommunityBaseInfo"
            parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCommunityBaseInfo">
        update t_home_community_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="address != null">address = #{address},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeCommunityBaseInfoById" parameterType="Long">
        delete from t_home_community_base_info where id = #{id}
    </delete>

    <delete id="deleteHomeCommunityBaseInfoByIds" parameterType="String">
        update t_home_community_base_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getCommunityList" resultType="cn.hutool.json.JSONObject">
        select id as value,name as label from t_home_community_base_info where del_flag = '0'
    </select>
</mapper>
