<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.housekeeper.mapper.HomeStreetInfoMapper">

    <resultMap type="HomeStreetInfo" id="HomeStreetInfoResult">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="name" column="name"/>
        <result property="sort" column="sort"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="phone" column="phone"/>
        <result property="adminName" column="admin_name"/>
        <result property="callGroupNumber" column="call_group_number"/>
        <result property="type" column="type"/>
        <result property="position" column="position"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
    </resultMap>

    <sql id="selectHomeStreetInfoVo">
        select id, number, name, sort, latitude, longitude, parent_id,sys_user_id, ancestors, del_flag, phone,
        admin_name, call_group_number, type, position, create_by, create_time, update_by, update_time, remark from
        t_home_street_info
    </sql>

    <select id="selectHomeStreetInfoList" parameterType="HomeStreetInfo" resultMap="HomeStreetInfoResult">
        <include refid="selectHomeStreetInfoVo"/>
        <where>
            del_flag = '0'
            <if test="number != null  and number != ''">and number = #{number}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="sort != null ">and sort = #{sort}</if>
            <if test="parentId != null ">and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''">and find_in_set(#{ancestors}, ancestors) or id =
                #{ancestors}
            </if>
            <if test="phone != null  and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="adminName != null  and adminName != ''">and admin_name like concat('%', #{adminName}, '%')</if>
            <if test="callGroupNumber != null  and callGroupNumber != ''">and call_group_number like concat('%',
                #{callGroupNumber}, '%')
            </if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="position != null  and position != ''">and position = #{position}</if>
            <if test="sysUserId != null  and sysUserId != ''">and sys_user_id = #{sysUserId}</if>
        </where>
        order by sort
    </select>

    <select id="selectHomeStreetInfoById" parameterType="Long" resultMap="HomeStreetInfoResult">
        <include refid="selectHomeStreetInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectHomeStreetInfoByParentId" parameterType="Long" resultMap="HomeStreetInfoResult">
        <include refid="selectHomeStreetInfoVo"/>
        where id = #{parentId}
    </select>

    <select id="selectHomeStreetInfoBySysUserId" parameterType="Long" resultMap="HomeStreetInfoResult">
        <include refid="selectHomeStreetInfoVo"/>
        where sys_user_id = #{sysUserId}
    </select>

    <insert id="insertHomeStreetInfo" parameterType="HomeStreetInfo">
        insert into t_home_street_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="number != null">number,</if>
            <if test="name != null">name,</if>
            <if test="sort != null">sort,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="phone != null">phone,</if>
            <if test="adminName != null">admin_name,</if>
            <if test="callGroupNumber != null">call_group_number,</if>
            <if test="type != null">type,</if>
            <if test="position != null">position,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="number != null">#{number},</if>
            <if test="name != null">#{name},</if>
            <if test="sort != null">#{sort},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="phone != null">#{phone},</if>
            <if test="adminName != null">#{adminName},</if>
            <if test="callGroupNumber != null">#{callGroupNumber},</if>
            <if test="type != null">#{type},</if>
            <if test="position != null">#{position},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
        </trim>
    </insert>

    <update id="updateHomeStreetInfo" parameterType="HomeStreetInfo">
        update t_home_street_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="number != null">number = #{number},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="adminName != null">admin_name = #{adminName},</if>
            <if test="callGroupNumber != null">call_group_number = #{callGroupNumber},</if>
            <if test="type != null">type = #{type},</if>
            <if test="position != null">position = #{position},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeStreetInfoById" parameterType="Long">
        update t_home_street_info set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteHomeStreetInfoByIds" parameterType="String">
        update t_home_street_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="hasChildByStreetId" resultType="Integer">
        select count(1) from t_home_street_info where del_flag = '0' and parent_id = #{id}
    </select>

    <select id="checkStreetNameUnique" parameterType="HomeStreetInfo" resultMap="HomeStreetInfoResult">
        <include refid="selectHomeStreetInfoVo"/>
        where name=#{name} and parent_id = #{parentId} and del_flag = '0' limit 1
    </select>
</mapper>
