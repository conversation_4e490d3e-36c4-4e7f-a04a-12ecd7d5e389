<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.housekeeper.mapper.HomeHousekeeperServiceProjectMapper">

    <resultMap type="com.ruoyi.homecare.housekeeper.domain.HomeHousekeeperServiceProject"
               id="HomeHousekeeperServiceProjectResult">
        <result property="id" column="id"/>
        <result property="courierStation" column="courier_station"/>
        <result property="serviceType" column="service_type"/>
        <result property="projectName" column="project_name"/>
        <result property="price" column="price"/>
        <result property="chargeMode" column="charge_mode"/>
        <result property="commissionRate" column="commission_rate"/>
        <result property="projectImg" column="project_img"/>
        <result property="projectRemark" column="project_remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeHousekeeperServiceProjectVo">
        select id, courier_station, service_type, project_name, price, charge_mode, commission_rate, project_img,
        project_remark, create_time, create_by, update_time, update_by, del_flag, remark from
        t_home_housekeeper_service_project
    </sql>

    <select id="selectHomeHousekeeperServiceProjectList"
            parameterType="com.ruoyi.homecare.housekeeper.domain.HomeHousekeeperServiceProject"
            resultMap="HomeHousekeeperServiceProjectResult">
        <include refid="selectHomeHousekeeperServiceProjectVo"/>
        <where>
            del_flag = '0'
            <if test="courierStation != null ">and courier_station = #{courierStation}</if>
            <if test="serviceType != null  and serviceType != ''">and service_type = #{serviceType}</if>
            <if test="projectName != null  and projectName != ''">and project_name like concat('%', #{projectName},
                '%')
            </if>
            <if test="price != null  and price != ''">and price = #{price}</if>
            <if test="chargeMode != null  and chargeMode != ''">and charge_mode = #{chargeMode}</if>
            <if test="commissionRate != null  and commissionRate != ''">and commission_rate = #{commissionRate}</if>
            <if test="projectImg != null  and projectImg != ''">and project_img = #{projectImg}</if>
            <if test="projectRemark != null  and projectRemark != ''">and project_remark = #{projectRemark}</if>
        </where>
    </select>

    <select id="selectHomeHousekeeperServiceProjectById" parameterType="Long"
            resultMap="HomeHousekeeperServiceProjectResult">
        <include refid="selectHomeHousekeeperServiceProjectVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeHousekeeperServiceProject"
            parameterType="com.ruoyi.homecare.housekeeper.domain.HomeHousekeeperServiceProject">
        insert into t_home_housekeeper_service_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="courierStation != null">courier_station,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="projectName != null">project_name,</if>
            <if test="price != null">price,</if>
            <if test="chargeMode != null">charge_mode,</if>
            <if test="commissionRate != null">commission_rate,</if>
            <if test="projectImg != null">project_img,</if>
            <if test="projectRemark != null">project_remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="courierStation != null">#{courierStation},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="price != null">#{price},</if>
            <if test="chargeMode != null">#{chargeMode},</if>
            <if test="commissionRate != null">#{commissionRate},</if>
            <if test="projectImg != null">#{projectImg},</if>
            <if test="projectRemark != null">#{projectRemark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeHousekeeperServiceProject"
            parameterType="com.ruoyi.homecare.housekeeper.domain.HomeHousekeeperServiceProject">
        update t_home_housekeeper_service_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="courierStation != null">courier_station = #{courierStation},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="price != null">price = #{price},</if>
            <if test="chargeMode != null">charge_mode = #{chargeMode},</if>
            <if test="commissionRate != null">commission_rate = #{commissionRate},</if>
            <if test="projectImg != null">project_img = #{projectImg},</if>
            <if test="projectRemark != null">project_remark = #{projectRemark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeHousekeeperServiceProjectById" parameterType="Long">
        delete from t_home_housekeeper_service_project where id = #{id}
    </delete>

    <delete id="deleteHomeHousekeeperServiceProjectByIds" parameterType="String">
        update t_home_housekeeper_service_project set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
