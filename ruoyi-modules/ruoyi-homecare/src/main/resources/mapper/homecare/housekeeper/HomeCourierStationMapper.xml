<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.housekeeper.mapper.HomeCourierStationMapper">

    <resultMap type="com.ruoyi.homecare.housekeeper.domain.HomeCourierStation" id="HomeCourierStationResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="location" column="location"/>
        <result property="number" column="number"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeCourierStationVo">
        select id, name, location, number, create_time, create_by, update_time, update_by, del_flag, remark from
        t_home_courier_station
    </sql>

    <select id="selectHomeCourierStationList" parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCourierStation"
            resultMap="HomeCourierStationResult">
        <include refid="selectHomeCourierStationVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="location != null  and location != ''">and location like concat('%', #{location}, '%')</if>
            <if test="number != null  and number != ''">and number = #{number}</if>
        </where>
    </select>

    <select id="selectHomeCourierStationById" parameterType="Long" resultMap="HomeCourierStationResult">
        <include refid="selectHomeCourierStationVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeCourierStation" parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCourierStation">
        insert into t_home_courier_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="location != null">location,</if>
            <if test="number != null">number,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="location != null">#{location},</if>
            <if test="number != null">#{number},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeCourierStation" parameterType="com.ruoyi.homecare.housekeeper.domain.HomeCourierStation">
        update t_home_courier_station
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="location != null">location = #{location},</if>
            <if test="number != null">number = #{number},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeCourierStationById" parameterType="Long">
        delete from t_home_courier_station where id = #{id}
    </delete>

    <delete id="deleteHomeCourierStationByIds" parameterType="String">
        update t_home_courier_station set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getAllCourierStationList" resultType="cn.hutool.json.JSONObject">
        select id as value ,name as label from t_home_courier_station where del_flag = '0'
    </select>
</mapper>
