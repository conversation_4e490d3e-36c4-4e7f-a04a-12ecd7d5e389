<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.callCenter.mapper.HomeCallOutRecordMapper">

    <resultMap type="com.ruoyi.homecare.callCenter.domain.HomeCallOutRecord" id="HomeCallOutRecordResult">
        <result property="id" column="id"/>
        <result property="callPerson" column="call_person"/>
        <result property="callNumber" column="call_number"/>
        <result property="calledNumber" column="called_number"/>
        <result property="hangUpType" column="hang_up_type"/>
        <result property="callTime" column="call_time"/>
        <result property="hangUpTime" column="hang_up_time"/>
        <result property="callResults" column="call_results"/>
        <result property="callDuration" column="call_duration"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeCallOutRecordVo">
        select id, call_person, call_number, called_number, hang_up_type, call_time, hang_up_time, call_results,
        call_duration, create_time, create_by, update_time, update_by, del_flag, remark from t_home_call_out_record
    </sql>

    <select id="selectHomeCallOutRecordList" parameterType="com.ruoyi.homecare.callCenter.domain.HomeCallOutRecord"
            resultMap="HomeCallOutRecordResult">
        <include refid="selectHomeCallOutRecordVo"/>
        <where>
            del_flag = '0'
            <if test="callPerson != null  and callPerson != ''">and call_person like concat('%', #{callPerson}, '%')
            </if>
            <if test="callNumber != null  and callNumber != ''">and call_number like concat('%', #{callNumber}, '%')
            </if>
            <if test="calledNumber != null  and calledNumber != ''">and called_number like concat('%', #{calledNumber},
                '%')
            </if>
            <if test="hangUpType != null  and hangUpType != ''">and hang_up_type = #{hangUpType}</if>
            <if test="params.beginCallTime != null and params.beginCallTime != '' and params.endCallTime != null and params.endCallTime != ''">
                and DATE_FORMAT(call_time,'%Y-%d-%m') between DATE_FORMAT(#{params.beginCallTime},'%Y-%d-%m') and
                date_format(#{params.endCallTime},'%Y-%d-%m')
            </if>
            <if test="params.beginHangUpTime != null and params.beginHangUpTime != '' and params.endHangUpTime != null and params.endHangUpTime != ''">
                and hang_up_time between #{params.beginHangUpTime} and #{params.endHangUpTime}
            </if>
            <if test="callResults != null  and callResults != ''">and call_results = #{callResults}</if>
            <if test="callDuration != null  and callDuration != ''">and call_duration = #{callDuration}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHomeCallOutRecordById" parameterType="Long" resultMap="HomeCallOutRecordResult">
        <include refid="selectHomeCallOutRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeCallOutRecord" parameterType="com.ruoyi.homecare.callCenter.domain.HomeCallOutRecord">
        insert into t_home_call_out_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="callPerson != null">call_person,</if>
            <if test="callNumber != null">call_number,</if>
            <if test="calledNumber != null">called_number,</if>
            <if test="hangUpType != null">hang_up_type,</if>
            <if test="callTime != null">call_time,</if>
            <if test="hangUpTime != null">hang_up_time,</if>
            <if test="callResults != null">call_results,</if>
            <if test="callDuration != null">call_duration,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="callPerson != null">#{callPerson},</if>
            <if test="callNumber != null">#{callNumber},</if>
            <if test="calledNumber != null">#{calledNumber},</if>
            <if test="hangUpType != null">#{hangUpType},</if>
            <if test="callTime != null">#{callTime},</if>
            <if test="hangUpTime != null">#{hangUpTime},</if>
            <if test="callResults != null">#{callResults},</if>
            <if test="callDuration != null">#{callDuration},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeCallOutRecord" parameterType="com.ruoyi.homecare.callCenter.domain.HomeCallOutRecord">
        update t_home_call_out_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="callPerson != null">call_person = #{callPerson},</if>
            <if test="callNumber != null">call_number = #{callNumber},</if>
            <if test="calledNumber != null">called_number = #{calledNumber},</if>
            <if test="hangUpType != null">hang_up_type = #{hangUpType},</if>
            <if test="callTime != null">call_time = #{callTime},</if>
            <if test="hangUpTime != null">hang_up_time = #{hangUpTime},</if>
            <if test="callResults != null">call_results = #{callResults},</if>
            <if test="callDuration != null">call_duration = #{callDuration},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeCallOutRecordById" parameterType="Long">
        delete from t_home_call_out_record where id = #{id}
    </delete>

    <delete id="deleteHomeCallOutRecordByIds" parameterType="String">
        update t_home_call_out_record set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
