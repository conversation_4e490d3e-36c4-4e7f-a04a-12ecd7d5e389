<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeoplePhysicalExaminationRecordsMapper">

    <resultMap type="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeoplePhysicalExaminationRecords"
               id="ElderlyPeoplePhysicalExaminationRecordsResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="medicalExaminationHospital" column="medical_examination_hospital"/>
        <result property="physicalExaminationTime" column="physical_examination_time"/>
        <result property="nextPhysicalExaminationTime" column="next_physical_examination_time"/>
        <result property="physicalExaminationImg" column="physical_examination_img"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectElderlyPeoplePhysicalExaminationRecordsVo">
        select id, user_id, medical_examination_hospital, physical_examination_time, next_physical_examination_time,
        physical_examination_img, create_time, create_by, update_time, update_by, del_flag, remark from
        t_home_elderly_people_physical_examination_records
    </sql>

    <select id="selectElderlyPeoplePhysicalExaminationRecordsList"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeoplePhysicalExaminationRecords"
            resultMap="ElderlyPeoplePhysicalExaminationRecordsResult">
        <include refid="selectElderlyPeoplePhysicalExaminationRecordsVo"/>
        <where>
            del_flag = '0'
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="medicalExaminationHospital != null  and medicalExaminationHospital != ''">and
                medical_examination_hospital = #{medicalExaminationHospital}
            </if>
            <if test="physicalExaminationTime != null ">and physical_examination_time = #{physicalExaminationTime}</if>
            <if test="nextPhysicalExaminationTime != null ">and next_physical_examination_time =
                #{nextPhysicalExaminationTime}
            </if>
            <if test="physicalExaminationImg != null  and physicalExaminationImg != ''">and physical_examination_img =
                #{physicalExaminationImg}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectElderlyPeoplePhysicalExaminationRecordsById" parameterType="Long"
            resultMap="ElderlyPeoplePhysicalExaminationRecordsResult">
        <include refid="selectElderlyPeoplePhysicalExaminationRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertElderlyPeoplePhysicalExaminationRecords"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeoplePhysicalExaminationRecords"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_elderly_people_physical_examination_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="medicalExaminationHospital != null">medical_examination_hospital,</if>
            <if test="physicalExaminationTime != null">physical_examination_time,</if>
            <if test="nextPhysicalExaminationTime != null">next_physical_examination_time,</if>
            <if test="physicalExaminationImg != null">physical_examination_img,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="medicalExaminationHospital != null">#{medicalExaminationHospital},</if>
            <if test="physicalExaminationTime != null">#{physicalExaminationTime},</if>
            <if test="nextPhysicalExaminationTime != null">#{nextPhysicalExaminationTime},</if>
            <if test="physicalExaminationImg != null">#{physicalExaminationImg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateElderlyPeoplePhysicalExaminationRecords"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeoplePhysicalExaminationRecords">
        update t_home_elderly_people_physical_examination_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="medicalExaminationHospital != null">medical_examination_hospital =
                #{medicalExaminationHospital},
            </if>
            <if test="physicalExaminationTime != null">physical_examination_time = #{physicalExaminationTime},</if>
            <if test="nextPhysicalExaminationTime != null">next_physical_examination_time =
                #{nextPhysicalExaminationTime},
            </if>
            <if test="physicalExaminationImg != null">physical_examination_img = #{physicalExaminationImg},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyPeoplePhysicalExaminationRecordsById" parameterType="Long">
        delete from t_home_elderly_people_physical_examination_records where id = #{id}
    </delete>

    <delete id="deleteElderlyPeoplePhysicalExaminationRecordsByIds" parameterType="String">
        update t_home_elderly_people_physical_examination_records set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
