<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleFamilyInfoMapper">

    <resultMap type="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleFamilyInfo"
               id="HomeCareElderlyPeopleFamilyInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="relation" column="relation"/>
        <result property="liveWithStatus" column="live_with_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectElderlyPeopleFamilyInfoVo">
        select id, name, phone, relation, live_with_status, create_time, create_by, update_time, update_by, del_flag,
        remark, user_id from t_home_elderly_people_family_info
    </sql>

    <select id="selectElderlyPeopleFamilyInfoList"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleFamilyInfo"
            resultMap="HomeCareElderlyPeopleFamilyInfoResult">
        <include refid="selectElderlyPeopleFamilyInfoVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="relation != null  and relation != ''">and relation = #{relation}</if>
            <if test="liveWithStatus != null  and liveWithStatus != ''">and live_with_status = #{liveWithStatus}</if>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectElderlyPeopleFamilyInfoById" parameterType="String"
            resultMap="HomeCareElderlyPeopleFamilyInfoResult">
        <include refid="selectElderlyPeopleFamilyInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertElderlyPeopleFamilyInfo"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleFamilyInfo">
        insert into t_home_elderly_people_family_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="relation != null">relation,</if>
            <if test="liveWithStatus != null">live_with_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="relation != null">#{relation},</if>
            <if test="liveWithStatus != null">#{liveWithStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateElderlyPeopleFamilyInfo"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleFamilyInfo">
        update t_home_elderly_people_family_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="relation != null">relation = #{relation},</if>
            <if test="liveWithStatus != null">live_with_status = #{liveWithStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyPeopleFamilyInfoById" parameterType="String">
        delete from t_home_elderly_people_family_info where id = #{id}
    </delete>

    <delete id="deleteElderlyPeopleFamilyInfoByIds" parameterType="String">
        update t_home_elderly_people_family_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
