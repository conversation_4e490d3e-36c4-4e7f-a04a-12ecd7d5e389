<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.elderlyPeople.mapper.HomeMedicationRemindMapper">

    <resultMap type="com.ruoyi.homecare.elderlyPeople.domain.HomeMedicationRemind" id="HomeMedicationRemindResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
        <result property="frequency" column="frequency"/>
        <result property="sendTime" column="send_time"/>
        <result property="sendContent" column="send_content"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeMedicationRemindVo">
        SELECT
        a.id,
        a.user_id,
        a.begin_date,
        a.end_date,
        a.frequency,
        a.send_time,
        a.send_content,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        b.name as userName,
        b.phone as phone,
        a.remark
        FROM
        t_home_medication_remind as a left join t_home_elderly_people_info as b on a.user_id = b.id
    </sql>

    <select id="selectHomeMedicationRemindList"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.HomeMedicationRemind"
            resultMap="HomeMedicationRemindResult">
        <include refid="selectHomeMedicationRemindVo"/>
        <where>
            <if test="userId != null  and userId != ''">and a.user_id = #{userId}</if>
            <if test="beginDate != null ">and a.begin_date = #{beginDate}</if>
            <if test="endDate != null ">and a.end_date = #{endDate}</if>
            <if test="userName != null ">and b.name like concat('%',#{userName},'%%')</if>
            <if test="phone != null ">and b.phone = #{phone}</if>
            <if test="frequency != null  and frequency != ''">and a.frequency = #{frequency}</if>
            <if test="params.beginSendTime != null and params.beginSendTime != '' and params.endSendTime != null and params.endSendTime != ''">
                and a.send_time between #{params.beginSendTime} and #{params.endSendTime}
            </if>
            <if test="sendContent != null  and sendContent != ''">and a.send_content = #{sendContent}</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectHomeMedicationRemindById" parameterType="Long" resultMap="HomeMedicationRemindResult">
        <include refid="selectHomeMedicationRemindVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertHomeMedicationRemind" parameterType="com.ruoyi.homecare.elderlyPeople.domain.HomeMedicationRemind"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_medication_remind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="beginDate != null">begin_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="frequency != null">frequency,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="sendContent != null">send_content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="beginDate != null">#{beginDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="sendContent != null">#{sendContent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeMedicationRemind"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.HomeMedicationRemind">
        update t_home_medication_remind
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="beginDate != null">begin_date = #{beginDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="sendContent != null">send_content = #{sendContent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeMedicationRemindById" parameterType="Long">
        delete from t_home_medication_remind where id = #{id}
    </delete>

    <delete id="deleteHomeMedicationRemindByIds" parameterType="String">
        delete from t_home_medication_remind where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
