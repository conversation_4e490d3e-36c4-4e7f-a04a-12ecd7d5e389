<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleMedicationHistoryMapper">

    <resultMap type="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleMedicationHistory"
               id="HomeCareElderlyPeopleMedicationHistoryResult">
        <result property="id" column="id"/>
        <result property="diseaseName" column="disease_name"/>
        <result property="medicineName" column="medicine_name"/>
        <result property="meteringUsage" column="metering_usage"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectElderlyPeopleMedicationHistoryVo">
        select id, disease_name, medicine_name, metering_usage, begin_time, end_time, status, create_time, create_by,
        update_time, update_by, del_flag, remark, user_id from t_home_elderly_people_medication_history
    </sql>

    <select id="selectElderlyPeopleMedicationHistoryList"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleMedicationHistory"
            resultMap="HomeCareElderlyPeopleMedicationHistoryResult">
        <include refid="selectElderlyPeopleMedicationHistoryVo"/>
        <where>
            del_flag = '0'
            <if test="diseaseName != null  and diseaseName != ''">and disease_name like concat('%', #{diseaseName},
                '%')
            </if>
            <if test="medicineName != null  and medicineName != ''">and medicine_name like concat('%', #{medicineName},
                '%')
            </if>
            <if test="meteringUsage != null  and meteringUsage != ''">and metering_usage = #{meteringUsage}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            ORDER BY status , create_time
        </where>
    </select>

    <select id="selectElderlyPeopleMedicationHistoryById" parameterType="String"
            resultMap="HomeCareElderlyPeopleMedicationHistoryResult">
        <include refid="selectElderlyPeopleMedicationHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertElderlyPeopleMedicationHistory"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleMedicationHistory">
        insert into t_home_elderly_people_medication_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="diseaseName != null">disease_name,</if>
            <if test="medicineName != null">medicine_name,</if>
            <if test="meteringUsage != null">metering_usage,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="diseaseName != null">#{diseaseName},</if>
            <if test="medicineName != null">#{medicineName},</if>
            <if test="meteringUsage != null">#{meteringUsage},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateElderlyPeopleMedicationHistory"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleMedicationHistory">
        update t_home_elderly_people_medication_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="diseaseName != null">disease_name = #{diseaseName},</if>
            <if test="medicineName != null">medicine_name = #{medicineName},</if>
            <if test="meteringUsage != null">metering_usage = #{meteringUsage},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyPeopleMedicationHistoryById" parameterType="String">
        delete from t_home_elderly_people_medication_history where id = #{id}
    </delete>

    <delete id="deleteElderlyPeopleMedicationHistoryByIds" parameterType="String">
        update t_home_elderly_people_medication_history set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
