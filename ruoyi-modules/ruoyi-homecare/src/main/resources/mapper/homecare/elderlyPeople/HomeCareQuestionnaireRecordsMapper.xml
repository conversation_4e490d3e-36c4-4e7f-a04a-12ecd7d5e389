<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.elderlyPeople.mapper.HomeCareQuestionnaireRecordsMapper">

    <resultMap type="com.ruoyi.homecare.elderlyPeople.domain.QuestionnaireRecords"
               id="HomeCareQuestionnaireRecordsResult">
        <result property="id" column="id"/>
        <result property="baseId" column="base_id"/>
        <result property="jsonData" column="json_data"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectQuestionnaireRecordsVo">
        select id, base_id, json_data, create_time, create_by, update_time, update_by, del_flag, remark from
        t_home_questionnaire_records
    </sql>

    <select id="selectQuestionnaireRecordsList"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.QuestionnaireRecords"
            resultMap="HomeCareQuestionnaireRecordsResult">
        <include refid="selectQuestionnaireRecordsVo"/>
        <where>
            <if test="baseId != null ">and base_id = #{baseId}</if>
            <if test="jsonData != null  and jsonData != ''">and json_data = #{jsonData}</if>
        </where>
    </select>

    <select id="selectQuestionnaireRecordsById" parameterType="Long" resultMap="HomeCareQuestionnaireRecordsResult">
        <include refid="selectQuestionnaireRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertQuestionnaireRecords"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.QuestionnaireRecords">
        insert into t_home_questionnaire_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="baseId != null">base_id,</if>
            <if test="jsonData != null">json_data,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="baseId != null">#{baseId},</if>
            <if test="jsonData != null">#{jsonData},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateQuestionnaireRecords"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.QuestionnaireRecords">
        update t_home_questionnaire_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="baseId != null">base_id = #{baseId},</if>
            <if test="jsonData != null">json_data = #{jsonData},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQuestionnaireRecordsById" parameterType="Long">
        delete from t_home_questionnaire_records where id = #{id}
    </delete>

    <delete id="deleteQuestionnaireRecordsByIds" parameterType="String">
        delete from t_home_questionnaire_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getByBaseId" parameterType="Long" resultMap="HomeCareQuestionnaireRecordsResult">
        <include refid="selectQuestionnaireRecordsVo"/>
        where base_id = #{baseId}
    </select>
</mapper>
