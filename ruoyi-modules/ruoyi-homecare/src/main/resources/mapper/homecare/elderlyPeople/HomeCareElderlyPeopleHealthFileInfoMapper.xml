<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleHealthFileInfoMapper">

    <resultMap type="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthFileInfo"
               id="ElderlyPeopleHealthFileInfoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="bloodPressure" column="blood_pressure"/>
        <result property="bloodPressureMeasurementTime" column="blood_pressure_measurement_time"/>
        <result property="bloodSugar" column="blood_sugar"/>
        <result property="bloodSugarMeasurementTime" column="blood_sugar_measurement_time"/>
        <result property="leftVision" column="left_vision"/>
        <result property="rightVision" column="right_vision"/>
        <result property="correctedLeftVision" column="corrected_left_vision"/>
        <result property="correctedRightVision" column="corrected_right_vision"/>
        <result property="colorVision" column="color_vision"/>
        <result property="eyeComplication" column="eye_complication"/>
        <result property="eyeOther" column="eye_other"/>
        <result property="hearingSituation" column="hearing_situation"/>
        <result property="earComplication" column="ear_complication"/>
        <result property="earOther" column="ear_other"/>
        <result property="olfactorySituation" column="olfactory_situation"/>
        <result property="noseComplication" column="nose_complication"/>
        <result property="voiceSituation" column="voice_situation"/>
        <result property="throatComplication" column="throat_complication"/>
        <result property="throatOther" column="throat_other"/>
        <result property="internalMedicineComplication" column="internal_medicine_complication"/>
        <result property="internalMedicineOther" column="internal_medicine_other"/>
        <result property="surgicalComplication" column="surgical_complication"/>
        <result property="surgicalOther" column="surgical_other"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="username" column="username"/>
        <result property="sex" column="sex"/>
        <result property="idCardNum" column="idCardNum"/>
        <result property="dateBirth" column="dateBirth"/>
    </resultMap>

    <sql id="selectElderlyPeopleHealthFileInfoVo">
        select id, user_id, blood_pressure, blood_pressure_measurement_time, blood_sugar, blood_sugar_measurement_time,
        left_vision, right_vision, corrected_left_vision, corrected_right_vision, color_vision, eye_complication,
        eye_other, hearing_situation, ear_complication, ear_other, olfactory_situation, nose_complication,
        voice_situation, throat_complication, throat_other, internal_medicine_complication, internal_medicine_other,
        surgical_complication, surgical_other, create_time, create_by, update_time, update_by, del_flag, remark from
        t_home_elderly_people_health_file_info
    </sql>

    <select id="selectElderlyPeopleHealthFileInfoList"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthFileInfo"
            resultMap="ElderlyPeopleHealthFileInfoResult">
        <include refid="selectElderlyPeopleHealthFileInfoVo"/>
        <where>
            del_flag = '0'
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="bloodPressure != null  and bloodPressure != ''">and blood_pressure = #{bloodPressure}</if>
            <if test="bloodPressureMeasurementTime != null ">and blood_pressure_measurement_time =
                #{bloodPressureMeasurementTime}
            </if>
            <if test="bloodSugar != null  and bloodSugar != ''">and blood_sugar = #{bloodSugar}</if>
            <if test="bloodSugarMeasurementTime != null ">and blood_sugar_measurement_time =
                #{bloodSugarMeasurementTime}
            </if>
            <if test="leftVision != null  and leftVision != ''">and left_vision = #{leftVision}</if>
            <if test="rightVision != null  and rightVision != ''">and right_vision = #{rightVision}</if>
            <if test="correctedLeftVision != null  and correctedLeftVision != ''">and corrected_left_vision =
                #{correctedLeftVision}
            </if>
            <if test="correctedRightVision != null  and correctedRightVision != ''">and corrected_right_vision =
                #{correctedRightVision}
            </if>
            <if test="colorVision != null  and colorVision != ''">and color_vision = #{colorVision}</if>
            <if test="eyeComplication != null  and eyeComplication != ''">and eye_complication = #{eyeComplication}</if>
            <if test="eyeOther != null  and eyeOther != ''">and eye_other = #{eyeOther}</if>
            <if test="hearingSituation != null  and hearingSituation != ''">and hearing_situation =
                #{hearingSituation}
            </if>
            <if test="earComplication != null  and earComplication != ''">and ear_complication = #{earComplication}</if>
            <if test="earOther != null  and earOther != ''">and ear_other = #{earOther}</if>
            <if test="olfactorySituation != null  and olfactorySituation != ''">and olfactory_situation =
                #{olfactorySituation}
            </if>
            <if test="noseComplication != null  and noseComplication != ''">and nose_complication =
                #{noseComplication}
            </if>
            <if test="voiceSituation != null  and voiceSituation != ''">and voice_situation = #{voiceSituation}</if>
            <if test="throatComplication != null  and throatComplication != ''">and throat_complication =
                #{throatComplication}
            </if>
            <if test="throatOther != null  and throatOther != ''">and throat_other = #{throatOther}</if>
            <if test="internalMedicineComplication != null  and internalMedicineComplication != ''">and
                internal_medicine_complication = #{internalMedicineComplication}
            </if>
            <if test="internalMedicineOther != null  and internalMedicineOther != ''">and internal_medicine_other =
                #{internalMedicineOther}
            </if>
            <if test="surgicalComplication != null  and surgicalComplication != ''">and surgical_complication =
                #{surgicalComplication}
            </if>
            <if test="surgicalOther != null  and surgicalOther != ''">and surgical_other = #{surgicalOther}</if>
        </where>
    </select>

    <select id="selectElderlyPeopleHealthFileInfoById" parameterType="Long"
            resultMap="ElderlyPeopleHealthFileInfoResult">
        <include refid="selectElderlyPeopleHealthFileInfoVo"/>
        where id = #{id}
    </select>

    <select id="getDataInfoByUserId" parameterType="String" resultMap="ElderlyPeopleHealthFileInfoResult">
        SELECT
        a.id,
        u.id as user_id,
        u.id_card_num as idCardNum,
        u.name as username,
        u.sex,
        DATE_FORMAT(u.date_birth,'%Y-%m-%d') as dateBirth,
        a.blood_pressure,
        a.blood_pressure_measurement_time,
        a.blood_sugar,
        a.blood_sugar_measurement_time,
        a.left_vision,
        a.right_vision,
        a.corrected_left_vision,
        a.corrected_right_vision,
        a.color_vision,
        a.eye_complication,
        a.eye_other,
        a.hearing_situation,
        a.ear_complication,
        a.ear_other,
        a.olfactory_situation,
        a.nose_complication,
        a.voice_situation,
        a.throat_complication,
        a.throat_other,
        a.internal_medicine_complication,
        a.internal_medicine_other,
        a.surgical_complication,
        a.surgical_other,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_home_elderly_people_info as u left join
        t_home_elderly_people_health_file_info as a on u.id = a.user_id where u.id = #{userId}
    </select>

    <insert id="insertElderlyPeopleHealthFileInfo"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthFileInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_home_elderly_people_health_file_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bloodPressure != null">blood_pressure,</if>
            <if test="bloodPressureMeasurementTime != null">blood_pressure_measurement_time,</if>
            <if test="bloodSugar != null">blood_sugar,</if>
            <if test="bloodSugarMeasurementTime != null">blood_sugar_measurement_time,</if>
            <if test="leftVision != null">left_vision,</if>
            <if test="rightVision != null">right_vision,</if>
            <if test="correctedLeftVision != null">corrected_left_vision,</if>
            <if test="correctedRightVision != null">corrected_right_vision,</if>
            <if test="colorVision != null">color_vision,</if>
            <if test="eyeComplication != null">eye_complication,</if>
            <if test="eyeOther != null">eye_other,</if>
            <if test="hearingSituation != null">hearing_situation,</if>
            <if test="earComplication != null">ear_complication,</if>
            <if test="earOther != null">ear_other,</if>
            <if test="olfactorySituation != null">olfactory_situation,</if>
            <if test="noseComplication != null">nose_complication,</if>
            <if test="voiceSituation != null">voice_situation,</if>
            <if test="throatComplication != null">throat_complication,</if>
            <if test="throatOther != null">throat_other,</if>
            <if test="internalMedicineComplication != null">internal_medicine_complication,</if>
            <if test="internalMedicineOther != null">internal_medicine_other,</if>
            <if test="surgicalComplication != null">surgical_complication,</if>
            <if test="surgicalOther != null">surgical_other,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bloodPressure != null">#{bloodPressure},</if>
            <if test="bloodPressureMeasurementTime != null">#{bloodPressureMeasurementTime},</if>
            <if test="bloodSugar != null">#{bloodSugar},</if>
            <if test="bloodSugarMeasurementTime != null">#{bloodSugarMeasurementTime},</if>
            <if test="leftVision != null">#{leftVision},</if>
            <if test="rightVision != null">#{rightVision},</if>
            <if test="correctedLeftVision != null">#{correctedLeftVision},</if>
            <if test="correctedRightVision != null">#{correctedRightVision},</if>
            <if test="colorVision != null">#{colorVision},</if>
            <if test="eyeComplication != null">#{eyeComplication},</if>
            <if test="eyeOther != null">#{eyeOther},</if>
            <if test="hearingSituation != null">#{hearingSituation},</if>
            <if test="earComplication != null">#{earComplication},</if>
            <if test="earOther != null">#{earOther},</if>
            <if test="olfactorySituation != null">#{olfactorySituation},</if>
            <if test="noseComplication != null">#{noseComplication},</if>
            <if test="voiceSituation != null">#{voiceSituation},</if>
            <if test="throatComplication != null">#{throatComplication},</if>
            <if test="throatOther != null">#{throatOther},</if>
            <if test="internalMedicineComplication != null">#{internalMedicineComplication},</if>
            <if test="internalMedicineOther != null">#{internalMedicineOther},</if>
            <if test="surgicalComplication != null">#{surgicalComplication},</if>
            <if test="surgicalOther != null">#{surgicalOther},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateElderlyPeopleHealthFileInfo"
            parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleHealthFileInfo">
        update t_home_elderly_people_health_file_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bloodPressure != null">blood_pressure = #{bloodPressure},</if>
            <if test="bloodPressureMeasurementTime != null">blood_pressure_measurement_time =
                #{bloodPressureMeasurementTime},
            </if>
            <if test="bloodSugar != null">blood_sugar = #{bloodSugar},</if>
            <if test="bloodSugarMeasurementTime != null">blood_sugar_measurement_time = #{bloodSugarMeasurementTime},
            </if>
            <if test="leftVision != null">left_vision = #{leftVision},</if>
            <if test="rightVision != null">right_vision = #{rightVision},</if>
            <if test="correctedLeftVision != null">corrected_left_vision = #{correctedLeftVision},</if>
            <if test="correctedRightVision != null">corrected_right_vision = #{correctedRightVision},</if>
            <if test="colorVision != null">color_vision = #{colorVision},</if>
            <if test="eyeComplication != null">eye_complication = #{eyeComplication},</if>
            <if test="eyeOther != null">eye_other = #{eyeOther},</if>
            <if test="hearingSituation != null">hearing_situation = #{hearingSituation},</if>
            <if test="earComplication != null">ear_complication = #{earComplication},</if>
            <if test="earOther != null">ear_other = #{earOther},</if>
            <if test="olfactorySituation != null">olfactory_situation = #{olfactorySituation},</if>
            <if test="noseComplication != null">nose_complication = #{noseComplication},</if>
            <if test="voiceSituation != null">voice_situation = #{voiceSituation},</if>
            <if test="throatComplication != null">throat_complication = #{throatComplication},</if>
            <if test="throatOther != null">throat_other = #{throatOther},</if>
            <if test="internalMedicineComplication != null">internal_medicine_complication =
                #{internalMedicineComplication},
            </if>
            <if test="internalMedicineOther != null">internal_medicine_other = #{internalMedicineOther},</if>
            <if test="surgicalComplication != null">surgical_complication = #{surgicalComplication},</if>
            <if test="surgicalOther != null">surgical_other = #{surgicalOther},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyPeopleHealthFileInfoById" parameterType="Long">
        delete from t_home_elderly_people_health_file_info where id = #{id}
    </delete>

    <delete id="deleteElderlyPeopleHealthFileInfoByIds" parameterType="String">
        update t_home_elderly_people_health_file_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
