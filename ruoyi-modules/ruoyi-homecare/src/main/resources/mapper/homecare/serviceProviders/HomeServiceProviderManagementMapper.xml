<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProviderManagementMapper">

    <resultMap type="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement"
               id="HomeServiceProviderManagementResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="nature" column="nature"/>
        <result property="userName" column="user_name"/>
        <result property="password" column="password"/>
        <result property="principal" column="principal"/>
        <result property="phone" column="phone"/>
        <result property="address" column="address"/>
        <result property="serviceState" column="service_state"/>
        <result property="holidaySeason" column="holiday_season"/>
        <result property="weekendFlag" column="weekend_flag"/>
        <result property="businessHours" column="business_hours"/>
        <result property="fixedTelephone" column="fixed_telephone"/>
        <result property="serviceType" column="service_type"/>
        <result property="businessLicense" column="business_license"/>
        <result property="merchantsPhotos" column="merchants_photos"/>
        <result property="contractAttachment" column="contract_attachment"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="createClient" column="create_client"/>
        <result property="state" column="state"/>
        <result property="communityId" column="community_id"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditCause" column="audit_cause"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="label" column="label"/>
        <result property="totalSales" column="total_sales"/>
        <result property="praiseNum" column="praise_num"/>
        <result property="badNum" column="bad_num"/>
        <result property="totalOrderNum" column="total_order_num"/>
        <result property="praiseRate" column="praiseRate"/>
        <result property="evaluateNum" column="evaluate_num"/>
        <result property="evaluateNumValue" column="evaluate_num_value"/>
        <!--        <result property="praiseRate2"    column="praiseRate2"    />-->
    </resultMap>

    <sql id="selectHomeServiceProviderManagementVo">
        select id, name,
        nature,state,sys_user_id,bad_num,total_order_num,label,evaluate_num,evaluate_num_value,total_sales,praise_num,
        create_client,audit_cause,audit_time, community_id, user_name, password, principal, phone, address,
        service_state, holiday_season, weekend_flag, business_hours, fixed_telephone, service_type, business_license,
        merchants_photos, contract_attachment, create_time, create_by, update_time, update_by, del_flag, remark
        ,ban_flag,
        -- IFNULL(round( (IFNULL(total_order_num,0)-IFNULL(bad_num,0))/IFNULL(total_order_num,0) * 100, 2 ),'100.00') as
        praiseRate2,
        IFNULL(CONCAT( round( (IFNULL(total_order_num,0)-IFNULL(bad_num,0))/IFNULL(total_order_num,0) * 100, 2 ), '%'
        ),'100.00%') as praiseRate
        from t_home_service_provider_management
    </sql>

    <select id="selectHomeServiceProviderManagementList"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement"
            resultMap="HomeServiceProviderManagementResult">
        <include refid="selectHomeServiceProviderManagementVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="nature != null  and nature != ''">and nature = #{nature}</if>
            <if test="createClient != null  and createClient != ''">and create_client = #{createClient}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="password != null  and password != ''">and password = #{password}</if>
            <if test="principal != null  and principal != ''">and principal like concat('%', #{principal}, '%')</if>
            <if test="label != null  and label != ''">and label like concat('%', #{label}, '%')</if>
            <if test="phone != null  and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="serviceState != null  and serviceState != ''">and service_state = #{serviceState}</if>
            <if test="holidaySeason != null  and holidaySeason != ''">and holiday_season = #{holidaySeason}</if>
            <if test="weekendFlag != null  and weekendFlag != ''">and weekend_flag = #{weekendFlag}</if>
            <if test="businessHours != null  and businessHours != ''">and business_hours = #{businessHours}</if>
            <if test="fixedTelephone != null  and fixedTelephone != ''">and fixed_telephone = #{fixedTelephone}</if>
            <if test="serviceType != null  and serviceType != ''">and service_type = #{serviceType}</if>
            <if test="sysUserId != null  and sysUserId != ''">and sys_user_id = #{sysUserId}</if>
            <if test="banFlag != null  and banFlag != ''">and ban_flag = #{banFlag}</if>

        </where>
        order by audit_time desc
    </select>

    <select id="getServiceProviderListInfo"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.vo.AppServiceProviderManagementVo"
            resultType="com.ruoyi.homecare.serviceProviders.domain.vo.AppServiceProviderManagementVo">
        SELECT
        id AS id,
        name AS name,
        nature AS nature,
        business_hours AS businessHours,
        fixed_telephone AS fixedTelephone ,
        community_id as communityId,
        total_sales as totalSales,
        service_state as serviceState,
        substring_index(merchants_photos,',',1) as merchantsPhotos,
        -- merchants_photos as merchantsPhotos,
        praise_num as praiseNum,
        -- IFNULL(CONCAT( round( praise_num/total_sales * 100, 2 ), '%' ),'0.00%') as praiseRate
        IFNULL(CONCAT( round( (IFNULL(total_order_num,0)-IFNULL(bad_num,0))/IFNULL(total_order_num,0) * 100, 2 ), '%'
        ),'100.00%') as praiseRate
        FROM
        t_home_service_provider_management
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="label != null  and label != ''">and FIND_IN_SET(#{label},label)</if>
            <if test="communityId != null  and communityId != ''">and FIND_IN_SET(#{communityId},community_id)</if>
            <if test="serviceType != null  and serviceType != ''">and service_type like concat('%', #{serviceType},
                '%')
            </if>
            <if test="serviceState != null  and serviceState != ''">and service_state = #{serviceState}</if>
        </where>
    </select>


    <select id="selectHomeServiceProviderManagementById" parameterType="Long"
            resultMap="HomeServiceProviderManagementResult">
        <include refid="selectHomeServiceProviderManagementVo"/>
        where id = #{id}
    </select>

    <select id="getServiceProviderBySysUserId" parameterType="Long" resultMap="HomeServiceProviderManagementResult">
        <include refid="selectHomeServiceProviderManagementVo"/>
        where sys_user_id = #{sysUserId}
    </select>

    <insert id="insertHomeServiceProviderManagement"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_service_provider_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="nature != null">nature,</if>
            <if test="state != null">state,</if>
            <if test="createClient != null">create_client,</if>
            <if test="userName != null">user_name,</if>
            <if test="password != null">password,</if>
            <if test="principal != null">principal,</if>
            <if test="phone != null">phone,</if>
            <if test="address != null">address,</if>
            <if test="serviceState != null">service_state,</if>
            <if test="holidaySeason != null">holiday_season,</if>
            <if test="weekendFlag != null">weekend_flag,</if>
            <if test="businessHours != null">business_hours,</if>
            <if test="fixedTelephone != null">fixed_telephone,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="merchantsPhotos != null">merchants_photos,</if>
            <if test="contractAttachment != null">contract_attachment,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="communityId != null">community_id,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="auditCause != null">audit_cause,</if>
            <if test="label != null">label,</if>
            <if test="totalSales != null">total_sales,</if>
            <if test="praiseNum != null">praise_num,</if>
            <if test="totalOrderNum != null">total_order_num,</if>
            <if test="badNum != null">bad_num,</if>
            <if test="banFlag != null">ban_flag,</if>
            <if test="evaluateNumValue != null">evaluate_num_value,</if>
            <if test="evaluateNum != null">evaluate_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="nature != null">#{nature},</if>
            <if test="state != null">#{state},</if>
            <if test="createClient != null">#{createClient},</if>
            <if test="userName != null">#{userName},</if>
            <if test="password != null">#{password},</if>
            <if test="principal != null">#{principal},</if>
            <if test="phone != null">#{phone},</if>
            <if test="address != null">#{address},</if>
            <if test="serviceState != null">#{serviceState},</if>
            <if test="holidaySeason != null">#{holidaySeason},</if>
            <if test="weekendFlag != null">#{weekendFlag},</if>
            <if test="businessHours != null">#{businessHours},</if>
            <if test="fixedTelephone != null">#{fixedTelephone},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="merchantsPhotos != null">#{merchantsPhotos},</if>
            <if test="contractAttachment != null">#{contractAttachment},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="auditCause != null">#{auditCause},</if>
            <if test="label != null">#{label},</if>
            <if test="totalSales != null">#{totalSales},</if>
            <if test="praiseNum != null">#{praiseNum},</if>
            <if test="totalOrderNum != null">#{totalOrderNum},</if>
            <if test="badNum != null">#{badNum},</if>
            <if test="banFlag != null">#{banFlag},</if>
            <if test="evaluateNumValue != null">#{evaluateNumValue},</if>
            <if test="evaluateNum != null">#{evaluateNum},</if>
        </trim>
    </insert>

    <update id="updateHomeServiceProviderManagement"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement">
        update t_home_service_provider_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="createClient != null">create_client = #{createClient},</if>
            <if test="state != null">state = #{state},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="password != null">password = #{password},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="serviceState != null">service_state = #{serviceState},</if>
            <if test="holidaySeason != null">holiday_season = #{holidaySeason},</if>
            <if test="weekendFlag != null">weekend_flag = #{weekendFlag},</if>
            <if test="businessHours != null">business_hours = #{businessHours},</if>
            <if test="fixedTelephone != null">fixed_telephone = #{fixedTelephone},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="businessLicense != null">business_license = #{businessLicense},</if>
            <if test="merchantsPhotos != null">merchants_photos = #{merchantsPhotos},</if>
            <if test="contractAttachment != null">contract_attachment = #{contractAttachment},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="auditCause != null">audit_cause = #{auditCause},</if>
            <if test="label != null">label = #{label},</if>
            <if test="totalSales != null">total_sales = #{totalSales},</if>
            <if test="praiseNum != null">praise_num = #{praiseNum},</if>
            <if test="totalOrderNum != null">total_order_num = #{totalOrderNum},</if>
            <if test="badNum != null">bad_num = #{badNum},</if>
            <if test="banFlag != null">ban_flag = #{banFlag},</if>
            <if test="evaluateNumValue != null">evaluate_num_value = #{evaluateNumValue},</if>
            <if test="evaluateNum != null">evaluate_num = #{evaluateNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeServiceProviderManagementById" parameterType="Long">
        delete from t_home_service_provider_management where id = #{id}
    </delete>

    <delete id="deleteHomeServiceProviderManagementByIds" parameterType="String">
        update t_home_service_provider_management set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getServiceProviderList" resultType="cn.hutool.json.JSONObject" parameterType="STRING">
        select id as value , name as label from t_home_service_provider_management
        <where>
            del_flag = '0'
            <if test="name != null and name != '' ">
                and name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

</mapper>
