<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProviderCommunityIndexMapper">

    <resultMap type="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex"
               id="HomeServiceProviderCommunityIndexResult">
        <result property="id" column="id"/>
        <result property="serviceProviderId" column="service_provider_id"/>
        <result property="communityId" column="community_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeServiceProviderCommunityIndexVo">
        select id, service_provider_id, community_id, create_time, create_by, update_time, update_by, del_flag, remark
        from t_home_service_provider_community_index
    </sql>

    <select id="selectHomeServiceProviderCommunityIndexList"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex"
            resultMap="HomeServiceProviderCommunityIndexResult">
        <include refid="selectHomeServiceProviderCommunityIndexVo"/>
        <where>
            <if test="serviceProviderId != null ">and service_provider_id = #{serviceProviderId}</if>
            <if test="communityId != null ">and community_id = #{communityId}</if>
        </where>
    </select>

    <select id="selectHomeServiceProviderCommunityIndexById" parameterType="Long"
            resultMap="HomeServiceProviderCommunityIndexResult">
        <include refid="selectHomeServiceProviderCommunityIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeServiceProviderCommunityIndex"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_service_provider_community_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceProviderId != null">service_provider_id,</if>
            <if test="communityId != null">community_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceProviderId != null">#{serviceProviderId},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeServiceProviderCommunityIndex"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderCommunityIndex">
        update t_home_service_provider_community_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceProviderId != null">service_provider_id = #{serviceProviderId},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeServiceProviderCommunityIndexById" parameterType="Long">
        delete from t_home_service_provider_community_index where id = #{id}
    </delete>

    <delete id="deleteHomeServiceProviderCommunityIndexByIds" parameterType="String">
        delete from t_home_service_provider_community_index where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByServiceId" parameterType="Long">
        delete from t_home_service_provider_community_index where service_provider_id = #{serviceProviderId}
    </delete>

    <select id="getListIdByServiceId" parameterType="Long" resultType="String">
        select GROUP_CONCAT(community_id) as ids from t_home_service_provider_community_index where service_provider_id
        = #{baseId}
    </select>

</mapper>
