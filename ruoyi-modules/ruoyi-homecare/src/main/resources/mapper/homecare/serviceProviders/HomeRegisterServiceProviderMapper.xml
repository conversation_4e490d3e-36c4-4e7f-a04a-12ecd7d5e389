<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.serviceProviders.mapper.HomeRegisterServiceProviderMapper">

    <resultMap type="HomeRegisterServiceProvider" id="HomeRegisterServiceProviderResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="nature" column="nature"/>
        <result property="userName" column="user_name"/>
        <result property="password" column="password"/>
        <result property="principal" column="principal"/>
        <result property="phone" column="phone"/>
        <result property="address" column="address"/>
        <result property="serviceState" column="service_state"/>
        <result property="holidaySeason" column="holiday_season"/>
        <result property="weekendFlag" column="weekend_flag"/>
        <result property="businessHours" column="business_hours"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="fixedTelephone" column="fixed_telephone"/>
        <result property="serviceType" column="service_type"/>
        <result property="label" column="label"/>
        <result property="businessLicense" column="business_license"/>
        <result property="merchantsPhotos" column="merchants_photos"/>
        <result property="contractAttachment" column="contract_attachment"/>
        <result property="communityId" column="community_id"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditCause" column="audit_cause"/>
        <result property="state" column="state"/>
        <result property="selectState" column="select_state"/>
        <result property="registerClient" column="register_client"/>
        <result property="flag" column="flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeRegisterServiceProviderVo">
        select id, name, nature, user_name, password, principal, phone, address, service_state,
        holiday_season, weekend_flag, business_hours, sys_user_id, fixed_telephone, service_type,
        label, business_license, merchants_photos, contract_attachment, community_id, audit_time,
        audit_cause, state, select_state, register_client, flag, create_time, create_by, update_time,
        update_by, del_flag, remark
        from t_home_register_service_provider
    </sql>

    <select id="selectHomeRegisterServiceProviderList" parameterType="HomeRegisterServiceProvider"
            resultMap="HomeRegisterServiceProviderResult">
        <include refid="selectHomeRegisterServiceProviderVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="nature != null  and nature != ''">and nature = #{nature}</if>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="password != null  and password != ''">and password = #{password}</if>
            <if test="principal != null  and principal != ''">and principal = #{principal}</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="serviceState != null  and serviceState != ''">and service_state = #{serviceState}</if>
            <if test="holidaySeason != null  and holidaySeason != ''">and holiday_season = #{holidaySeason}</if>
            <if test="weekendFlag != null  and weekendFlag != ''">and weekend_flag = #{weekendFlag}</if>
            <if test="businessHours != null  and businessHours != ''">and business_hours = #{businessHours}</if>
            <if test="sysUserId != null ">and sys_user_id = #{sysUserId}</if>
            <if test="fixedTelephone != null  and fixedTelephone != ''">and fixed_telephone = #{fixedTelephone}</if>
            <if test="serviceType != null  and serviceType != ''">and service_type = #{serviceType}</if>
            <if test="label != null  and label != ''">and label = #{label}</if>
            <if test="businessLicense != null  and businessLicense != ''">and business_license = #{businessLicense}</if>
            <if test="merchantsPhotos != null  and merchantsPhotos != ''">and merchants_photos = #{merchantsPhotos}</if>
            <if test="contractAttachment != null  and contractAttachment != ''">and contract_attachment =
                #{contractAttachment}
            </if>
            <if test="communityId != null  and communityId != ''">and community_id = #{communityId}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="auditCause != null  and auditCause != ''">and audit_cause = #{auditCause}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
            <if test="selectState != null  and selectState != ''">and select_state = #{selectState}</if>
            <if test="registerClient != null  and registerClient != ''">and register_client = #{registerClient}</if>
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHomeRegisterServiceProviderById" parameterType="Long"
            resultMap="HomeRegisterServiceProviderResult">
        <include refid="selectHomeRegisterServiceProviderVo"/>
        where id = #{id}
    </select>


    <select id="getServiceProvidersInfoBySysUserId" parameterType="Long" resultMap="HomeRegisterServiceProviderResult">
        <include refid="selectHomeRegisterServiceProviderVo"/>
        where sys_user_id = #{sysUserId} order by create_time desc limit 1
    </select>

    <insert id="insertHomeRegisterServiceProvider" parameterType="HomeRegisterServiceProvider" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_home_register_service_provider
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="nature != null">nature,</if>
            <if test="userName != null">user_name,</if>
            <if test="password != null">password,</if>
            <if test="principal != null">principal,</if>
            <if test="phone != null">phone,</if>
            <if test="address != null">address,</if>
            <if test="serviceState != null">service_state,</if>
            <if test="holidaySeason != null">holiday_season,</if>
            <if test="weekendFlag != null">weekend_flag,</if>
            <if test="businessHours != null">business_hours,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="fixedTelephone != null">fixed_telephone,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="label != null">label,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="merchantsPhotos != null">merchants_photos,</if>
            <if test="contractAttachment != null">contract_attachment,</if>
            <if test="communityId != null">community_id,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditCause != null">audit_cause,</if>
            <if test="state != null">state,</if>
            <if test="selectState != null">select_state,</if>
            <if test="registerClient != null">register_client,</if>
            <if test="flag != null">flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="nature != null">#{nature},</if>
            <if test="userName != null">#{userName},</if>
            <if test="password != null">#{password},</if>
            <if test="principal != null">#{principal},</if>
            <if test="phone != null">#{phone},</if>
            <if test="address != null">#{address},</if>
            <if test="serviceState != null">#{serviceState},</if>
            <if test="holidaySeason != null">#{holidaySeason},</if>
            <if test="weekendFlag != null">#{weekendFlag},</if>
            <if test="businessHours != null">#{businessHours},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="fixedTelephone != null">#{fixedTelephone},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="label != null">#{label},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="merchantsPhotos != null">#{merchantsPhotos},</if>
            <if test="contractAttachment != null">#{contractAttachment},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditCause != null">#{auditCause},</if>
            <if test="state != null">#{state},</if>
            <if test="selectState != null">#{selectState},</if>
            <if test="registerClient != null">#{registerClient},</if>
            <if test="flag != null">#{flag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeRegisterServiceProvider" parameterType="HomeRegisterServiceProvider">
        update t_home_register_service_provider
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="password != null">password = #{password},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="serviceState != null">service_state = #{serviceState},</if>
            <if test="holidaySeason != null">holiday_season = #{holidaySeason},</if>
            <if test="weekendFlag != null">weekend_flag = #{weekendFlag},</if>
            <if test="businessHours != null">business_hours = #{businessHours},</if>
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="fixedTelephone != null">fixed_telephone = #{fixedTelephone},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="label != null">label = #{label},</if>
            <if test="businessLicense != null">business_license = #{businessLicense},</if>
            <if test="merchantsPhotos != null">merchants_photos = #{merchantsPhotos},</if>
            <if test="contractAttachment != null">contract_attachment = #{contractAttachment},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditCause != null">audit_cause = #{auditCause},</if>
            <if test="state != null">state = #{state},</if>
            <if test="selectState != null">select_state = #{selectState},</if>
            <if test="registerClient != null">register_client = #{registerClient},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeRegisterServiceProviderById" parameterType="Long">
        delete from t_home_register_service_provider where id = #{id}
    </delete>

    <delete id="deleteHomeRegisterServiceProviderByIds" parameterType="String">
        delete from t_home_register_service_provider where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="updateBySysUserId" parameterType="Long">
        update t_home_register_service_provider set select_state = '1' where sys_user_id = #{sysUserId}
    </update>
</mapper>
