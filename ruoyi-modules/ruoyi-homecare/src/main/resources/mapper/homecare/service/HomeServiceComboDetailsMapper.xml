<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.service.mapper.HomeServiceComboDetailsMapper">


    <select id="selectHomeServiceComboDetailsList"
            parameterType="com.ruoyi.homecare.service.domain.HomeServiceComboDetails"
            resultType="com.ruoyi.homecare.service.domain.HomeServiceComboDetails">
        select a.* ,b.service_project from t_Home_Service_Combo_Details a
        LEFT JOIN t_home_service_project b on a.service_id=b.id
        <where>
            a.del_flag = '0'
            <if test="serviceProviderId != null  and serviceProviderId != ''">and a.service_provider_id =
                #{serviceProviderId}
            </if>
            <if test="serviceName != null  and serviceName != ''">and b.service_project like concat('%', #{serviceName},
                '%')
            </if>
            <if test="comboId != null  and comboId != ''">and a.combo_id = #{comboId}</if>

        </where>
    </select>

</mapper>
