<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.service.mapper.HomeServiceProviderWorkerMapper">

    <select id="selectWorkerList" parameterType="HomeServiceProviderWorker" resultType="HomeServiceProviderWorker">
        select
        t.id,
        t.name,
        t.age,
        t.birthday,
        t.ethnic,
        t.service_times,
        t.phone,
        t.service_provider_id,
        b.name as serviceProviderName
        from t_home_service_provider_worker t
        left join t_home_service_provider_management as b on t.service_provider_id = b.id
        <where>
            t.del_flag = '0' and b.del_flag='0'
            <if test="serviceProviderId != null  and serviceProviderId != ''">and t.service_provider_id =
                #{serviceProviderId}
            </if>
            <if test="name != null  and name != ''">and t.name like concat('%', #{name}, '%')</if>
        </where>
        order by t.create_time desc
    </select>

</mapper>
