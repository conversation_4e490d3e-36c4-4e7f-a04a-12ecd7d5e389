<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.service.mapper.HomeServiceComboInfoMapper">


    <sql id="selectHomeServiceComboInfoVo">
        SELECT
        a.*,b.name serviceProviderName
        FROM
        t_home_service_combo_info as a left join t_home_service_provider_management as b on a.service_provider_id = b.id
    </sql>

    <select id="selectComboList" parameterType="com.ruoyi.homecare.service.domain.HomeServiceComboInfo"
            resultType="HomeServiceComboInfo">
        <include refid="selectHomeServiceComboInfoVo"/>
        <where>
            a.del_flag = '0' and b.del_flag=0
            <if test="comboName != null  and comboName != ''">and a.combo_name like concat('%', #{comboName}, '%')</if>
            <if test="serviceProviderId != null  and serviceProviderId != ''">and a.service_provider_id =
                #{serviceProviderId}
            </if>
            <if test="serviceProviderName != null  and serviceProviderName != ''">and b.name like concat('%',
                #{serviceProviderName}, '%')
            </if>
        </where>
    </select>

    <select id="selectHomeServiceComboInfoById" parameterType="Long" resultType="HomeServiceComboInfo">
        <include refid="selectHomeServiceComboInfoVo"/>
        where a.id = #{id}
    </select>

</mapper>
