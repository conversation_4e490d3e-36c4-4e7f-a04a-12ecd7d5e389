<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.service.mapper.HomeServiceProjectMapper">

    <resultMap type="com.ruoyi.homecare.service.domain.HomeServiceProject" id="HomeServiceProjectResult">
        <result property="id" column="id"/>
        <result property="serviceType" column="service_type"/>
        <result property="serviceTypeStr" column="service_type_str"/>
        <result property="serviceProject" column="service_project"/>
        <result property="price" column="price"/>
        <result property="chargeMode" column="charge_mode"/>
        <result property="commissionRate" column="commission_rate"/>
        <result property="projectImg" column="project_img"/>
        <result property="projectRemark" column="project_remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="sales" column="sales"/>
        <result property="status" column="status"/>
        <result property="serviceProviderId" column="service_provider_id"/>
    </resultMap>

    <sql id="selectHomeServiceProjectVo">
        SELECT
        a.id,
        a.service_type,
        p.type_name as service_type_str,
        a.service_project,
        a.price,
        a.sales,
        a.charge_mode,
        a.commission_rate,
        a.project_img,
        a.service_provider_id,
        b.name as serviceProviderName,
        a.project_remark,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark,
        a.status
        FROM
        t_home_service_project as a
        left join t_home_service_provider_management as b on a.service_provider_id = b.id
        left join t_home_service_provider_service_type as p on a.service_type=p.id
    </sql>

    <select id="selectHomeServiceProjectList" parameterType="com.ruoyi.homecare.service.domain.HomeServiceProject"
            resultMap="HomeServiceProjectResult">
        <include refid="selectHomeServiceProjectVo"/>
        <where>
            a.del_flag = '0' and b.del_flag='0' and b.service_type like CONCAT('%','service','%')
            <if test="serviceType != null  and serviceType != ''">and a.service_type = #{serviceType}</if>
            <if test="serviceProviderId != null  and serviceProviderId != ''">and a.service_provider_id =
                #{serviceProviderId}
            </if>
            <if test="serviceProject != null  and serviceProject != ''">and a.service_project like concat('%',
                #{serviceProject}, '%')
            </if>
            <if test="price != null  and price != ''">and a.price = #{price}</if>
            <if test="chargeMode != null  and chargeMode != ''">and a.charge_mode = #{chargeMode}</if>
            <if test="commissionRate != null  and commissionRate != ''">and a.commission_rate = #{commissionRate}</if>
            <if test="projectImg != null  and projectImg != ''">and a.project_img = #{projectImg}</if>
            <if test="projectRemark != null  and projectRemark != ''">and a.project_remark = #{projectRemark}</if>
            <if test="status != null  and status != ''">and a.status = #{status}</if>
        </where>
        order by a.service_provider_id, a.create_time DESC
    </select>

    <select id="selectHomeServiceProjectById" parameterType="Long" resultMap="HomeServiceProjectResult">
        <include refid="selectHomeServiceProjectVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertHomeServiceProject" parameterType="com.ruoyi.homecare.service.domain.HomeServiceProject"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_service_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceType != null">service_type,</if>
            <if test="serviceProject != null">service_project,</if>
            <if test="serviceProviderId != null">service_provider_id,</if>
            <if test="price != null">price,</if>
            <if test="chargeMode != null">charge_mode,</if>
            <if test="commissionRate != null">commission_rate,</if>
            <if test="projectImg != null">project_img,</if>
            <if test="projectRemark != null">project_remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="sales != null">sales,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceType != null">#{serviceType},</if>
            <if test="serviceProject != null">#{serviceProject},</if>
            <if test="serviceProviderId != null">#{serviceProviderId},</if>
            <if test="price != null">#{price},</if>
            <if test="chargeMode != null">#{chargeMode},</if>
            <if test="commissionRate != null">#{commissionRate},</if>
            <if test="projectImg != null">#{projectImg},</if>
            <if test="projectRemark != null">#{projectRemark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="sales != null">#{sales},</if>
        </trim>
    </insert>

    <update id="updateHomeServiceProject" parameterType="com.ruoyi.homecare.service.domain.HomeServiceProject">
        update t_home_service_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="serviceProject != null">service_project = #{serviceProject},</if>
            <if test="serviceProviderId != null">service_provider_id = #{serviceProviderId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="chargeMode != null">charge_mode = #{chargeMode},</if>
            <if test="commissionRate != null">commission_rate = #{commissionRate},</if>
            <if test="projectImg != null">project_img = #{projectImg},</if>
            <if test="projectRemark != null">project_remark = #{projectRemark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sales != null">sales = #{sales},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeServiceProjectById" parameterType="Long">
        delete from t_home_service_project where id = #{id}
    </delete>

    <delete id="deleteHomeServiceProjectByIds" parameterType="String">
        update t_home_service_project set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getProjectList" resultType="cn.hutool.json.JSONObject">
        select id as value,service_project as label from t_home_service_project
        where del_flag = '0'
        <if test="serviceProviderId != null and serviceProviderId!=''">
            and service_Provider_Id = #{serviceProviderId}
        </if>
    </select>
</mapper>
