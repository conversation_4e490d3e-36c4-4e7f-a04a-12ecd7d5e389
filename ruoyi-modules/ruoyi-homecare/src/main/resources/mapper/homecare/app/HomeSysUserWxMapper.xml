<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.app.mapper.HomeSysUserWxMapper">

    <resultMap type="HomeSysUserWx" id="HomeSysUserWxResult">
        <result property="openId" column="open_id"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginTokens" column="login_tokens"/>
    </resultMap>

    <sql id="selectHomeSysUserWxVo">
        select open_id, sys_user_id, login_tokens, create_by, create_date, update_by, update_date, del_flag from
        t_home_sys_user_wx
    </sql>

    <select id="selectHomeSysUserWxList" parameterType="HomeSysUserWx" resultMap="HomeSysUserWxResult">
        <include refid="selectHomeSysUserWxVo"/>
        <where>
            <if test="openId != null  and openId != ''">and open_id = #{openId}</if>
            <if test="sysUserId != null  and sysUserId != ''">and sys_user_id = #{sysUserId}</if>
            <if test="loginTokens != null  and loginTokens != ''">and login_tokens = #{loginTokens}</if>
            <if test="createDate != null ">and create_date = #{createDate}</if>
            <if test="updateDate != null ">and update_date = #{updateDate}</if>
        </where>
    </select>

    <select id="selectHomeSysUserWxByOpenId" parameterType="String" resultMap="HomeSysUserWxResult">
        <include refid="selectHomeSysUserWxVo"/>
        where open_id = #{openId}
    </select>

    <insert id="insertHomeSysUserWx" parameterType="HomeSysUserWx">
        insert into t_home_sys_user_wx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">open_id,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="loginTokens != null">login_tokens,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null">#{openId},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="loginTokens != null">#{loginTokens},</if>
        </trim>
    </insert>

    <update id="updateHomeSysUserWx" parameterType="HomeSysUserWx">
        update t_home_sys_user_wx
        <trim prefix="SET" suffixOverrides=",">
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="loginTokens != null">login_tokens = #{loginTokens},</if>
        </trim>
        where open_id = #{openId}
    </update>

    <delete id="deleteHomeSysUserWxByOpenId" parameterType="String">
        delete from t_home_sys_user_wx where open_id = #{openId}
    </delete>

    <delete id="deleteHomeSysUserWxBySysUserId" parameterType="Long">
        delete from t_home_sys_user_wx where sys_user_id = #{sysUserId}
    </delete>

    <delete id="deleteHomeSysUserWxByOpenIds" parameterType="String">
        delete from t_home_sys_user_wx where open_id in
        <foreach item="openId" collection="array" open="(" separator="," close=")">
            #{openId}
        </foreach>
    </delete>
    <select id="">
        SELECT
        b.name as workName,
        c.price as price,
        d.name as providerName,
        case when a.status = '1' then '未指定人员'
        when a.status = '2' then '未开始'
        when a.status = '3' then '服务中'
        else '已完成' end as statusLabel,
        a.*
        FROM
        t_home_order_service_work AS a
        LEFT JOIN t_home_service_provider_worker as b on a.worker_id = b.id
        left join t_home_service_project as c on a.service_id = c.id
        left join t_home_service_provider_management as d on d.id = a.service_provider_id
        <where>
            <if test="workName != '' and workName != null ">
                and b.name like concat('%',#{workName},'%')
            </if>
            <if test="beginTime != null and endTime != null ">
                and DATE_FORMAT(a.start_time,'%Y-%m-%d') between DATE_FORMAT(beginTime,'%Y-%m-%d') and
                DATE_FORMAT(endTime,'%Y-%m-%d')
            </if>
        </where>
        order by a.create_time desc
    </select>

</mapper>
