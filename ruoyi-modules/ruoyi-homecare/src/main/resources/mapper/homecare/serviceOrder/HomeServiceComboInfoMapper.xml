<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.serviceOrder.mapper.HomeOrderServiceComboInfoMapper">


    <select id="getSoldComboList" resultType="HomeOrderServiceComboInfo">
        SELECT
        combo.*
        FROM
        t_home_order_base_info base
        LEFT JOIN t_home_order_service_combo_info combo ON base.id = combo.order_id
        <where>
            base.type = 4
            <if test="serviceProviderId!=null and serviceProviderId!=''">
                AND base.provider_id = #{serviceProviderId}
            </if>
        </where>
    </select>


</mapper>
