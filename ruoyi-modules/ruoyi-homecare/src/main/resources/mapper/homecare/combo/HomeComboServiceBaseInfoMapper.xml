<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.combo.mapper.HomeComboServiceBaseInfoMapper">

    <resultMap type="com.ruoyi.homecare.combo.domain.HomeComboServiceBaseInfo" id="HomeComboServiceBaseInfoResult">
        <result property="id" column="id"/>
        <result property="comboId" column="combo_id"/>
        <result property="projectId" column="project_id"/>
        <result property="cycle" column="cycle"/>
        <result property="frequency" column="frequency"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="projectName" column="projectName"/>
        <result property="merchantName" column="merchantName"/>
    </resultMap>

    <sql id="selectHomeComboServiceBaseInfoVo">
        SELECT
        a.id,
        a.combo_id,
        a.project_id,
        c.service_project as projectName,
        a.cycle,
        a.frequency,
        a.merchant_id,
        b.name as merchantName,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_home_combo_service_base_info as a left join t_home_service_provider_management as b on a.merchant_id = b.id
        left join t_home_service_project as c on c.id = a.project_id
    </sql>

    <select id="selectHomeComboServiceBaseInfoList"
            parameterType="com.ruoyi.homecare.combo.domain.HomeComboServiceBaseInfo"
            resultMap="HomeComboServiceBaseInfoResult">
        <include refid="selectHomeComboServiceBaseInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="comboId != null ">and a.combo_id = #{comboId}</if>
            <if test="projectName != null and projectName != '' ">and c.service_project like concat('%', #{projectName},
                '%')
            </if>
            <if test="cycle != null  and cycle != ''">and a.cycle = #{cycle}</if>
            <if test="frequency != null ">and a.frequency = #{frequency}</if>
            <if test="merchantId != null ">and a.merchant_id = #{merchantId}</if>
        </where>
    </select>

    <select id="selectHomeComboServiceBaseInfoById" parameterType="Long" resultMap="HomeComboServiceBaseInfoResult">
        <include refid="selectHomeComboServiceBaseInfoVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertHomeComboServiceBaseInfo" parameterType="com.ruoyi.homecare.combo.domain.HomeComboServiceBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_combo_service_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="comboId != null">combo_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="cycle != null">cycle,</if>
            <if test="frequency != null">frequency,</if>
            <if test="merchantId != null">merchant_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="comboId != null">#{comboId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="cycle != null">#{cycle},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeComboServiceBaseInfo"
            parameterType="com.ruoyi.homecare.combo.domain.HomeComboServiceBaseInfo">
        update t_home_combo_service_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="comboId != null">combo_id = #{comboId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeComboServiceBaseInfoById" parameterType="Long">
        delete from t_home_combo_service_base_info where id = #{id}
    </delete>

    <delete id="deleteHomeComboServiceBaseInfoByIds" parameterType="String">
        update t_home_combo_service_base_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
