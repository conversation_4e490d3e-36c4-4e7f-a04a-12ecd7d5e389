<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.combo.mapper.HomeServiceRemindMapper">

    <resultMap type="com.ruoyi.homecare.combo.domain.HomeServiceRemind" id="HomeServiceRemindResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="projectId" column="project_id"/>
        <result property="providerId" column="provider_id"/>
        <result property="frequency" column="frequency"/>
        <result property="cycle" column="cycle"/>
        <result property="beginTime" column="begin_time"/>
        <result property="shouldEndTime" column="should_end_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="userName" column="userName"/>
        <result property="providerName" column="providerName"/>
        <result property="projectName" column="projectName"/>
    </resultMap>

    <sql id="selectHomeServiceRemindVo">
        SELECT
        a.id,
        a.user_id,
        b.`name` as userName,
        c.`name` as providerName,
        d.service_project as projectName,
        a.project_id,
        a.provider_id,
        a.frequency,
        a.cycle,
        a.begin_time,
        a.should_end_time,
        CONCAT('建议 ',a.should_end_time,' 进行护理服务') as flag,
        a.actual_end_time,
        a.state,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_home_service_remind AS a
        LEFT JOIN t_home_elderly_people_info AS b ON a.user_id = b.id
        LEFT JOIN t_home_service_provider_management AS c ON a.provider_id = c.id
        LEFT JOIN t_home_service_project AS d ON d.id = a.project_id
    </sql>

    <select id="selectHomeServiceRemindList" parameterType="com.ruoyi.homecare.combo.domain.HomeServiceRemind"
            resultMap="HomeServiceRemindResult">
        <include refid="selectHomeServiceRemindVo"/>
        <where>
            a.del_flag = '0'
            <if test="userId != null  and userId != ''">and a.user_id = #{userId}</if>
            <if test="userName != null  and userName != ''">and b.name like concat('%',#{userName},'%')</if>
            <if test="providerName != null  and providerName != ''">and c.name like concat('%',#{providerName},'%')</if>
            <if test="projectName != null  and projectName != ''">and d.service_project like
                concat('%',#{projectName},'%')
            </if>
            <if test="projectId != null ">and a.project_id = #{projectId}</if>
            <if test="providerId != null ">and a.provider_id = #{providerId}</if>
            <if test="frequency != null  and frequency != ''">and a.frequency = #{frequency}</if>
            <if test="cycle != null  and cycle != ''">and a.cycle = #{cycle}</if>
            <if test="beginTime != null ">and a.begin_time = #{beginTime}</if>
            <if test="shouldEndTime != null ">and a.should_end_time = #{shouldEndTime}</if>
            <if test="actualEndTime != null ">and a.actual_end_time = #{actualEndTime}</if>
            <if test="state != null  and state != ''">and a.state = #{state}</if>
        </where>
    </select>

    <select id="selectHomeServiceRemindById" parameterType="Long" resultMap="HomeServiceRemindResult">
        <include refid="selectHomeServiceRemindVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertHomeServiceRemind" parameterType="com.ruoyi.homecare.combo.domain.HomeServiceRemind"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_service_remind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="providerId != null">provider_id,</if>
            <if test="frequency != null">frequency,</if>
            <if test="cycle != null">cycle,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="shouldEndTime != null">should_end_time,</if>
            <if test="actualEndTime != null">actual_end_time,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="providerId != null">#{providerId},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="cycle != null">#{cycle},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="shouldEndTime != null">#{shouldEndTime},</if>
            <if test="actualEndTime != null">#{actualEndTime},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeServiceRemind" parameterType="com.ruoyi.homecare.combo.domain.HomeServiceRemind">
        update t_home_service_remind
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="providerId != null">provider_id = #{providerId},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="shouldEndTime != null">should_end_time = #{shouldEndTime},</if>
            <if test="actualEndTime != null">actual_end_time = #{actualEndTime},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeServiceRemindById" parameterType="Long">
        delete from t_home_service_remind where id = #{id}
    </delete>

    <delete id="deleteHomeServiceRemindByIds" parameterType="String">
        update t_home_service_remind set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
