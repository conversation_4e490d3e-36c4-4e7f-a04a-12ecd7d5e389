<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.combo.mapper.HomeComboBaseInfoMapper">

    <resultMap type="com.ruoyi.homecare.combo.domain.HomeComboBaseInfo" id="HomeComboBaseInfoResult">
        <result property="id" column="id"/>
        <result property="level" column="level"/>
        <result property="comboName" column="combo_name"/>
        <result property="servicePeriod" column="service_period"/>
        <result property="period" column="period"/>
        <result property="amount" column="amount"/>
        <result property="introduce" column="introduce"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeComboBaseInfoVo">
        select id, level, combo_name, service_period, period, amount, introduce, state, create_time, create_by,
        update_time, update_by, del_flag, remark from t_home_combo_base_info
    </sql>

    <select id="selectHomeComboBaseInfoList" parameterType="com.ruoyi.homecare.combo.domain.HomeComboBaseInfo"
            resultMap="HomeComboBaseInfoResult">
        <include refid="selectHomeComboBaseInfoVo"/>
        <where>
            del_flag = '0'
            <if test="level != null  and level != ''">and level = #{level}</if>
            <if test="comboName != null  and comboName != ''">and combo_name like concat('%', #{comboName}, '%')</if>
            <if test="servicePeriod != null  and servicePeriod != ''">and service_period = #{servicePeriod}</if>
            <if test="params.beginPeriod != null and params.beginPeriod != '' and params.endPeriod != null and params.endPeriod != ''">
                and period between #{params.beginPeriod} and #{params.endPeriod}
            </if>
            <if test="params.beginAmount != null and params.beginAmount != '' and params.endAmount != null and params.endAmount != ''">
                and amount between #{params.beginAmount} and #{params.endAmount}
            </if>
            <if test="introduce != null  and introduce != ''">and introduce = #{introduce}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
        </where>
    </select>

    <select id="selectHomeComboBaseInfoById" parameterType="Long" resultMap="HomeComboBaseInfoResult">
        <include refid="selectHomeComboBaseInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeComboBaseInfo" parameterType="com.ruoyi.homecare.combo.domain.HomeComboBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_combo_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="level != null">level,</if>
            <if test="comboName != null">combo_name,</if>
            <if test="servicePeriod != null">service_period,</if>
            <if test="period != null">period,</if>
            <if test="amount != null">amount,</if>
            <if test="introduce != null">introduce,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="level != null">#{level},</if>
            <if test="comboName != null">#{comboName},</if>
            <if test="servicePeriod != null">#{servicePeriod},</if>
            <if test="period != null">#{period},</if>
            <if test="amount != null">#{amount},</if>
            <if test="introduce != null">#{introduce},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeComboBaseInfo" parameterType="com.ruoyi.homecare.combo.domain.HomeComboBaseInfo">
        update t_home_combo_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="level != null">level = #{level},</if>
            <if test="comboName != null">combo_name = #{comboName},</if>
            <if test="servicePeriod != null">service_period = #{servicePeriod},</if>
            <if test="period != null">period = #{period},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="introduce != null">introduce = #{introduce},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeComboBaseInfoById" parameterType="Long">
        delete from t_home_combo_base_info where id = #{id}
    </delete>

    <delete id="deleteHomeComboBaseInfoByIds" parameterType="String">
        update t_home_combo_base_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getComboList" resultType="cn.hutool.json.JSONObject" parameterType="String">
        select id as value,combo_name as label from t_home_combo_base_info
        <where>
            del_flag = '0'
            <if test="level != null and level != ''">
                and level = #{level}
            </if>
        </where>
    </select>

</mapper>
