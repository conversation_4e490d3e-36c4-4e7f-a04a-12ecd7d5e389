<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.settlement.mapper.HomePaymentUserFeesMapper">

    <resultMap type="com.ruoyi.homecare.settlement.domain.HomePaymentUserFees" id="HomePaymentUserFeesResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="methodPayment" column="method_payment"/>
        <result property="paymentTime" column="payment_time"/>
        <result property="oldMoney" column="old_money"/>
        <result property="paymentMoney" column="payment_money"/>
        <result property="newMoney" column="new_money"/>
        <result property="createTime" column="create_time"/>
        <result property="createByName" column="create_by_name"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="userName" column="userName"/>
        <result property="orderId" column="order_id"/>
        <result property="createClient" column="create_client"/>
    </resultMap>

    <sql id="selectHomePaymentUserFeesVo">

        SELECT
        a.id,
        a.user_id,
        b.name as userName,
        a.method_payment,
        a.payment_time,
        a.old_money,
        a.payment_money,
        a.new_money,
        a.create_time,
        a.create_by_name,
        a.create_by,
        a.update_time,
        a.update_by,
        a.order_id,
        a.create_client,
        a.del_flag,
        a.remark
        FROM
        t_home_payment_user_fees as a left join t_home_elderly_people_info as b on a.user_id = b.id
    </sql>

    <select id="selectHomePaymentUserFeesList" parameterType="com.ruoyi.homecare.settlement.domain.HomePaymentUserFees"
            resultMap="HomePaymentUserFeesResult">
        <include refid="selectHomePaymentUserFeesVo"/>
        <where>
            a.del_flag = '0'
            <if test="userId != null  and userId != ''">and a.user_id = #{userId}</if>
            <if test="methodPayment != null  and methodPayment != ''">and a.method_payment = #{methodPayment}</if>
            <if test="orderId != null  and orderId != ''">and a.order_id = #{orderId}</if>
            <if test="createClient != null  and createClient != ''">and a.create_client = #{createClient}</if>
            <if test="paymentTime != null ">and a.payment_time = #{paymentTime}</if>
            <if test="oldMoney != null ">and a.old_money = #{oldMoney}</if>
            <if test="paymentMoney != null ">and a.payment_money = #{paymentMoney}</if>
            <if test="newMoney != null ">and a.new_money = #{newMoney}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and date_format(a.create_time,'%Y-%d-%m') between date_format(#{params.beginCreateTime},'%Y-%d-%m') and
                date_format(#{params.endCreateTime},'%Y-%d-%m')
            </if>
            <if test="createByName != null  and createByName != ''">and a.create_by_name like concat('%',
                #{createByName}, '%')
            </if>
            <if test="userName != null  and userName != ''">and b.name like concat('%', #{userName}, '%')</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectHomePaymentUserFeesById" parameterType="Long" resultMap="HomePaymentUserFeesResult">
        <include refid="selectHomePaymentUserFeesVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertHomePaymentUserFees" parameterType="com.ruoyi.homecare.settlement.domain.HomePaymentUserFees"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_payment_user_fees
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="methodPayment != null">method_payment,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="oldMoney != null">old_money,</if>
            <if test="paymentMoney != null">payment_money,</if>
            <if test="newMoney != null">new_money,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createByName != null">create_by_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="orderId != null">order_id,</if>
            <if test="createClient != null">create_client,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="methodPayment != null">#{methodPayment},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="oldMoney != null">#{oldMoney},</if>
            <if test="paymentMoney != null">#{paymentMoney},</if>
            <if test="newMoney != null">#{newMoney},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createByName != null">#{createByName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="createClient != null">#{createClient},</if>
        </trim>
    </insert>

    <update id="updateHomePaymentUserFees" parameterType="com.ruoyi.homecare.settlement.domain.HomePaymentUserFees">
        update t_home_payment_user_fees
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="methodPayment != null">method_payment = #{methodPayment},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="oldMoney != null">old_money = #{oldMoney},</if>
            <if test="paymentMoney != null">payment_money = #{paymentMoney},</if>
            <if test="newMoney != null">new_money = #{newMoney},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createByName != null">create_by_name = #{createByName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="createClient != null">create_client = #{createClient},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomePaymentUserFeesById" parameterType="Long">
        delete from t_home_payment_user_fees where id = #{id}
    </delete>

    <delete id="deleteHomePaymentUserFeesByIds" parameterType="String">
        update t_home_payment_user_fees set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
