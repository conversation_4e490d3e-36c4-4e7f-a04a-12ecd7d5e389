<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.settlement.mapper.HomeAmountChangeRecordMapper">

    <resultMap type="com.ruoyi.homecare.settlement.domain.HomeAmountChangeRecord" id="HomeAmountChangeRecordResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="type" column="type"/>
        <result property="lastAmount" column="last_amount"/>
        <result property="changedAmount" column="changed_amount"/>
        <result property="amount" column="amount"/>
        <result property="originalNumber" column="original_number"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeAmountChangeRecordVo">
        select id, user_id, type, last_amount, changed_amount, amount, original_number, create_time, create_by,
        update_time, update_by, del_flag, remark from t_home_record_amount_change
    </sql>

    <select id="selectHomeAmountChangeRecordList"
            parameterType="com.ruoyi.homecare.settlement.domain.HomeAmountChangeRecord"
            resultMap="HomeAmountChangeRecordResult">
        <include refid="selectHomeAmountChangeRecordVo"/>
        <where>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="lastAmount != null ">and last_amount = #{lastAmount}</if>
            <if test="changedAmount != null ">and changed_amount = #{changedAmount}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="originalNumber != null  and originalNumber != ''">and original_number = #{originalNumber}</if>
        </where>
    </select>

    <select id="selectHomeAmountChangeRecordById" parameterType="Long" resultMap="HomeAmountChangeRecordResult">
        <include refid="selectHomeAmountChangeRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeAmountChangeRecord"
            parameterType="com.ruoyi.homecare.settlement.domain.HomeAmountChangeRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_home_record_amount_change
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="type != null">type,</if>
            <if test="lastAmount != null">last_amount,</if>
            <if test="changedAmount != null">changed_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="originalNumber != null">original_number,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="type != null">#{type},</if>
            <if test="lastAmount != null">#{lastAmount},</if>
            <if test="changedAmount != null">#{changedAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="originalNumber != null">#{originalNumber},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeAmountChangeRecord"
            parameterType="com.ruoyi.homecare.settlement.domain.HomeAmountChangeRecord">
        update t_home_record_amount_change
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="lastAmount != null">last_amount = #{lastAmount},</if>
            <if test="changedAmount != null">changed_amount = #{changedAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="originalNumber != null">original_number = #{originalNumber},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeAmountChangeRecordById" parameterType="Long">
        delete from t_home_record_amount_change where id = #{id}
    </delete>

    <delete id="deleteHomeAmountChangeRecordByIds" parameterType="String">
        delete from t_home_record_amount_change where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
