<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.food.mapper.HomeFoodServiceProviderIndexMapper">

    <resultMap type="com.ruoyi.homecare.food.domain.HomeFoodServiceProviderIndex"
               id="HomeFoodServiceProviderIndexResult">
        <result property="id" column="id"/>
        <result property="foodId" column="food_id"/>
        <result property="serviceProviderId" column="service_provider_id"/>
        <result property="marketingPrice" column="marketing_price"/>
        <result property="price" column="price"/>
        <result property="packing" column="packing"/>
        <result property="stock" column="stock"/>
        <result property="freight" column="freight"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeFoodServiceProviderIndexVo">
        select id, food_id, service_provider_id, marketing_price, price, packing, stock, freight, create_time,
        create_by, update_time, update_by, del_flag, remark from t_home_food_service_provider_index
    </sql>

    <select id="selectHomeFoodServiceProviderIndexList"
            parameterType="com.ruoyi.homecare.food.domain.HomeFoodServiceProviderIndex"
            resultMap="HomeFoodServiceProviderIndexResult">
        <include refid="selectHomeFoodServiceProviderIndexVo"/>
        <where>
            del_flag = '0'
            <if test="foodId != null ">and food_id = #{foodId}</if>
            <if test="serviceProviderId != null ">and service_provider_id = #{serviceProviderId}</if>
            <if test="marketingPrice != null ">and marketing_price = #{marketingPrice}</if>
            <if test="params.beginPrice != null and params.beginPrice != '' and params.endPrice != null and params.endPrice != ''">
                and price between #{params.beginPrice} and #{params.endPrice}
            </if>
            <if test="packing != null ">and packing = #{packing}</if>
            <if test="stock != null ">and stock = #{stock}</if>
            <if test="freight != null ">and freight = #{freight}</if>
        </where>
    </select>

    <select id="selectHomeFoodServiceProviderIndexById" parameterType="Long"
            resultMap="HomeFoodServiceProviderIndexResult">
        <include refid="selectHomeFoodServiceProviderIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeFoodServiceProviderIndex"
            parameterType="com.ruoyi.homecare.food.domain.HomeFoodServiceProviderIndex" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_home_food_service_provider_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="foodId != null">food_id,</if>
            <if test="serviceProviderId != null">service_provider_id,</if>
            <if test="marketingPrice != null">marketing_price,</if>
            <if test="price != null">price,</if>
            <if test="packing != null">packing,</if>
            <if test="stock != null">stock,</if>
            <if test="freight != null">freight,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="foodId != null">#{foodId},</if>
            <if test="serviceProviderId != null">#{serviceProviderId},</if>
            <if test="marketingPrice != null">#{marketingPrice},</if>
            <if test="price != null">#{price},</if>
            <if test="packing != null">#{packing},</if>
            <if test="stock != null">#{stock},</if>
            <if test="freight != null">#{freight},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeFoodServiceProviderIndex"
            parameterType="com.ruoyi.homecare.food.domain.HomeFoodServiceProviderIndex">
        update t_home_food_service_provider_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="foodId != null">food_id = #{foodId},</if>
            <if test="serviceProviderId != null">service_provider_id = #{serviceProviderId},</if>
            <if test="marketingPrice != null">marketing_price = #{marketingPrice},</if>
            <if test="price != null">price = #{price},</if>
            <if test="packing != null">packing = #{packing},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="freight != null">freight = #{freight},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeFoodServiceProviderIndexById" parameterType="Long">
        delete from t_home_food_service_provider_index where id = #{id}
    </delete>

    <delete id="deleteHomeFoodServiceProviderIndexByIds" parameterType="String">
        update t_home_food_service_provider_index set del_flag = '0' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
