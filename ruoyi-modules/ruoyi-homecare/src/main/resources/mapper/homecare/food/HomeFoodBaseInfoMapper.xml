<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.food.mapper.HomeFoodBaseInfoMapper">

    <resultMap type="com.ruoyi.homecare.food.domain.HomeFoodBaseInfo" id="HomeFoodBaseInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="price" column="price"/>
        <result property="tabooCrow" column="taboo_crow"/>
        <result property="pungencyDegree" column="pungency_degree"/>
        <result property="ingredient" column="ingredient"/>
        <result property="img" column="img"/>
        <result property="productsThat" column="products_that"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeFoodBaseInfoVo">
        select id, name, price, taboo_crow, pungency_degree, ingredient, img, products_that, merchant_id, create_time,
        create_by, update_time, update_by, del_flag, remark from t_home_food_base_info
    </sql>

    <select id="selectHomeFoodBaseInfoList" parameterType="com.ruoyi.homecare.food.domain.HomeFoodBaseInfo"
            resultMap="HomeFoodBaseInfoResult">
        <include refid="selectHomeFoodBaseInfoVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="params.beginPrice != null and params.beginPrice != '' and params.endPrice != null and params.endPrice != ''">
                and price between #{params.beginPrice} and #{params.endPrice}
            </if>
            <if test="tabooCrow != null  and tabooCrow != ''">and taboo_crow like concat('%', #{tabooCrow}, '%')</if>
            <if test="pungencyDegree != null  and pungencyDegree != ''">and pungency_degree = #{pungencyDegree}</if>
            <if test="ingredient != null  and ingredient != ''">and ingredient like concat('%', #{ingredient}, '%')</if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="productsThat != null  and productsThat != ''">and products_that = #{productsThat}</if>
            <if test="merchantId != null ">and merchant_id = #{merchantId}</if>
        </where>
    </select>

    <select id="selectHomeFoodBaseInfoById" parameterType="Long" resultMap="HomeFoodBaseInfoResult">
        <include refid="selectHomeFoodBaseInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeFoodBaseInfo" parameterType="com.ruoyi.homecare.food.domain.HomeFoodBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_food_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="price != null">price,</if>
            <if test="tabooCrow != null">taboo_crow,</if>
            <if test="pungencyDegree != null">pungency_degree,</if>
            <if test="ingredient != null">ingredient,</if>
            <if test="img != null">img,</if>
            <if test="productsThat != null">products_that,</if>
            <if test="merchantId != null">merchant_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="price != null">#{price},</if>
            <if test="tabooCrow != null">#{tabooCrow},</if>
            <if test="pungencyDegree != null">#{pungencyDegree},</if>
            <if test="ingredient != null">#{ingredient},</if>
            <if test="img != null">#{img},</if>
            <if test="productsThat != null">#{productsThat},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeFoodBaseInfo" parameterType="com.ruoyi.homecare.food.domain.HomeFoodBaseInfo">
        update t_home_food_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="price != null">price = #{price},</if>
            <if test="tabooCrow != null">taboo_crow = #{tabooCrow},</if>
            <if test="pungencyDegree != null">pungency_degree = #{pungencyDegree},</if>
            <if test="ingredient != null">ingredient = #{ingredient},</if>
            <if test="img != null">img = #{img},</if>
            <if test="productsThat != null">products_that = #{productsThat},</if>
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeFoodBaseInfoById" parameterType="Long">
        delete from t_home_food_base_info where id = #{id}
    </delete>

    <delete id="deleteHomeFoodBaseInfoByIds" parameterType="String">
        update t_home_food_base_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
