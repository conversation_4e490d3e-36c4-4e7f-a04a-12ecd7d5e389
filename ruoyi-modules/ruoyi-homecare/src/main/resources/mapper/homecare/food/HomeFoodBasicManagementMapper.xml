<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.food.mapper.HomeFoodBasicManagementMapper">

    <resultMap type="com.ruoyi.homecare.food.domain.HomeFoodBasicManagement" id="HomeFoodBasicManagementResult">
        <result property="id" column="id"/>
        <result property="packing" column="packing"/>
        <result property="shippingFee" column="shipping_fee"/>
        <result property="refundTime" column="refund_time"/>
        <result property="refund" column="refund"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeFoodBasicManagementVo">
        select id, packing, shipping_fee, refund_time, refund, create_time, create_by, update_time, update_by, del_flag,
        remark from t_home_food_basic_management
    </sql>

    <select id="selectHomeFoodBasicManagementList"
            parameterType="com.ruoyi.homecare.food.domain.HomeFoodBasicManagement"
            resultMap="HomeFoodBasicManagementResult">
        <include refid="selectHomeFoodBasicManagementVo"/>
        <where>
            del_flag = '0'
            <if test="packing != null ">and packing = #{packing}</if>
            <if test="shippingFee != null ">and shipping_fee = #{shippingFee}</if>
            <if test="refundTime != null  and refundTime != ''">and refund_time = #{refundTime}</if>
            <if test="refund != null  and refund != ''">and refund = #{refund}</if>
        </where>
    </select>

    <select id="selectHomeFoodBasicManagementById" parameterType="Long" resultMap="HomeFoodBasicManagementResult">
        <include refid="selectHomeFoodBasicManagementVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeFoodBasicManagement" parameterType="com.ruoyi.homecare.food.domain.HomeFoodBasicManagement"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_food_basic_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packing != null">packing,</if>
            <if test="shippingFee != null">shipping_fee,</if>
            <if test="refundTime != null">refund_time,</if>
            <if test="refund != null">refund,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packing != null">#{packing},</if>
            <if test="shippingFee != null">#{shippingFee},</if>
            <if test="refundTime != null">#{refundTime},</if>
            <if test="refund != null">#{refund},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeFoodBasicManagement" parameterType="com.ruoyi.homecare.food.domain.HomeFoodBasicManagement">
        update t_home_food_basic_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="packing != null">packing = #{packing},</if>
            <if test="shippingFee != null">shipping_fee = #{shippingFee},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="refund != null">refund = #{refund},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeFoodBasicManagementById" parameterType="Long">
        delete from t_home_food_basic_management where id = #{id}
    </delete>

    <delete id="deleteHomeFoodBasicManagementByIds" parameterType="String">
        update t_home_food_basic_management set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
