<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.settings.mapper.HomeLabelBaseMapper">

    <resultMap type="com.ruoyi.homecare.settings.domain.HomeLabelBase" id="HomeLabelBaseResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeLabelBaseVo">
        select id, name, type, create_time, create_by, update_time, update_by, del_flag, remark from t_home_label_base
    </sql>

    <select id="selectHomeLabelBaseList" parameterType="com.ruoyi.homecare.settings.domain.HomeLabelBase"
            resultMap="HomeLabelBaseResult">
        <include refid="selectHomeLabelBaseVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
        </where>
    </select>

    <select id="selectHomeLabelBaseById" parameterType="String" resultMap="HomeLabelBaseResult">
        <include refid="selectHomeLabelBaseVo"/>
        where id = #{id}
    </select>

    <select id="checkLabelUnique" parameterType="String" resultMap="HomeLabelBaseResult">
        <include refid="selectHomeLabelBaseVo"/>
        where del_flag = '0' and name = #{name} limit 1
    </select>

    <insert id="insertHomeLabelBase" parameterType="com.ruoyi.homecare.settings.domain.HomeLabelBase">
        insert into t_home_label_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeLabelBase" parameterType="com.ruoyi.homecare.settings.domain.HomeLabelBase">
        update t_home_label_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeLabelBaseById" parameterType="String">
        update t_home_label_base set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteHomeLabelBaseByIds" parameterType="String">
        update t_home_label_base set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByTypeNum" parameterType="String" resultType="int">
        select count(id) from t_home_label_base where type = #{type}
    </select>

    <select id="getServiceProviderLabel" resultType="cn.hutool.json.JSONObject" parameterType="String">
        select name as label , id as value from t_home_label_base where type = #{type}
    </select>
</mapper>
