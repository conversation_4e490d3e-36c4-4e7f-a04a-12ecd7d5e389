<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.settings.mapper.HomeBaseSettingsInfoMapper">

    <resultMap type="com.ruoyi.homecare.settings.domain.HomeBaseSettingsInfo" id="HomeBaseSettingsInfoResult">
        <result property="id" column="id"/>
        <result property="packing" column="packing"/>
        <result property="shippingFee" column="shipping_fee"/>
        <result property="refundTime" column="refund_time"/>
        <result property="refund" column="refund"/>
        <result property="foodRatio" column="food_ratio"/>
        <result property="goodsRatio" column="goods_ratio"/>
        <result property="serviceRatio" column="service_ratio"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="customerServiceHotline" column="customer_service_hotline"/>
    </resultMap>

    <sql id="selectHomeBaseSettingsInfoVo">
        select id, packing, shipping_fee, refund_time, refund, food_ratio,customer_service_hotline, goods_ratio,
        service_ratio, create_time, create_by, update_time, update_by, del_flag, remark from t_home_base_settings_info
    </sql>

    <select id="selectHomeBaseSettingsInfoList" parameterType="com.ruoyi.homecare.settings.domain.HomeBaseSettingsInfo"
            resultMap="HomeBaseSettingsInfoResult">
        <include refid="selectHomeBaseSettingsInfoVo"/>
        <where>
            <if test="packing != null ">and packing = #{packing}</if>
            <if test="shippingFee != null ">and shipping_fee = #{shippingFee}</if>
            <if test="refundTime != null  and refundTime != ''">and refund_time = #{refundTime}</if>
            <if test="refund != null  and refund != ''">and refund = #{refund}</if>
            <if test="foodRatio != null  and foodRatio != ''">and food_ratio = #{foodRatio}</if>
            <if test="goodsRatio != null  and goodsRatio != ''">and goods_ratio = #{goodsRatio}</if>
            <if test="serviceRatio != null  and serviceRatio != ''">and service_ratio = #{serviceRatio}</if>
        </where>
    </select>

    <select id="selectHomeBaseSettingsInfoById" parameterType="Long" resultMap="HomeBaseSettingsInfoResult">
        <include refid="selectHomeBaseSettingsInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeBaseSettingsInfo" parameterType="com.ruoyi.homecare.settings.domain.HomeBaseSettingsInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_base_settings_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="packing != null">packing,</if>
            <if test="shippingFee != null">shipping_fee,</if>
            <if test="refundTime != null">refund_time,</if>
            <if test="refund != null">refund,</if>
            <if test="foodRatio != null">food_ratio,</if>
            <if test="goodsRatio != null">goods_ratio,</if>
            <if test="serviceRatio != null">service_ratio,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="customerServiceHotline != null">customer_service_hotline,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="packing != null">#{packing},</if>
            <if test="shippingFee != null">#{shippingFee},</if>
            <if test="refundTime != null">#{refundTime},</if>
            <if test="refund != null">#{refund},</if>
            <if test="foodRatio != null">#{foodRatio},</if>
            <if test="goodsRatio != null">#{goodsRatio},</if>
            <if test="serviceRatio != null">#{serviceRatio},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="customerServiceHotline != null">#{customerServiceHotline},</if>
        </trim>
    </insert>

    <update id="updateHomeBaseSettingsInfo" parameterType="com.ruoyi.homecare.settings.domain.HomeBaseSettingsInfo">
        update t_home_base_settings_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="packing != null">packing = #{packing},</if>
            <if test="shippingFee != null">shipping_fee = #{shippingFee},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="refund != null">refund = #{refund},</if>
            <if test="foodRatio != null">food_ratio = #{foodRatio},</if>
            <if test="goodsRatio != null">goods_ratio = #{goodsRatio},</if>
            <if test="serviceRatio != null">service_ratio = #{serviceRatio},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="customerServiceHotline != null">customer_service_hotline = #{customerServiceHotline},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeBaseSettingsInfoById" parameterType="Long">
        delete from t_home_base_settings_info where id = #{id}
    </delete>

    <delete id="deleteHomeBaseSettingsInfoByIds" parameterType="String">
        delete from t_home_base_settings_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
