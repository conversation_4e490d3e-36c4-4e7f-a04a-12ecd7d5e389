<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.merchantSettlement.mapper.MerchantSettlementMapper">

    <select id="getOrderList" parameterType="com.ruoyi.homecare.merchantSettlement.vo.MerchantSettlementListReqVo"
            resultType="com.ruoyi.homecare.merchantSettlement.vo.MSOrderInfoResVo">
        select t.* ,(t.total_fee-t.commission_amount) as commissionAmount, m.name as providerName,
        r.audit_status refundStatus, r.amount as refundAmount
        from t_home_order_base_info t
        left join t_home_service_provider_management m on t.provider_id = m.id
        LEFT JOIN (select * from t_home_order_refund_info where audit_status=2) r on t.id=r.order_id
        <where>
            t.status>=2
            <if test="serviceProviderId != null  and serviceProviderId != ''">and t.provider_Id = #{serviceProviderId}
            </if>
            <if test="queryMonth != null  and queryMonth != ''">and DATE_FORMAT(t.create_time, '%Y%m') = #{queryMonth}
            </if>
        </where>
    </select>


    <select id="selectServiceProviderMonthCountInfo"
            resultType="com.ruoyi.homecare.merchantSettlement.domain.MerchantSettlement">
        SELECT
        count(1) business_num,
        sum(t.total_price) business_amount,
        count(r.amount) refund_num,
        sum(r.amount) refund_amount,
        sum(CASE WHEN r.amount IS NULL THEN t.commission_amount else 0 END )AS commission_amount
        FROM
        t_home_order_base_info t
        LEFT JOIN ( SELECT * FROM t_home_order_refund_info WHERE audit_status = 2 ) r ON t.id = r.order_id
        <where>
            t.status>=2
            <if test="serviceProviderId != null  and serviceProviderId != ''">and t.provider_Id = #{serviceProviderId}
            </if>
            <if test="queryMonth != null  and queryMonth != ''">and DATE_FORMAT(t.create_time, '%Y%m') = #{queryMonth}
            </if>
        </where>
    </select>


</mapper>
