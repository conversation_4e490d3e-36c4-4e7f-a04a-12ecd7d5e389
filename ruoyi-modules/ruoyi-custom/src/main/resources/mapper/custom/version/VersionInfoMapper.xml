<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.version.mapper.VersionInfoMapper">

    <resultMap type="VersionInfo" id="VersionInfoResult">
        <result property="id" column="id"/>
        <result property="versionNumber" column="version_number"/>
        <result property="description" column="description"/>
        <result property="appDownloadUrl" column="app_download_url"/>
        <result property="appName" column="app_name"/>
        <result property="releaseDate" column="release_date"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectVersionInfoVo">
        SELECT id, version_number, description, app_download_url,
               app_name, release_date, status, del_flag
        FROM t_version_info
    </sql>

    <select id="selectVersionInfoList" parameterType="VersionInfo" resultMap="VersionInfoResult">
        <include refid="selectVersionInfoVo"/>
        <where>
            del_flag = '0'
            <!-- 按版本号精确匹配 -->
            <if test="versionNumber != null">
                and version_number = #{versionNumber}
            </if>

            <!-- 按App名称模糊查询 -->
            <if test="appName != null and appName != ''">
                and app_name like concat('%', #{appName}, '%')
            </if>

            <!-- 按描述内容精确匹配 -->
            <if test="description != null and description != ''">
                and description = #{description}
            </if>

            <!-- 按发布日期查询 -->
            <if test="releaseDate != null">
                and release_date = #{releaseDate}
            </if>

            <!-- 按下载地址查询 -->
            <if test="appDownloadUrl != null and appDownloadUrl != ''">
                and app_download_url = #{appDownloadUrl}
            </if>

            <!-- 按状态筛选 -->
            <if test="status != null and status != ''">
                and status = #{status}
            </if>

            <!-- 忽略逻辑删除的数据 -->
            <if test="delFlag == null or delFlag == '0'">
                and del_flag != '1'
            </if>
        </where>
        ORDER BY version_number DESC
    </select>

    <select id="selectVersionInfoById" parameterType="Long"
            resultMap="VersionInfoResult">
        <include refid="selectVersionInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertVersionInfo" parameterType="VersionInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_version_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="versionNumber != null">version_number,</if>
            <if test="description != null">description,</if>
            <if test="appDownloadUrl != null">app_download_url,</if>
            <if test="appName != null">app_name,</if>
            <if test="releaseDate != null">release_date,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="versionNumber != null">#{versionNumber},</if>
            <if test="description != null">#{description},</if>
            <if test="appDownloadUrl != null">#{appDownloadUrl},</if>
            <if test="appName != null">#{appName},</if>
            <if test="releaseDate != null">#{releaseDate},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateVersionInfo" parameterType="VersionInfo">
        update t_version_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="versionNumber != null">version_number = #{versionNumber},</if>
            <if test="description != null">description = #{description},</if>
            <if test="appDownloadUrl != null">app_download_url = #{appDownloadUrl},</if>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="releaseDate != null">release_date = #{releaseDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVersionInfoById" parameterType="Long">
        DELETE
        FROM t_version_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteVersionInfoByIds" parameterType="String">
        delete from t_version_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCurrentUse" resultMap="VersionInfoResult">
        <include refid="selectVersionInfoVo"/>
        WHERE status = '1' AND del_flag = '0'
        ORDER BY version_number DESC
        LIMIT 1
    </select>

</mapper>
