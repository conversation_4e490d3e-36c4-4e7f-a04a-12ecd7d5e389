<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.issueReport.mapper.IssueReportInfoMapper">

    <resultMap type="IssueReportInfo" id="IssueReportInfoResult">
        <result property="id" column="id"/>
        <result property="area" column="area"/>
        <result property="reporterId" column="reporter_id"/>
        <result property="reporterName" column="reporter_name"/>
        <result property="reportTime" column="report_time"/>
        <result property="urgencyLevel" column="urgency_level"/>
        <result property="status" column="status"/>
        <result property="issueDescription" column="issue_description"/>
        <result property="resolution" column="resolution"/>
    </resultMap>

    <sql id="selectIssueReportInfoVo">
        SELECT id,
               area,
               reporter_id,
               reporter_name,
               report_time,
               urgency_level,
               status,
               issue_description,
               resolution
        FROM t_issue_report_info
    </sql>

    <select id="selectIssueReportInfoList" parameterType="IssueReportInfo" resultMap="IssueReportInfoResult">
        <include refid="selectIssueReportInfoVo"/>
        <where>
            <if test="area != null  and area != ''">
                and area = #{area}
            </if>
            <if test="reporterId != null ">
                and reporter_id = #{reporterId}
            </if>
            <if test="reporterName != null  and reporterName != ''">
                and reporter_name like concat('%', #{reporterName}, '%')
            </if>
            <if test="reportTime != null ">
                and report_time = #{reportTime}
            </if>
            <if test="urgencyLevel != null  and urgencyLevel != ''">
                and urgency_level = #{urgencyLevel}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="issueDescription != null  and issueDescription != ''">
                and issue_description = #{issueDescription}
            </if>
            <if test="resolution != null  and resolution != ''">
                and resolution = #{resolution}
            </if>
        </where>
    </select>

    <select id="selectIssueReportInfoById" parameterType="Long"
            resultMap="IssueReportInfoResult">
        <include refid="selectIssueReportInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertIssueReportInfo" parameterType="IssueReportInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_issue_report_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null and area != ''">area,
            </if>
            <if test="reporterId != null">reporter_id,
            </if>
            <if test="reporterName != null and reporterName != ''">reporter_name,
            </if>
            <if test="reportTime != null">report_time,
            </if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level,
            </if>
            <if test="status != null and status != ''">status,
            </if>
            <if test="issueDescription != null and issueDescription != ''">issue_description,
            </if>
            <if test="resolution != null">resolution,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null and area != ''">#{area},
            </if>
            <if test="reporterId != null">#{reporterId},
            </if>
            <if test="reporterName != null and reporterName != ''">#{reporterName},
            </if>
            <if test="reportTime != null">#{reportTime},
            </if>
            <if test="urgencyLevel != null and urgencyLevel != ''">#{urgencyLevel},
            </if>
            <if test="status != null and status != ''">#{status},
            </if>
            <if test="issueDescription != null and issueDescription != ''">#{issueDescription},
            </if>
            <if test="resolution != null">#{resolution},
            </if>
        </trim>
    </insert>

    <update id="updateIssueReportInfo" parameterType="IssueReportInfo">
        update t_issue_report_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null and area != ''">area =
                #{area},
            </if>
            <if test="reporterId != null">reporter_id =
                #{reporterId},
            </if>
            <if test="reporterName != null and reporterName != ''">reporter_name =
                #{reporterName},
            </if>
            <if test="reportTime != null">report_time =
                #{reportTime},
            </if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level =
                #{urgencyLevel},
            </if>
            <if test="status != null and status != ''">status =
                #{status},
            </if>
            <if test="issueDescription != null and issueDescription != ''">issue_description =
                #{issueDescription},
            </if>
            <if test="resolution != null">resolution =
                #{resolution},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIssueReportInfoById" parameterType="Long">
        DELETE
        FROM t_issue_report_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteIssueReportInfoByIds" parameterType="String">
        delete from t_issue_report_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
