<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.warehouse.mapper.MaterialsRemainingStockMapper">

    <resultMap type="MaterialsRemainingStock" id="MaterialsRemainingStockResult">
        <result property="id" column="id"/>
        <result property="baseId" column="base_id"/>
        <result property="quantity" column="quantity"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="warehouseId" column="warehouse_id"/>
    </resultMap>

    <sql id="selectMaterialsRemainingStockVo">
        select id, base_id, quantity,warehouse_id, create_time, create_by, update_time, update_by, del_flag, remark from
        t_materials_remaining_stock
    </sql>

    <select id="selectMaterialsRemainingStockList" parameterType="MaterialsRemainingStock"
            resultMap="MaterialsRemainingStockResult">
        <include refid="selectMaterialsRemainingStockVo"/>
        <where>
            <if test="baseId != null ">and base_id = #{baseId}</if>
            <if test="quantity != null ">and quantity = #{quantity}</if>
            <if test="warehouseId != null ">and warehouse_id = #{warehouseId}</if>
        </where>
    </select>

    <select id="selectMaterialsRemainingStockById" parameterType="Long" resultMap="MaterialsRemainingStockResult">
        <include refid="selectMaterialsRemainingStockVo"/>
        where id = #{id}
    </select>

    <insert id="insertMaterialsRemainingStock" parameterType="MaterialsRemainingStock">
        insert into t_materials_remaining_stock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="baseId != null">base_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="warehouseId != null">warehouse_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="baseId != null">#{baseId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
        </trim>
    </insert>

    <update id="updateMaterialsRemainingStock" parameterType="MaterialsRemainingStock">
        update t_materials_remaining_stock
        <trim prefix="SET" suffixOverrides=",">
            <if test="baseId != null">base_id = #{baseId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialsRemainingStockById" parameterType="Long">
        delete from t_materials_remaining_stock where id = #{id}
    </delete>

    <delete id="deleteMaterialsRemainingStockByIds" parameterType="String">
        update t_materials_remaining_stock set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getByBaseId" parameterType="Long" resultMap="MaterialsRemainingStockResult">
        SELECT
        b.id,
        a.id as base_id,
        IFNULL(b.quantity,0) as quantity,
        b.create_time,
        b.create_by,
        b.update_time,
        b.update_by,
        b.del_flag,
        b.remark
        FROM
        t_materials_base AS a
        LEFT JOIN (select * from t_materials_remaining_stock where warehouse_id = #{warehouseId})AS b ON a.id =
        b.base_id
        <where>
            <if test="materialId != null">
                and a.id = #{materialId}
            </if>
        </where>
    </select>
</mapper>
