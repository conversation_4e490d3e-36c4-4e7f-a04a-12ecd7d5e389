<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.warehouse.mapper.InWarehousingRecordBaseMapper">

    <resultMap type="InWarehousingRecordBase" id="InWarehousingRecordBaseResult">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="materialSource" column="material_source"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehousingDate" column="warehousing_date"/>
        <result property="responsiblePerson" column="responsible_person"/>
        <result property="registerPerson" column="register_person"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="warehouseName" column="warehouseName"/>
        <result property="responsiblePersonName" column="responsiblePersonName"/>
        <result property="registerPersonName" column="registerPersonName"/>
    </resultMap>

    <sql id="selectInWarehousingRecordBaseVo">
        SELECT
        a.id,
        a.number,
        a.material_source,
        a.warehouse_id,
        b.name as warehouseName,
        a.warehousing_date,
        a.responsible_person,
        a.register_person,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_in_warehousing_record_base as a left join t_warehouse_base as b on a.warehouse_id = b.id
    </sql>

    <select id="selectInWarehousingRecordBaseList" parameterType="InWarehousingRecordBase"
            resultMap="InWarehousingRecordBaseResult">
        <include refid="selectInWarehousingRecordBaseVo"/>
        <where>
            a.del_flag = '0'
            <if test="number != null  and number != ''">and a.number like concat('%',#{number},'%')</if>
            <if test="materialSource != null  and materialSource != ''">and a.material_source = #{materialSource}</if>
            <if test="beginDate != null and endDate!=null">and date_format(a.warehousing_date,'%y%m%d') BETWEEN
                date_format(#{beginDate},'%y%m%d') and date_format(#{endDate},'%y%m%d')
            </if>
            <if test="warehouseId != null ">and a.warehouse_id = #{warehouseId}</if>
            <if test="warehouseName != null ">and b.name = #{warehouseName}</if>
            <if test="warehousingDate != null ">and a.warehousing_date = #{warehousingDate}</if>
            <if test="responsiblePerson != null  and responsiblePerson != ''">and a.responsible_person =
                #{responsiblePerson}
            </if>
            <if test="registerPerson != null  and registerPerson != ''">and a.register_person = #{registerPerson}</if>
        </where>
    </select>

    <select id="selectInWarehousingRecordBaseById" parameterType="Long" resultMap="InWarehousingRecordBaseResult">
        <include refid="selectInWarehousingRecordBaseVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertInWarehousingRecordBase" parameterType="InWarehousingRecordBase" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_in_warehousing_record_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="number != null">number,</if>
            <if test="materialSource != null">material_source,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehousingDate != null">warehousing_date,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="registerPerson != null">register_person,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="number != null">#{number},</if>
            <if test="materialSource != null">#{materialSource},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehousingDate != null">#{warehousingDate},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="registerPerson != null">#{registerPerson},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateInWarehousingRecordBase" parameterType="InWarehousingRecordBase">
        update t_in_warehousing_record_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="number != null">number = #{number},</if>
            <if test="materialSource != null">material_source = #{materialSource},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehousingDate != null">warehousing_date = #{warehousingDate},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="registerPerson != null">register_person = #{registerPerson},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInWarehousingRecordBaseById" parameterType="Long">
        delete from t_in_warehousing_record_base where id = #{id}
    </delete>

    <delete id="deleteInWarehousingRecordBaseByIds" parameterType="String">
        update t_in_warehousing_record_base set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
