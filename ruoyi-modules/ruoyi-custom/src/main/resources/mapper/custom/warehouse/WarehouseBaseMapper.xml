<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.warehouse.mapper.WarehouseBaseMapper">

    <resultMap type="WarehouseBase" id="WarehouseBaseResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="affiliatedOrganization" column="affiliated_organization"/>
        <result property="location" column="location"/>
        <result property="administrator" column="administrator"/>
        <result property="applicationDescribe" column="application_describe"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectWarehouseBaseVo">
        select id, name, affiliated_organization, location, administrator, application_describe, create_time, create_by,
        update_time, update_by, del_flag, remark from t_warehouse_base
    </sql>

    <select id="selectWarehouseBaseList" parameterType="WarehouseBase" resultMap="WarehouseBaseResult">
        <include refid="selectWarehouseBaseVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="affiliatedOrganization != null  and affiliatedOrganization != ''">and affiliated_organization =
                #{affiliatedOrganization}
            </if>
            <if test="location != null  and location != ''">and location = #{location}</if>
            <if test="administrator != null  and administrator != ''">and administrator = #{administrator}</if>
            <if test="applicationDescribe != null  and applicationDescribe != ''">and application_describe =
                #{applicationDescribe}
            </if>
        </where>
    </select>

    <select id="selectWarehouseBaseById" parameterType="Long" resultMap="WarehouseBaseResult">
        <include refid="selectWarehouseBaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertWarehouseBase" parameterType="WarehouseBase">
        insert into t_warehouse_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="affiliatedOrganization != null">affiliated_organization,</if>
            <if test="location != null">location,</if>
            <if test="administrator != null">administrator,</if>
            <if test="applicationDescribe != null">application_describe,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="affiliatedOrganization != null">#{affiliatedOrganization},</if>
            <if test="location != null">#{location},</if>
            <if test="administrator != null">#{administrator},</if>
            <if test="applicationDescribe != null">#{applicationDescribe},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateWarehouseBase" parameterType="WarehouseBase">
        update t_warehouse_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="affiliatedOrganization != null">affiliated_organization = #{affiliatedOrganization},</if>
            <if test="location != null">location = #{location},</if>
            <if test="administrator != null">administrator = #{administrator},</if>
            <if test="applicationDescribe != null">application_describe = #{applicationDescribe},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWarehouseBaseById" parameterType="Long">
        delete from t_warehouse_base where id = #{id}
    </delete>

    <delete id="deleteWarehouseBaseByIds" parameterType="String">
        update t_warehouse_base set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getWarehouseLabelList" resultType="cn.hutool.json.JSONObject">
        select id as value , name as label from t_warehouse_base where del_flag = '0'
    </select>
</mapper>
