<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.warehouse.mapper.InventoryRecordBaseMapper">

    <resultMap type="InventoryRecordBase" id="InventoryRecordBaseResult">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="inventoryStaff" column="inventory_staff"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="warehouseName" column="warehouseName"/>
        <result property="inventoryStaffName" column="inventoryStaffName"/>
        <result property="administrator" column="administrator"/>
        <result property="inventoryDate" column="inventory_date"/>
    </resultMap>

    <sql id="selectInventoryRecordBaseVo">
        SELECT
        a.id,
        a.number,
        a.warehouse_id,
        c.name as warehouseName,
        c.administrator as administrator,
        a.inventory_staff,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.inventory_date,
        a.remark
        FROM
        t_inventory_record_base as a left join t_warehouse_base
        as c on a.warehouse_id = c.id
    </sql>

    <select id="selectInventoryRecordBaseList" parameterType="InventoryRecordBase"
            resultMap="InventoryRecordBaseResult">
        <include refid="selectInventoryRecordBaseVo"/>
        <where>
            a.del_flag = '0'
            <if test="number != null  and number != ''">and a.number like concat('%',#{number},'%')</if>
            <if test="warehouseId != null ">and a.warehouse_id = #{warehouseId}</if>
            <if test="beginDate != null and endDate!=null">and date_format(a.inventory_date,'%y%m%d') BETWEEN
                date_format(#{beginDate},'%y%m%d') and date_format(#{endDate},'%y%m%d')
            </if>
            <if test="inventoryStaff != null  and inventoryStaff != ''">and a.inventory_staff = #{inventoryStaff}</if>
            <if test="warehouseName != null  and warehouseName != ''">and c.name = #{warehouseName}</if>
            order by a.create_time desc
        </where>
    </select>

    <select id="selectInventoryRecordBaseById" parameterType="Long" resultMap="InventoryRecordBaseResult">
        <include refid="selectInventoryRecordBaseVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertInventoryRecordBase" parameterType="InventoryRecordBase" useGeneratedKeys="true" keyProperty="id">
        insert into t_inventory_record_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="number != null">number,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="inventoryStaff != null">inventory_staff,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="inventoryDate != null">inventory_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="number != null">#{number},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="inventoryStaff != null">#{inventoryStaff},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="inventoryDate != null">#{inventoryDate},</if>
        </trim>
    </insert>

    <update id="updateInventoryRecordBase" parameterType="InventoryRecordBase">
        update t_inventory_record_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="number != null">number = #{number},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="inventoryDate != null">inventory_date = #{inventoryDate},</if>
            <if test="inventoryStaff != null">inventory_staff = #{inventoryStaff},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryRecordBaseById" parameterType="Long">
        delete from t_inventory_record_base where id = #{id}
    </delete>

    <delete id="deleteInventoryRecordBaseByIds" parameterType="String">
        update t_inventory_record_base set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
