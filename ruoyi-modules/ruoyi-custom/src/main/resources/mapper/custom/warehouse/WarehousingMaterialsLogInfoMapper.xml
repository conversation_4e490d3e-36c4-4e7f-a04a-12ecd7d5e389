<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.warehouse.mapper.WarehousingMaterialsLogInfoMapper">

    <resultMap type="WarehousingMaterialsLogInfo" id="WarehousingMaterialsLogInfoResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="baseId" column="base_id"/>
        <result property="materialsId" column="materials_id"/>
        <result property="previousQuantity" column="previous_quantity"/>
        <result property="quantity" column="quantity"/>
        <result property="currentQuantity" column="current_quantity"/>
        <result property="price" column="price"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="productionDate" column="production_date"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="useType" column="use_type"/>
        <result property="useUser" column="use_user"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="materialsName" column="materialsName"/>
        <result property="supplierName" column="supplierName"/>
        <result property="typeCategory" column="typeCategory"/>
        <result property="specification" column="specification"/>
    </resultMap>

    <sql id="selectWarehousingMaterialsLogInfoVo">
        SELECT
        a.id,
        a.type,
        a.base_id,
        a.materials_id,
        c.`name` as materialsName,
        c.type as typeCategory,
        c.specification as specification,
        a.previous_quantity,
        a.quantity,
        a.warehouse_id,
        a.current_quantity,
        a.price,
        a.supplier_id,
        b.`name` as supplierName,
        a.production_date,
        a.effective_date,
        a.use_type,
        a.use_user,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_warehousing_materials_log_info as a LEFT JOIN t_supplier_info as b on a.supplier_id = b.id
        left join t_materials_base as c on c.id = a.materials_id
    </sql>

    <select id="selectWarehousingMaterialsLogInfoList" parameterType="WarehousingMaterialsLogInfo"
            resultMap="WarehousingMaterialsLogInfoResult">
        <include refid="selectWarehousingMaterialsLogInfoVo"/>
        <where>
            <if test="type != null  and type != ''">and a.type = #{type}</if>
            <if test="baseId != null ">and a.base_id = #{baseId}</if>
            <if test="materialsId != null ">and a.materials_id = #{materialsId}</if>
            <if test="previousQuantity != null ">and a.previous_quantity = #{previousQuantity}</if>
            <if test="quantity != null ">and a.quantity = #{quantity}</if>
            <if test="currentQuantity != null ">and a.current_quantity = #{currentQuantity}</if>
            <if test="price != null ">and a.price = #{price}</if>
            <if test="supplierId != null ">and a.supplier_id = #{supplierId}</if>
            <if test="productionDate != null ">and a.production_date = #{productionDate}</if>
            <if test="effectiveDate != null ">and a.effective_date = #{effectiveDate}</if>
            <if test="useType != null  and useType != ''">and a.use_type = #{useType}</if>
            <if test="useUser != null  and useUser != ''">and a.use_user = #{useUser}</if>
            <if test="warehouseId != null ">and a.warehouse_id = #{warehouseId}</if>
        </where>
    </select>

    <select id="selectWarehousingMaterialsLogInfoById" parameterType="Long"
            resultMap="WarehousingMaterialsLogInfoResult">
        <include refid="selectWarehousingMaterialsLogInfoVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertWarehousingMaterialsLogInfo" parameterType="WarehousingMaterialsLogInfo">
        insert into t_warehousing_materials_log_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="baseId != null">base_id,</if>
            <if test="materialsId != null">materials_id,</if>
            <if test="previousQuantity != null">previous_quantity,</if>
            <if test="quantity != null">quantity,</if>
            <if test="currentQuantity != null">current_quantity,</if>
            <if test="price != null">price,</if>
            <if test="supplierId != null">supplier_id,</if>
            <if test="productionDate != null">production_date,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="useType != null">use_type,</if>
            <if test="useUser != null">use_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="warehouseId != null">warehouse_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="baseId != null">#{baseId},</if>
            <if test="materialsId != null">#{materialsId},</if>
            <if test="previousQuantity != null">#{previousQuantity},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="currentQuantity != null">#{currentQuantity},</if>
            <if test="price != null">#{price},</if>
            <if test="supplierId != null">#{supplierId},</if>
            <if test="productionDate != null">#{productionDate},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="useType != null">#{useType},</if>
            <if test="useUser != null">#{useUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
        </trim>
    </insert>

    <update id="updateWarehousingMaterialsLogInfo" parameterType="WarehousingMaterialsLogInfo">
        update t_warehousing_materials_log_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="baseId != null">base_id = #{baseId},</if>
            <if test="materialsId != null">materials_id = #{materialsId},</if>
            <if test="previousQuantity != null">previous_quantity = #{previousQuantity},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="currentQuantity != null">current_quantity = #{currentQuantity},</if>
            <if test="price != null">price = #{price},</if>
            <if test="supplierId != null">supplier_id = #{supplierId},</if>
            <if test="productionDate != null">production_date = #{productionDate},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="useType != null">use_type = #{useType},</if>
            <if test="useUser != null">use_user = #{useUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWarehousingMaterialsLogInfoById" parameterType="Long">
        delete from t_warehousing_materials_log_info where id = #{id}
    </delete>

    <delete id="deleteWarehousingMaterialsLogInfoByIds" parameterType="String">
        update t_warehousing_materials_log_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getLogInfoByBaseId" parameterType="Object" resultMap="WarehousingMaterialsLogInfoResult">
        <include refid="selectWarehousingMaterialsLogInfoVo"/>
        where a.base_id = #{baseId} and a.type = #{type}
    </select>
</mapper>
