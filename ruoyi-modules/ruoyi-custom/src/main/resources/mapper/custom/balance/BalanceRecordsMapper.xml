<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.balance.mapper.BalanceRecordsMapper">

    <resultMap type="BalanceRecords" id="BalanceRecordsResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="lastAmount" column="last_amount"/>
        <result property="changedAmount" column="changed_amount"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="changedType" column="changed_type"/>
    </resultMap>

    <sql id="selectBalanceRecordsVo">
        select id, user_id, last_amount, changed_amount, amount, create_time, create_by, update_time, update_by,
        del_flag, remark, changed_type from t_balance_records
    </sql>

    <select id="selectBalanceRecordsList" parameterType="BalanceRecords" resultMap="BalanceRecordsResult">
        <include refid="selectBalanceRecordsVo"/>
        <where>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="lastAmount != null ">and last_amount = #{lastAmount}</if>
            <if test="changedAmount != null ">and changed_amount = #{changedAmount}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="changedType != null  and changedType != ''">and changed_type = #{changedType}</if>
        </where>
    </select>

    <select id="selectBalanceRecordsById" parameterType="String" resultMap="BalanceRecordsResult">
        <include refid="selectBalanceRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertBalanceRecords" parameterType="BalanceRecords">
        insert into t_balance_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="lastAmount != null">last_amount,</if>
            <if test="changedAmount != null">changed_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="changedType != null">changed_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="lastAmount != null">#{lastAmount},</if>
            <if test="changedAmount != null">#{changedAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="changedType != null">#{changedType},</if>
        </trim>
    </insert>

    <update id="updateBalanceRecords" parameterType="BalanceRecords">
        update t_balance_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="lastAmount != null">last_amount = #{lastAmount},</if>
            <if test="changedAmount != null">changed_amount = #{changedAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="changedType != null">changed_type = #{changedType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBalanceRecordsById" parameterType="String">
        delete from t_balance_records where id = #{id}
    </delete>

    <delete id="deleteBalanceRecordsByIds" parameterType="String">
        delete from t_balance_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
