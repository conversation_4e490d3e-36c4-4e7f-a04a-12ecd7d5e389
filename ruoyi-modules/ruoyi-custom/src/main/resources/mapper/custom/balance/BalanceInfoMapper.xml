<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.balance.mapper.BalanceInfoMapper">

    <resultMap type="BalanceInfo" id="BalanceInfoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="lastAmount" column="last_amount"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectBalanceInfoVo">
        select id, user_id, last_amount, amount, create_time, create_by, update_time, update_by, del_flag, remark from
        t_balance_info
    </sql>

    <select id="selectBalanceInfoList" parameterType="com.ruoyi.custom.admin.balance.domain.BalanceInfo"
            resultMap="BalanceInfoResult">
        <include refid="selectBalanceInfoVo"/>
        <where>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="lastAmount != null ">and last_amount = #{lastAmount}</if>
            <if test="amount != null ">and amount = #{amount}</if>
        </where>
    </select>

    <select id="selectBalanceInfoById" parameterType="String" resultMap="BalanceInfoResult">
        <include refid="selectBalanceInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertBalanceInfo" parameterType="com.ruoyi.custom.admin.balance.domain.BalanceInfo">
        insert into t_balance_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="lastAmount != null">last_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="lastAmount != null">#{lastAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateBalanceInfo" parameterType="com.ruoyi.custom.admin.balance.domain.BalanceInfo">
        update t_balance_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="lastAmount != null">last_amount = #{lastAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBalanceInfoById" parameterType="String">
        delete from t_balance_info where id = #{id}
    </delete>

    <delete id="deleteBalanceInfoByIds" parameterType="String">
        delete from t_balance_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
