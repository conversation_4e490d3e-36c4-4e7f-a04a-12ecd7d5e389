<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.liveManage.mapper.MealComboFeeMapper">

    <resultMap type="MealComboFee" id="MealComboFeeResult">
        <result property="id" column="id"/>
        <result property="baseId" column="base_id"/>
        <result property="fee" column="fee"/>
        <result property="version" column="version"/>
    </resultMap>

    <sql id="selectMealComboFeeVo">
        select id, base_id, fee, version
        from t_meal_combo_fee
    </sql>

    <select id="selectMealComboFeeList" parameterType="MealComboFee" resultMap="MealComboFeeResult">
        <include refid="selectMealComboFeeVo"/>
        <where>
            <if test="baseId != null ">
                and base_id = #{baseId}
            </if>
            <if test="fee != null ">
                and fee = #{fee}
            </if>
            <if test="version != null  and version != ''">
                and version = #{version}
            </if>
        </where>
    </select>

    <select id="selectMealComboFeeById" parameterType="Long"
            resultMap="MealComboFeeResult">
        <include refid="selectMealComboFeeVo"/>
        where id = #{id}
    </select>

    <insert id="insertMealComboFee" parameterType="MealComboFee" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_meal_combo_fee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="baseId != null">base_id,
            </if>
            <if test="fee != null">fee,
            </if>
            <if test="version != null">version,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="baseId != null">#{baseId},
            </if>
            <if test="fee != null">#{fee},
            </if>
            <if test="version != null">#{version},
            </if>
        </trim>
    </insert>

    <update id="updateMealComboFee" parameterType="MealComboFee">
        update t_meal_combo_fee
        <trim prefix="SET" suffixOverrides=",">
            <if test="baseId != null">base_id =
                #{baseId},
            </if>
            <if test="fee != null">fee =
                #{fee},
            </if>
            <if test="version != null">version =
                #{version},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMealComboFeeById" parameterType="Long">
        delete from t_meal_combo_fee where id = #{id}
    </delete>

    <delete id="deleteMealComboFeeByIds" parameterType="String">
        delete from t_meal_combo_fee where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
