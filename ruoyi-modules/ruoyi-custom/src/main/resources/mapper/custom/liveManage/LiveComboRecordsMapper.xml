<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.liveManage.mapper.LiveComboRecordsMapper">

    <resultMap type="com.ruoyi.custom.admin.liveManage.domain.LiveComboRecords" id="LiveComboRecordsResult">
        <result property="id" column="id"/>
        <result property="liveId" column="live_id"/>
        <result property="careLevel" column="care_level"/>
        <result property="comboId" column="combo_id"/>
        <result property="comboVersion" column="combo_version"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectLiveComboRecordsVo">
        select id, live_id, care_level, combo_id, combo_version, state,
        create_time, create_by, update_time, update_by, del_flag, remark from t_live_combo_records
    </sql>

    <select id="selectLiveComboRecordsList" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveComboRecords"
            resultMap="LiveComboRecordsResult">
        <include refid="selectLiveComboRecordsVo"/>
        <where>
            <if test="liveId != null  and liveId != ''">and live_id = #{liveId}</if>
            <if test="careLevel != null  and careLevel != ''">and care_level = #{careLevel}</if>
            <if test="comboId != null  and comboId != ''">and combo_id = #{comboId}</if>
            <if test="comboVersion != null  and comboVersion != ''">and combo_version = #{comboVersion}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
        </where>
    </select>

    <select id="selectLiveComboRecordsById" parameterType="String" resultMap="LiveComboRecordsResult">
        <include refid="selectLiveComboRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertLiveComboRecords" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveComboRecords">
        insert into t_live_combo_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="liveId != null">live_id,</if>
            <if test="careLevel != null">care_level,</if>
            <if test="comboId != null">combo_id,</if>
            <if test="comboVersion != null">combo_version,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="liveId != null">#{liveId},</if>
            <if test="careLevel != null">#{careLevel},</if>
            <if test="comboId != null">#{comboId},</if>
            <if test="comboVersion != null">#{comboVersion},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateLiveComboRecords" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveComboRecords">
        update t_live_combo_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="careLevel != null">care_level = #{careLevel},</if>
            <if test="comboId != null">combo_id = #{comboId},</if>
            <if test="comboVersion != null">combo_version = #{comboVersion},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLiveComboRecordsById" parameterType="String">
        delete from t_live_combo_records where id = #{id}
    </delete>

    <delete id="deleteLiveComboRecordsByIds" parameterType="String">
        delete from t_live_combo_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
