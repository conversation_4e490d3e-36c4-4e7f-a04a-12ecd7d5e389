<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.liveManage.mapper.MealComboBaseMapper">

    <resultMap type="MealComboBase" id="MealComboBaseResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>

        <result property="mealFeeId" column="meal_fee_id"/>
        <result property="fee" column="fee"/>
    </resultMap>

    <sql id="selectMealComboBaseVo">
        SELECT t.id,
               t.name,
               t.remark,
               t.del_flag,

               t1.id AS meal_fee_id,
               t1.fee
        FROM t_meal_combo_base t
                 LEFT JOIN (SELECT *,
                                   ROW_NUMBER() OVER (PARTITION BY base_id ORDER BY id DESC) AS rn
                            FROM t_meal_combo_fee) t1 ON t.id = t1.base_id AND t1.rn = 1
    </sql>

    <select id="selectMealComboBaseList" parameterType="MealComboBase" resultMap="MealComboBaseResult">
        <include refid="selectMealComboBaseVo"/>
        <where>
            <if test="delFlag != null  and delFlag != ''">
                and t.del_flag = #{delFlag}
            </if>
            <if test="name != null  and name != ''">
                and t.name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>

    <select id="selectMealComboBaseById" parameterType="Long"
            resultMap="MealComboBaseResult">
        <include refid="selectMealComboBaseVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertMealComboBase" parameterType="MealComboBase" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_meal_combo_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,
            </if>
            <if test="remark != null">remark,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},
            </if>
            <if test="remark != null">#{remark},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
        </trim>
    </insert>

    <update id="updateMealComboBase" parameterType="MealComboBase">
        update t_meal_combo_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name =
                #{name},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMealComboBaseById" parameterType="Long">
        DELETE
        FROM t_meal_combo_base
        WHERE id = #{id}
    </delete>

    <update id="deleteMealComboBaseByIds" parameterType="String">
        UPDATE t_meal_combo_base
        SET del_flag = '1'
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateMealComboRecord">
        UPDATE t_live_meal_combo_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="mealId != null">meal_id = #{mealId},
            </if>
            <if test="mealFeeId != null">meal_fee_id = #{mealFeeId},
            </if>
            <if test="status != null">status = #{status},
            </if>
        </trim>
        WHERE live_id = #{liveId}
    </update>

    <insert id="insertMealComboRecord">
        INSERT INTO t_live_meal_combo_record (live_id, meal_id, meal_fee_id, status)
        VALUES (#{liveId}, #{mealId}, #{mealFeeId}, #{status})
    </insert>

</mapper>
