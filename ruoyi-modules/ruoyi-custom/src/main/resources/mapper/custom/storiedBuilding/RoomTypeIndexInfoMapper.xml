<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.storiedBuilding.mapper.RoomTypeIndexInfoMapper">

    <resultMap type="com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo" id="RoomTypeIndexInfoResult">
        <result property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="roomId" column="room_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="endTime" column="end_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="typeVersion" column="type_version"/>
        <result property="typeVersionId" column="type_version_id"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectRoomTypeIndexInfoVo">
        select id, type_id, room_id, status, create_time, type_version, type_version_id, create_by, end_time, update_by,
        del_flag, remark from t_room_type_index_info
    </sql>

    <select id="selectRoomTypeIndexInfoList" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo"
            resultMap="RoomTypeIndexInfoResult">
        <include refid="selectRoomTypeIndexInfoVo"/>
        <where>
            del_flag = '0'
            <if test="typeId != null ">and type_id = #{typeId}</if>
            <if test="roomId != null ">and room_id = #{roomId}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="endTime != null ">and end_time = #{endTime}</if>
            <if test="typeVersion != null ">and type_version= #{typeVersion}</if>
        </where>
    </select>

    <select id="selectRoomTypeIndexInfoById" parameterType="Long" resultMap="RoomTypeIndexInfoResult">
        <include refid="selectRoomTypeIndexInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectRoomTypeIndexInfoByRoomId" parameterType="Long" resultMap="RoomTypeIndexInfoResult">
        <include refid="selectRoomTypeIndexInfoVo"/>
        where status = '0' and room_id = #{roomId}
    </select>

    <insert id="insertRoomTypeIndexInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo">
        insert into t_room_type_index_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="typeId != null">type_id,</if>
            <if test="roomId != null">room_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="endTime != null">end_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="typeVersion != null">type_version,</if>
            <if test="typeVersionId != null">type_version_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="typeVersion != null">#{typeVersion},</if>
            <if test="typeVersionId != null">#{typeVersionId},</if>
        </trim>
    </insert>

    <update id="updateRoomTypeIndexInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo">
        update t_room_type_index_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="typeVersion != null">type_version = #{typeVersion},</if>
            <if test="typeVersionId != null">type_version_id = #{typeVersionId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoomTypeIndexInfoById" parameterType="Long">
        delete from t_room_type_index_info where id = #{id}
    </delete>

    <delete id="deleteRoomTypeIndexInfoByIds" parameterType="String">
        update t_room_type_index_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByRoomId" parameterType="String">
        update t_room_type_index_info set status = '1' where room_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="hasRoomTypeId" parameterType="Long" resultType="int">
        select count(1) from t_room_type_index_info where del_flag = '0' and status = '0' and type_id = #{typeId} limit
        1
    </select>
</mapper>
