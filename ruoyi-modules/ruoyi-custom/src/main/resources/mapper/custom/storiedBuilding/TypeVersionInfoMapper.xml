<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.storiedBuilding.mapper.TypeVersionInfoMapper">

    <resultMap type="com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo" id="TypeVersionInfoResult">
        <result property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="status" column="status"/>
        <result property="fees" column="fees"/>
        <result property="version" column="version"/>
        <result property="bedNum" column="bed_num"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTypeVersionInfoVo">
        select id, type_id, status, fees, version, bed_num, create_time, create_by, update_time, update_by,
        del_flag, remark from t_room_type_version_info
    </sql>

    <select id="selectTypeVersionInfoList" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo"
            resultMap="TypeVersionInfoResult">
        <include refid="selectTypeVersionInfoVo"/>
        <where>
            del_flag = '0'
            <if test="typeId != null  and typeId != ''">and type_id = #{typeId}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="fees != null ">and fees = #{fees}</if>
            <if test="version != null  and version != ''">and version = #{version}</if>
            <if test="bedNum != null ">and bed_num = #{bedNum}</if>
        </where>
    </select>

    <select id="selectTypeVersionInfoById" parameterType="Long" resultMap="TypeVersionInfoResult">
        <include refid="selectTypeVersionInfoVo"/>
        where id = #{id}
    </select>

    <select id="getVersionInfo" parameterType="String" resultMap="TypeVersionInfoResult">
        <include refid="selectTypeVersionInfoVo"/>
        where type_id = #{typeId}
        <if test="status != null and status != '' ">
            and status = #{status}
        </if>
    </select>

    <insert id="insertTypeVersionInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo">
        insert into t_room_type_version_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="typeId != null">type_id,</if>
            <if test="status != null">status,</if>
            <if test="fees != null">fees,</if>
            <if test="version != null">version,</if>
            <if test="bedNum != null">bed_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="status != null">#{status},</if>
            <if test="fees != null">#{fees},</if>
            <if test="version != null">#{version},</if>
            <if test="bedNum != null">#{bedNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateTypeVersionInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo">
        update t_room_type_version_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="fees != null">fees = #{fees},</if>
            <if test="version != null">version = #{version},</if>
            <if test="bedNum != null">bed_num = #{bedNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTypeVersionInfoById" parameterType="Long">
        delete from t_room_type_version_info where id = #{id}
    </delete>

    <delete id="deleteTypeVersionInfoByIds" parameterType="String">
        update t_room_type_version_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
