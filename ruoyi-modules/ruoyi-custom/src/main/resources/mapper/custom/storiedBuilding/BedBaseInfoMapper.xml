<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.storiedBuilding.mapper.BedBaseInfoMapper">

    <resultMap type="com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo" id="BedBaseInfoResult">
        <result property="id" column="id"/>
        <result property="bedName" column="bed_name"/>
        <result property="bedNum" column="bed_num"/>
        <result property="bedState" column="bed_state"/>
        <result property="careIndex" column="care_index"/>
        <result property="feeIndex" column="fee_index"/>
        <result property="liveRecordsIndex" column="live_records_index"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="roomId" column="room_id"/>
    </resultMap>

    <sql id="selectBedBaseInfoVo">
        SELECT id,
               bed_name,
               bed_num,
               bed_state,
               care_index,
               fee_index,
               live_records_index,
               create_time,
               create_by,
               update_time,
               update_by,
               del_flag,
               remark,
               room_id
        FROM t_bed_base_info
    </sql>

    <select id="selectBedBaseInfoList" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo"
            resultMap="BedBaseInfoResult">
        <include refid="selectBedBaseInfoVo"/>
        <where>
            del_flag = '0'
            <if test="bedName != null  and bedName != ''">and bed_name like concat('%', #{bedName}, '%')</if>
            <if test="bedNum != null  and bedNum != ''">and bed_num like concat('%', #{bedNum}, '%')</if>
            <if test="bedState != null  and bedState != ''">and bed_state = #{bedState}</if>
            <if test="careIndex != null  and careIndex != ''">and care_index = #{careIndex}</if>
            <if test="feeIndex != null  and feeIndex != ''">and fee_index = #{feeIndex}</if>
            <if test="liveRecordsIndex != null  and liveRecordsIndex != ''">and live_records_index =
                #{liveRecordsIndex}
            </if>
            <if test="roomId != null ">and room_id = #{roomId}</if>
        </where>
    </select>

    <select id="selectBedBaseInfoById" parameterType="Long" resultMap="BedBaseInfoResult">
        <include refid="selectBedBaseInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertBedBaseInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_bed_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bedName != null">bed_name,</if>
            <if test="bedNum != null">bed_num,</if>
            <if test="bedState != null">bed_state,</if>
            <if test="careIndex != null">care_index,</if>
            <if test="feeIndex != null">fee_index,</if>
            <if test="liveRecordsIndex != null">live_records_index,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="roomId != null">room_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bedName != null">#{bedName},</if>
            <if test="bedNum != null">#{bedNum},</if>
            <if test="bedState != null">#{bedState},</if>
            <if test="careIndex != null">#{careIndex},</if>
            <if test="feeIndex != null">#{feeIndex},</if>
            <if test="liveRecordsIndex != null">#{liveRecordsIndex},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="roomId != null">#{roomId},</if>
        </trim>
    </insert>

    <update id="updateBedBaseInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo">
        update t_bed_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="bedName != null">bed_name = #{bedName},</if>
            <if test="bedNum != null">bed_num = #{bedNum},</if>
            <if test="bedState != null">bed_state = #{bedState},</if>
            <if test="careIndex != null">care_index = #{careIndex},</if>
            <if test="feeIndex != null">fee_index = #{feeIndex},</if>
            <if test="liveRecordsIndex != null">live_records_index = #{liveRecordsIndex},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBedBaseInfoById" parameterType="Long">
        DELETE
        FROM t_bed_base_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteBedBaseInfoByIds" parameterType="String">
        update t_bed_base_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="hasBedByRoomId" parameterType="Long" resultType="int">
        SELECT COUNT(1)
        FROM t_bed_base_info
        WHERE del_flag = '0'
          AND room_id = #{id}
        LIMIT 1
    </select>

    <select id="getBedInfo" parameterType="Long" resultType="cn.hutool.json.JSONObject">
        SELECT a.id                                                                       AS id,
               CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', a.`bed_name`) AS groupName,
               a.bed_name                                                                 AS name,
               a.room_id                                                                  AS roomId,
               r.type_version                                                             AS roomVersion,
               r.type_version_id                                                          AS roomTypeVersionId,
               rt.fees                                                                     AS roomFees,
               IFNULL(u.id, '')                                                           AS userId,
               IFNULL(u.name, '')                                                         AS userName,
               '4'                                                                        AS type
        FROM t_bed_base_info AS a
                 LEFT JOIN t_room_type_index_info AS r ON a.room_id = r.room_id
                 LEFT JOIN t_room_type_version_info AS rt ON r.type_version_id = rt.id
                 LEFT JOIN t_live_bed_records br ON a.id = br.bed_id AND br.live_state = 0
                 LEFT JOIN t_live_base_info AS b ON br.live_id = b.id
                 LEFT JOIN t_elderly_people_info AS u ON u.id = b.user_id
                 LEFT JOIN t_storied_building_info bud1 ON a.room_id = bud1.id
                 LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
                 LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
        WHERE r.STATUS = '0'
          AND a.del_flag = '0'
          AND a.room_id = #{roomId}

    </select>
    <select id="getRoomBedNum" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        SELECT COUNT(id) AS totalBedNumber, SUM(IF(bed_state = '0', 1, 0)) AS occupancyNumber
        FROM t_bed_base_info
        WHERE room_id = #{id}
          AND del_flag = '0'
    </select>

    <select id="getRoomBedList" resultType="cn.hutool.json.JSONObject" parameterType="Long">


        SELECT a.id                     AS id,
               a.bed_name               AS name,
               a.room_id                AS roomId,
               r.type_version           AS roomVersion,
               IFNULL(u.id, '')         AS userId,
               IFNULL(u.name, '')       AS userName,
               IFNULL(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM CAST(ve.fees AS CHAR))), '') AS fees,
               IFNULL(u.age, '')        AS age,
               IFNULL(u.phone, '')      AS phone,
               -- IFNULL(a.bed_state,1) AS bedState,
               IFNULL(br.live_state, 1) AS bedState
        FROM t_bed_base_info AS a
                 LEFT JOIN t_room_type_index_info AS r ON a.room_id = r.room_id
                 LEFT JOIN t_live_bed_records AS br ON a.id = br.bed_id AND br.live_state = 0
                 LEFT JOIN t_live_base_info AS b ON br.live_id = b.id
                 LEFT JOIN t_elderly_people_info AS u ON u.id = b.user_id
                 LEFT JOIN t_room_type_version_info AS ve ON ve.type_id = r.type_id AND ve.version = r.type_version
        WHERE r.status = '0'
          AND a.del_flag = '0'
          AND r.room_id = #{roomId}


    </select>
    <select id="getBedStateData" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        SELECT
        a.id,
        CONCAT(bud3.`name`,'-',bud2.`name`,'-',bud1.`name`,'-',a.`bed_name`) as groupName,
        CONCAT(bud1.name,'-',a.bed_name) as name,
        a.room_id as roomId,
        r.type_version as roomVersion,
        r.type_version_id as typeVersionId,
        -- a.bed_state as bedState,
        CASE WHEN u.status = '0' OR u.status = '2' THEN '0' ELSE '1' END as bedState,
        IFNULL(u.name,'') as username,
        IFNULL(u.phone,'') as phone,
        IFNULL(u.age,'') as age,
        IFNULL(ty.name,'') as typeName
        FROM
        t_bed_base_info AS a
        left join ( select * from (SELECT *,ROW_NUMBER() OVER (PARTITION BY bed_id ORDER BY create_time DESC ) as rn
        from
        t_live_bed_records) t where t.rn = 1 ) as br on a.id=br.bed_id and br.live_state=0
        -- left join t_live_base_info as l on a.live_records_index = l.id
        left join t_live_base_info as l on br.live_id=l.id
        LEFT JOIN t_storied_building_info bud1 on a.room_id= bud1.id
        LEFT JOIN t_storied_building_info bud2 on bud1.parent_id= bud2.id
        LEFT JOIN t_storied_building_info bud3 on bud2.parent_id= bud3.id
        left join t_elderly_people_info as u on u.id = l.user_id
        left join (select room_id,type_id,type_version,type_version_id from t_room_type_index_info where status = '0')
        as r on r.room_id = bud1.id
        left join t_room_type_base_info as ty on r.type_id = ty.id
        <where>
            bud1.type = 3 AND a.del_flag = '0'
            <if test="id != null and id != ''">
                AND (bud1.ancestors LIKE '%${id},%'
                OR bud1.id = #{id})
            </if>
            <if test="typeId != null">
                AND r.type_id = #{typeId}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="getBedRecordsList" parameterType="String" resultType="BedRecordsInfoVo">
        SELECT
        IFNULL(u.name,'') as name,
        li.begin_date as beginDate,
        li.end_date as endDate,
        li.live_state as liveState
        FROM
        t_live_bed_records AS li
        LEFT JOIN t_live_base_info AS b ON b.id = li.live_id
        LEFT JOIN t_elderly_people_info AS u ON u.id = b.user_id
        <where>
            li.live_state != '0'
            <if test="bedId != null and bedId != '' ">
                and li.bed_id = #{bedId}
            </if>
            <if test="name != null and name != '' ">
                and u.name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="getBedNameByRoomId" parameterType="Long" resultType="cn.hutool.json.JSONObject">
        SELECT id, bed_name AS label, '4' AS type
        FROM t_bed_base_info
        WHERE del_flag = '0'
          AND room_id = #{roomId}
    </select>


    <resultMap id="BedStateDataRespResultMap" type="com.ruoyi.custom.api.adminApp.rep.BedStateDataResp">
        <id column="building_id" property="buildingId"/>
        <result column="building_name" property="buildingName"/>

        <collection property="floorInfoList" ofType="com.ruoyi.custom.api.adminApp.rep.BedStateDataResp$FloorInfo">
            <id column="floor_id" property="floorId"/>
            <result column="floor_name" property="floorName"/>
            <result column="bed_num" property="bedNum"/>
            <result column="occupied_bed_num" property="occupiedBedNum"/>

            <collection property="roomInfoList" ofType="com.ruoyi.custom.api.adminApp.rep.BedStateDataResp$RoomInfo">
                <id column="room_id" property="roomId"/>
                <result column="room_name" property="roomName"/>
                <result column="room_type" property="roomType"/>

                <collection property="bedInfoList" ofType="com.ruoyi.custom.api.adminApp.rep.BedStateDataResp$BedInfo">
                    <id column="bed_id" property="bedId"/>
                    <result column="bed_name" property="bedName"/>
                    <result column="bed_state_str" property="bedStateStr"/>
                    <result column="elderly_name" property="elderlyName"/>
                    <result column="room_version" property="roomVersion"/>
                    <result column="full_bed_name" property="fullBedName"/>

                </collection>
            </collection>
        </collection>
    </resultMap>


    <select id="getBedStateData2" resultMap="BedStateDataRespResultMap">
        WITH bed_count_per_floor AS (
        SELECT
        f.id AS floor_id,
        COUNT(b.id) AS bed_num
        FROM t_storied_building_info f
        LEFT JOIN t_storied_building_info r ON r.parent_id = f.id AND r.type = '3' AND r.del_flag = '0'
        LEFT JOIN t_bed_base_info b ON b.room_id = r.id AND b.del_flag = '0'
        WHERE f.type = '2' AND f.del_flag = '0'
        GROUP BY f.id
        ),

        occupied_bed_count_per_floor AS (
        SELECT
        f.id AS floor_id,
        COUNT(br.bed_id) AS occupied_bed_num
        FROM t_storied_building_info f
        LEFT JOIN t_storied_building_info r ON r.parent_id = f.id AND r.type = '3' AND r.del_flag = '0'
        LEFT JOIN t_bed_base_info b ON b.room_id = r.id AND b.del_flag = '0'
        LEFT JOIN (
        SELECT * FROM (
        SELECT *, ROW_NUMBER() OVER (PARTITION BY bed_id ORDER BY create_time DESC) AS rn
        FROM t_live_bed_records
        ) tmp WHERE tmp.rn = 1 AND (tmp.live_state = 0 OR tmp.live_state = 2)
        ) br ON br.bed_id = b.id
        WHERE f.type = '2' AND f.del_flag = '0'
        GROUP BY f.id
        )

        SELECT
        build.id AS building_id,
        build.name AS building_name,

        floor.id AS floor_id,
        floor.name AS floor_name,
        COALESCE(bcpf.bed_num, 0) AS bed_num,
        COALESCE(obcpf.occupied_bed_num, 0) AS occupied_bed_num,

        room.id AS room_id,
        room.name AS room_name,
        r.type_version AS room_version,
        ty.name AS room_type,

        bed.id AS bed_id,
        bed.bed_name,
        CONCAT(build.`name`, '-', floor.`name`, '-', room.`name`, '-', bed.bed_name) AS full_bed_name,
        CASE
        WHEN br.live_state = 0 OR br.live_state = 2 THEN '已入住'
        ELSE '未入住'
        END AS bed_state_str,
        CASE
        WHEN br.live_state = 0 OR br.live_state = 2 THEN u.name
        ELSE ''
        END AS elderly_name

        FROM t_storied_building_info build
        LEFT JOIN t_storied_building_info floor ON floor.parent_id = build.id AND floor.type = '2' AND floor.del_flag =
        '0'
        LEFT JOIN bed_count_per_floor bcpf ON floor.id = bcpf.floor_id
        LEFT JOIN occupied_bed_count_per_floor obcpf ON floor.id = obcpf.floor_id
        LEFT JOIN t_storied_building_info room ON room.parent_id = floor.id AND room.type = '3' AND room.del_flag = '0'
        LEFT JOIN t_bed_base_info AS bed ON bed.room_id = room.id AND bed.del_flag = '0'
        LEFT JOIN (
        SELECT * FROM (
        SELECT *, ROW_NUMBER() OVER (PARTITION BY bed_id ORDER BY create_time DESC ) AS rn
        FROM t_live_bed_records
        ) t WHERE t.rn = 1
        ) AS br ON bed.id = br.bed_id
        LEFT JOIN t_live_base_info AS l ON br.live_id = l.id
        LEFT JOIN t_elderly_people_info AS u ON u.id = l.user_id
        LEFT JOIN (
        SELECT room_id, type_id, type_version, type_version_id
        FROM t_room_type_index_info
        WHERE status = '0'
        ) AS r ON r.room_id = room.id
        LEFT JOIN t_room_type_base_info AS ty ON r.type_id = ty.id

        WHERE build.type = '1'
        AND build.del_flag = '0'

        <include refid="bedStateFilter"/>
        ORDER BY build.sort, floor.sort, room.sort, build.id, floor.id, room.id, bed.id
    </select>

    <select id="getBedCountSummary" resultType="cn.hutool.json.JSONObject">
        SELECT COUNT(bed.id) AS totalBeds,
        COUNT(CASE WHEN br.live_state = 0 OR br.live_state = 2 THEN 1 END) AS occupiedBeds,
        COUNT(CASE
        WHEN br.live_state IS NULL OR (br.live_state != 0 AND br.live_state != 2)
        THEN 1 END) AS freeBeds
        FROM t_bed_base_info bed
        LEFT JOIN (SELECT *
        FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY bed_id ORDER BY create_time DESC) AS rn
        FROM t_live_bed_records) t
        WHERE t.rn = 1) br ON bed.id = br.bed_id
        LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
        LEFT JOIN (SELECT room_id, type_id, type_version, type_version_id
        FROM t_room_type_index_info
        WHERE status = '0')
        AS r ON r.room_id = bud1.id
        LEFT JOIN t_room_type_base_info AS ty ON r.type_id = ty.id
        <where>
            bud1.type = 3 AND bed.del_flag = '0'
            <if test="id != null and id != ''">
                AND (bud1.ancestors LIKE '%${id},%'
                OR bud1.id = #{id})
            </if>
            <if test="typeId != null">
                AND r.type_id = #{typeId}
            </if>
        </where>
    </select>

    <sql id="bedStateFilter">
        <if test="bedState == 'occupied'">
            AND (br.live_state = 0 OR br.live_state = 2)
        </if>
        <if test="bedState == 'unoccupied'">
            AND (br.live_state IS NULL OR (br.live_state != 0 AND br.live_state != 2))
        </if>
    </sql>
</mapper>
