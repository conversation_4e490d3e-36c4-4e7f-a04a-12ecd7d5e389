<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.storiedBuilding.mapper.BedCareInfoMapper">

    <resultMap type="com.ruoyi.custom.admin.storiedBuilding.domain.BedCareInfo" id="BedCareInfoResult">
        <result property="id" column="id"/>
        <result property="bedId" column="bed_id"/>
        <result property="careWorkerId" column="care_worker_id"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
        <result property="careState" column="care_state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="buildingName" column="buildingName"/>
        <result property="floorName" column="floorName"/>
        <result property="roomName" column="roomName"/>
        <result property="bedName" column="bedName"/>
    </resultMap>

    <sql id="selectBedCareInfoVo">
        SELECT
        a.id,
        a.bed_id,
        bud3.name as buildingName,
        bud2.name as floorName,
        bud1.name as roomName,
        b.bed_name as bedName,
        a.care_worker_id,
        a.begin_date,
        a.end_date,
        a.care_state,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_bed_care_info AS a
        LEFT JOIN t_bed_base_info AS b ON a.bed_id = b.id
        LEFT JOIN t_storied_building_info bud1 ON b.room_id = bud1.id
        LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
        LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
    </sql>

    <select id="selectBedCareInfoList" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.BedCareInfo"
            resultMap="BedCareInfoResult">
        <include refid="selectBedCareInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="bedId != null ">and a.bed_id = #{bedId}</if>
            <if test="careWorkerId != null ">and a.care_worker_id = #{careWorkerId}</if>
            <if test="beginDate != null ">and a.begin_date = #{beginDate}</if>
            <if test="endDate != null ">and a.end_date = #{endDate}</if>
            <if test="careState != null  and careState != ''">and a.care_state = #{careState}</if>
        </where>
    </select>

    <select id="selectBedCareInfoById" parameterType="Long" resultMap="BedCareInfoResult">
        <include refid="selectBedCareInfoVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertBedCareInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.BedCareInfo">
        insert into t_bed_care_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bedId != null">bed_id,</if>
            <if test="careWorkerId != null">care_worker_id,</if>
            <if test="beginDate != null">begin_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="careState != null">care_state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bedId != null">#{bedId},</if>
            <if test="careWorkerId != null">#{careWorkerId},</if>
            <if test="beginDate != null">#{beginDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="careState != null">#{careState},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateBedCareInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.BedCareInfo">
        update t_bed_care_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="bedId != null">bed_id = #{bedId},</if>
            <if test="careWorkerId != null">care_worker_id = #{careWorkerId},</if>
            <if test="beginDate != null">begin_date = #{beginDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="careState != null">care_state = #{careState},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBedCareInfoById" parameterType="String">
        delete from t_bed_care_info where id = #{id}
    </delete>

    <delete id="deleteBedCareInfoByIds" parameterType="String">
        update t_bed_care_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getGroupCareBed" parameterType="String" resultType="String">
        SELECT GROUP_CONCAT(bed_id) as str from t_bed_care_info where del_flag= '0' and care_state = '0' and
        care_worker_id = #{careWorkerId}
    </select>

    <select id="persentCheck" resultType="java.lang.Integer">
        SELECT count(1) from t_bed_care_info where del_flag = '0' and bed_id in
        <foreach item="bedId" collection="array" open="(" separator="," close=")">
            #{bedId}
        </foreach>
    </select>

    <insert id="batchBed" parameterType="String">
        insert into t_bed_care_info(bed_id, care_worker_id,begin_date,care_state,del_flag) values
        <foreach item="id" collection="bedIds" separator=",">
            (#{id}, #{careWorkerId},now(),'0','0')
        </foreach>
    </insert>

    <update id="updateBed" parameterType="String">
        update t_bed_care_info set del_flag = '1',end_date = now(),care_state='1' where care_worker_id = #{careWorkerId}
        and bed_id in
        <foreach item="id" collection="bedIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
