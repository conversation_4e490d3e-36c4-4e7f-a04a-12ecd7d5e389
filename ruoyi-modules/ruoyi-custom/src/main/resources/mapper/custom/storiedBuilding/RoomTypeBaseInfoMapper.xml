<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.storiedBuilding.mapper.RoomTypeBaseInfoMapper">

    <resultMap type="com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeBaseInfo" id="RoomTypeBaseInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="fees" column="fees"/>
        <result property="version" column="version"/>
        <result property="typeVersionId" column="typeVersionId"/>
        <result property="bedNum" column="bed_num"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="bedNumber" column="bedNumber"/>
    </resultMap>

    <sql id="selectRoomTypeBaseInfoVo">
        SELECT
        a.id,
        a.NAME,
        v.bed_num as bedNumber,
        v.fees as fees,
        v.version as version,
        v.id as typeVersionId,
        a.STATUS,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_room_type_base_info as a
        left join (SELECT * FROM t_room_type_version_info where status = '0') as v on a.id = v.type_id
    </sql>

    <select id="selectRoomTypeBaseInfoList" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeBaseInfo"
            resultMap="RoomTypeBaseInfoResult">
        <include refid="selectRoomTypeBaseInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''">and a.status = #{status}</if>
            <if test="status != null  and status != ''">and a.status = #{status}</if>
        </where>
    </select>

    <select id="getRoomTypeLabelAndValue" resultType="cn.hutool.json.JSONObject">
        select id as value ,name as label from t_room_type_base_info where del_flag = '0'
    </select>

    <select id="selectRoomTypeBaseInfoById" parameterType="Long" resultMap="RoomTypeBaseInfoResult">
        <include refid="selectRoomTypeBaseInfoVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertRoomTypeBaseInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_room_type_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateRoomTypeBaseInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeBaseInfo">
        update t_room_type_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoomTypeBaseInfoById" parameterType="Long">
        delete from t_room_type_base_info where id = #{id}
    </delete>

    <delete id="deleteRoomTypeBaseInfoByIds" parameterType="String">
        update t_room_type_base_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectName" resultType="cn.hutool.json.JSONObject">
        SELECT GROUP_CONCAT(name) as str from t_room_type_base_info where del_flag= '0'
    </select>

    <select id="selectData" resultType="cn.hutool.json.JSONObject">
        SELECT id,name from t_room_type_base_info where del_flag= '0'
    </select>

    <select id="getRoomTypeBase" parameterType="Long" resultType="cn.hutool.json.JSONObject">
        SELECT
        main.id,
        main.name,
        t.fees
        FROM
        t_room_type_base_info as main
        LEFT JOIN t_room_type_index_info AS a on a.type_id = main.id
        LEFT JOIN t_room_type_version_info AS t ON t.type_id = a.type_id and t.id = a.type_version_id
        <where>
            a.status = '0'
            <if test="roomId != null and roomId != ''">
                and a.room_id = #{roomId}
            </if>
        </where>
    </select>
</mapper>
