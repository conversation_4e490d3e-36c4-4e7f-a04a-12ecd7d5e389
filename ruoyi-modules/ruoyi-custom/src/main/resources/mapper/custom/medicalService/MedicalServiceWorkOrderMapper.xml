<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.medicalService.mapper.MedicalServiceWorkOrderMapper">

    <resultMap type="MedicalServiceWorkOrder" id="MedicalServiceWorkOrderResult">
        <result property="id" column="id"/>
        <result property="workOrderNumber" column="work_order_number"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="bedName" column="bed_name"/>
        <result property="serviceItem" column="service_item"/>
        <result property="requirement" column="requirement"/>
        <result property="serviceStartTime" column="service_start_time"/>
        <result property="serviceEndTime" column="service_end_time"/>
        <result property="servicePersonnelId" column="service_personnel_id"/>
        <result property="servicePersonnelName" column="service_personnel_name"/>
        <result property="serviceProgress" column="service_progress"/>
    </resultMap>

    <sql id="selectMedicalServiceWorkOrderVo">
        SELECT id,
               work_order_number,
               elderly_id,
               elderly_name,
               service_item,
               requirement,
               service_start_time,
               service_end_time,
               service_personnel_id,
               service_personnel_name,
               CASE
                   WHEN service_start_time > NOW() THEN '0' -- 未开始
                   WHEN NOW() BETWEEN service_start_time AND service_end_time THEN '1' -- 服务中
                   WHEN NOW() > service_end_time THEN '2' -- 已结束
                   END AS service_progress,

               CONCAT(bud3.`name`,'-',bud2.`name`,'-',bud1.`name`,'-',bedinfo.bed_name) as bedName

        FROM t_medical_service_work_order a
                 LEFT JOIN (SELECT *
                            FROM (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
                                  FROM t_live_base_info) t
                            WHERE rn = 1) b ON a.elderly_id = b.user_id
                 LEFT JOIN t_live_bed_records bed on bed.live_id=b.id
                 LEFT JOIN t_bed_base_info bedinfo on bed.bed_id=bedinfo.id
                 LEFT JOIN t_storied_building_info bud1 on bed.room_id= bud1.id
                 LEFT JOIN t_storied_building_info bud2 on bud1.parent_id= bud2.id
                 LEFT JOIN t_storied_building_info bud3 on bud2.parent_id= bud3.id
    </sql>

    <select id="selectMedicalServiceWorkOrderList" parameterType="MedicalServiceWorkOrder"
            resultMap="MedicalServiceWorkOrderResult">
        <include refid="selectMedicalServiceWorkOrderVo"/>
        <where>
            <if test="workOrderNumber != null  and workOrderNumber != ''">
                and work_order_number = #{workOrderNumber}
            </if>
            <if test="elderlyId != null  and elderlyId != ''">
                and elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null  and elderlyName != ''">
                and elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="serviceItem != null  and serviceItem != ''">
                and service_item = #{serviceItem}
            </if>
            <if test="requirement != null  and requirement != ''">
                and requirement = #{requirement}
            </if>
            <if test="serviceStartTime != null  and serviceEndTime != null">
                and service_start_time >= #{serviceStartTime} and service_start_time &lt;= #{serviceEndTime}
            </if>
            <if test="servicePersonnelId != null ">
                and service_personnel_id = #{servicePersonnelId}
            </if>
            <if test="servicePersonnelName != null  and servicePersonnelName != ''">
                and service_personnel_name like concat('%', #{servicePersonnelName}, '%')
            </if>
            <if test="serviceProgress != null and serviceProgress != ''">
                and CASE
                WHEN service_start_time > NOW() THEN '0'
                WHEN NOW() BETWEEN service_start_time AND service_end_time THEN '1'
                WHEN NOW() > service_end_time THEN '2'
                END = #{serviceProgress}
            </if>
        </where>
    </select>

    <select id="selectMedicalServiceWorkOrderById" parameterType="Long"
            resultMap="MedicalServiceWorkOrderResult">
        <include refid="selectMedicalServiceWorkOrderVo"/>
        where id = #{id}
    </select>

    <insert id="insertMedicalServiceWorkOrder" parameterType="MedicalServiceWorkOrder" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_medical_service_work_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderNumber != null and workOrderNumber != ''">work_order_number,
            </if>
            <if test="elderlyId != null and elderlyId != ''">elderly_id,
            </if>
            <if test="elderlyName != null and elderlyName != ''">elderly_name,
            </if>
            <if test="serviceItem != null and serviceItem != ''">service_item,
            </if>
            <if test="requirement != null and requirement != ''">requirement,
            </if>
            <if test="serviceStartTime != null">service_start_time,
            </if>
            <if test="serviceEndTime != null">service_end_time,
            </if>
            <if test="servicePersonnelId != null">service_personnel_id,
            </if>
            <if test="servicePersonnelName != null and servicePersonnelName != ''">service_personnel_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderNumber != null and workOrderNumber != ''">#{workOrderNumber},
            </if>
            <if test="elderlyId != null and elderlyId != ''">#{elderlyId},
            </if>
            <if test="elderlyName != null and elderlyName != ''">#{elderlyName},
            </if>
            <if test="serviceItem != null and serviceItem != ''">#{serviceItem},
            </if>
            <if test="requirement != null and requirement != ''">#{requirement},
            </if>
            <if test="serviceStartTime != null">#{serviceStartTime},
            </if>
            <if test="serviceEndTime != null">#{serviceEndTime},
            </if>
            <if test="servicePersonnelId != null">#{servicePersonnelId},
            </if>
            <if test="servicePersonnelName != null and servicePersonnelName != ''">#{servicePersonnelName},
            </if>
        </trim>
    </insert>

    <update id="updateMedicalServiceWorkOrder" parameterType="MedicalServiceWorkOrder">
        update t_medical_service_work_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderNumber != null and workOrderNumber != ''">work_order_number =
                #{workOrderNumber},
            </if>
            <if test="elderlyId != null and elderlyId != ''">elderly_id =
                #{elderlyId},
            </if>
            <if test="elderlyName != null and elderlyName != ''">elderly_name =
                #{elderlyName},
            </if>
            <if test="serviceItem != null and serviceItem != ''">service_item =
                #{serviceItem},
            </if>
            <if test="requirement != null and requirement != ''">requirement =
                #{requirement},
            </if>
            <if test="serviceStartTime != null">service_start_time =
                #{serviceStartTime},
            </if>
            <if test="serviceEndTime != null">service_end_time =
                #{serviceEndTime},
            </if>
            <if test="servicePersonnelId != null">service_personnel_id =
                #{servicePersonnelId},
            </if>
            <if test="servicePersonnelName != null and servicePersonnelName != ''">service_personnel_name =
                #{servicePersonnelName},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMedicalServiceWorkOrderById" parameterType="Long">
        DELETE
        FROM t_medical_service_work_order
        WHERE id = #{id}
    </delete>

    <delete id="deleteMedicalServiceWorkOrderByIds" parameterType="String">
        delete from t_medical_service_work_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
