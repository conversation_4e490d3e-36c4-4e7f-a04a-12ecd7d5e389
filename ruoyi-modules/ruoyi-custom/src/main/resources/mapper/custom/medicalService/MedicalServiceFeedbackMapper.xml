<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.medicalService.mapper.MedicalServiceFeedbackMapper">

    <resultMap type="MedicalServiceFeedback" id="MedicalServiceFeedbackResult">
        <result property="id" column="id"/>
        <result property="workOrderId" column="work_order_id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="feedbackContent" column="feedback_content"/>
        <result property="feedbackTime" column="feedback_time"/>
        <result property="imageUrls" column="image_urls"/>
    </resultMap>

    <sql id="selectMedicalServiceFeedbackVo">
        SELECT id, work_order_id, doctor_id, doctor_name, feedback_content, feedback_time, image_urls
        FROM t_medical_service_feedback
    </sql>

    <select id="selectMedicalServiceFeedbackList" parameterType="MedicalServiceFeedback"
            resultMap="MedicalServiceFeedbackResult">
        <include refid="selectMedicalServiceFeedbackVo"/>
        <where>
            <if test="workOrderId != null ">
                and work_order_id = #{workOrderId}
            </if>
            <if test="doctorId != null ">
                and doctor_id = #{doctorId}
            </if>
            <if test="doctorName != null  and doctorName != ''">
                and doctor_name like concat('%', #{doctorName}, '%')
            </if>
            <if test="feedbackContent != null  and feedbackContent != ''">
                and feedback_content = #{feedbackContent}
            </if>
            <if test="feedbackTime != null ">
                and feedback_time = #{feedbackTime}
            </if>
            <if test="imageUrls != null  and imageUrls != ''">
                and image_urls = #{imageUrls}
            </if>
        </where>
    </select>

    <select id="selectMedicalServiceFeedbackById" parameterType="Long"
            resultMap="MedicalServiceFeedbackResult">
        <include refid="selectMedicalServiceFeedbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertMedicalServiceFeedback" parameterType="MedicalServiceFeedback" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_medical_service_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id,
            </if>
            <if test="doctorId != null">doctor_id,
            </if>
            <if test="doctorName != null and doctorName != ''">doctor_name,
            </if>
            <if test="feedbackContent != null and feedbackContent != ''">feedback_content,
            </if>
            <if test="feedbackTime != null">feedback_time,
            </if>
            <if test="imageUrls != null">image_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">#{workOrderId},
            </if>
            <if test="doctorId != null">#{doctorId},
            </if>
            <if test="doctorName != null and doctorName != ''">#{doctorName},
            </if>
            <if test="feedbackContent != null and feedbackContent != ''">#{feedbackContent},
            </if>
            <if test="feedbackTime != null">#{feedbackTime},
            </if>
            <if test="imageUrls != null">#{imageUrls},
            </if>
        </trim>
    </insert>

    <update id="updateMedicalServiceFeedback" parameterType="MedicalServiceFeedback">
        update t_medical_service_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id =
                #{workOrderId},
            </if>
            <if test="doctorId != null">doctor_id =
                #{doctorId},
            </if>
            <if test="doctorName != null and doctorName != ''">doctor_name =
                #{doctorName},
            </if>
            <if test="feedbackContent != null and feedbackContent != ''">feedback_content =
                #{feedbackContent},
            </if>
            <if test="feedbackTime != null">feedback_time =
                #{feedbackTime},
            </if>
            <if test="imageUrls != null">image_urls =
                #{imageUrls},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMedicalServiceFeedbackById" parameterType="Long">
        DELETE
        FROM t_medical_service_feedback
        WHERE id = #{id}
    </delete>

    <delete id="deleteMedicalServiceFeedbackByIds" parameterType="String">
        delete from t_medical_service_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
