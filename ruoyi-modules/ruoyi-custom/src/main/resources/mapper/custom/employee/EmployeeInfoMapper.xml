<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.employee.mapper.EmployeeInfoMapper">

    <resultMap type="EmployeeInfo" id="EmployeeInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="idCardNumber" column="id_card_number"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="nativePlace" column="native_place"/>
        <result property="householdType" column="household_type"/>
        <result property="university" column="university"/>
        <result property="graduationDate" column="graduation_date"/>
        <result property="highestEducation" column="highest_education"/>
        <result property="major" column="major"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="emergencyContact" column="emergency_contact"/>
        <result property="emergencyContactPhone" column="emergency_contact_phone"/>
        <result property="homeAddress" column="home_address"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="postIds" column="post_ids" typeHandler="com.ruoyi.custom.config.mybatis.handler.LongArrayTypeHandler"/>
        <result property="postNames" column="post_names"/>
        <result property="boardDate" column="board_date"/>
        <result property="regularizationDate" column="regularization_date"/>
        <result property="directSuperiorId" column="direct_superior_id"/>
        <result property="directSuperiorName" column="direct_superior_name"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="idPhotoUrl" column="id_photo_url"/>
        <result property="resumeUrl" column="resume_url"/>
        <result property="remarks" column="remarks"/>
        <result property="resignationRequestDate" column="resignation_request_date"/>
        <result property="expectedResignationDate" column="expected_resignation_date"/>
        <result property="resignationType" column="resignation_type"/>
        <result property="resignationReason" column="resignation_reason"/>
        <result property="resignationAttachmentUrl" column="resignation_attachment_url"/>
        <result property="userId" column="user_id"/>

        <result property="entryType" column="entry_type"/>

    </resultMap>

    <resultMap type="EmployeeInfo" id="EmployeeInfoResult2">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="idCardNumber" column="id_card_number"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="nativePlace" column="native_place"/>
        <result property="householdType" column="household_type"/>
        <result property="university" column="university"/>
        <result property="graduationDate" column="graduation_date"/>
        <result property="highestEducation" column="highest_education"/>
        <result property="major" column="major"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="emergencyContact" column="emergency_contact"/>
        <result property="emergencyContactPhone" column="emergency_contact_phone"/>
        <result property="homeAddress" column="home_address"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="postIds" column="post_ids" typeHandler="com.ruoyi.custom.config.mybatis.handler.LongArrayTypeHandler"/>
        <result property="postNames" column="post_names"/>
        <result property="boardDate" column="board_date"/>
        <result property="regularizationDate" column="regularization_date"/>
        <result property="directSuperiorId" column="direct_superior_id"/>
        <result property="directSuperiorName" column="direct_superior_name"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="idPhotoUrl" column="id_photo_url"/>
        <result property="resumeUrl" column="resume_url"/>
        <result property="remarks" column="remarks"/>
        <result property="resignationRequestDate" column="resignation_request_date"/>
        <result property="expectedResignationDate" column="expected_resignation_date"/>
        <result property="resignationType" column="resignation_type"/>
        <result property="resignationReason" column="resignation_reason"/>
        <result property="resignationAttachmentUrl" column="resignation_attachment_url"/>
        <result property="userId" column="user_id"/>

        <collection property="employeeEntryStatusList" ofType="EmployeeEntryStatus">
            <result property="id" column="t1_id"/>
            <result property="employeeId" column="t1_employee_id"/>
            <result property="type" column="t1_type"/>
            <result property="statusDate" column="t1_status_date"/>
            <result property="result" column="t1_result"/>
            <result property="remark" column="t1_remark"/>
            <result property="fileUrls" column="t1_file_urls"/>
        </collection>
    </resultMap>

    <sql id="selectEmployeeInfoVo">
        SELECT t.id,
               t.name,
               t.gender,
               t.date_of_birth,
               t.id_card_number,
               t.political_status,
               t.marital_status,
               t.native_place,
               t.household_type,
               t.university,
               t.graduation_date,
               t.highest_education,
               t.major,
               t.phone_number,
               t.emergency_contact,
               t.emergency_contact_phone,
               t.home_address,
               t.dept_id,
               t.dept_name,
               t.board_date,
               t.regularization_date,
               t.post_ids,
               t.post_names,
               t.direct_superior_id,
               t.direct_superior_name,
               t.bank_account,
               t.id_photo_url,
               t.resume_url,
               t.remarks,
               t.resignation_request_date,
               t.expected_resignation_date,
               t.resignation_type,
               t.resignation_reason,
               t.status,
               t.resignation_attachment_url,
               t.user_id

    </sql>

    <select id="selectEmployeeInfoList" parameterType="EmployeeInfo" resultMap="EmployeeInfoResult">
        <include refid="selectEmployeeInfoVo"/>
        ,t1.type AS entry_type
        FROM t_employee_info t
        LEFT JOIN (SELECT *,
        ROW_NUMBER() OVER (PARTITION BY employee_id ORDER BY status_date DESC) AS rn
        FROM t_employee_entry_status) t1 ON t1.employee_id = t.id AND t1.rn = 1
        <where>
            <if test="name != null  and name != ''">
                and t.name like concat('%', #{name}, '%')
            </if>
            <if test="gender != null  and gender != ''">
                and t.gender = #{gender}
            </if>
            <if test="dateOfBirth != null ">
                and t.date_of_birth = #{dateOfBirth}
            </if>
            <if test="idCardNumber != null  and idCardNumber != ''">
                and t.id_card_number = #{idCardNumber}
            </if>
            <if test="politicalStatus != null  and politicalStatus != ''">
                and t.political_status = #{politicalStatus}
            </if>
            <if test="maritalStatus != null  and maritalStatus != ''">
                and t.marital_status = #{maritalStatus}
            </if>
            <if test="nativePlace != null  and nativePlace != ''">
                and t.native_place = #{nativePlace}
            </if>
            <if test="householdType != null  and householdType != ''">
                and t.household_type = #{householdType}
            </if>
            <if test="university != null  and university != ''">
                and t.university = #{university}
            </if>
            <if test="graduationDate != null ">
                and t.graduation_date = #{graduationDate}
            </if>
            <if test="highestEducation != null  and highestEducation != ''">
                and t.highest_education = #{highestEducation}
            </if>
            <if test="major != null  and major != ''">
                and t.major = #{major}
            </if>
            <if test="phoneNumber != null  and phoneNumber != ''">
                and t.phone_number = #{phoneNumber}
            </if>
            <if test="emergencyContact != null  and emergencyContact != ''">
                and t.emergency_contact = #{emergencyContact}
            </if>
            <if test="emergencyContactPhone != null  and emergencyContactPhone != ''">
                and t.emergency_contact_phone = #{emergencyContactPhone}
            </if>
            <if test="homeAddress != null  and homeAddress != ''">
                and t.home_address = #{homeAddress}
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="deptName != null  and deptName != ''">
                and t.dept_name like concat('%', #{deptName}, '%')
            </if>
            <if test="postIds != null and postIds.length > 0">
                AND t.post_ids IS NOT NULL
                AND (
                <foreach collection="postIds" item="postId" separator=" AND ">
                    FIND_IN_SET(#{postId}, t.post_ids) > 0
                </foreach>
                )
            </if>
            <if test="directSuperiorId != null ">
                and t.direct_superior_id = #{directSuperiorId}
            </if>
            <if test="directSuperiorName != null  and directSuperiorName != ''">
                and t.direct_superior_name like concat('%', #{directSuperiorName}, '%')
            </if>
            <if test="bankAccount != null  and bankAccount != ''">
                and t.bank_account = #{bankAccount}
            </if>
            <if test="idPhotoUrl != null  and idPhotoUrl != ''">
                and t.id_photo_url = #{idPhotoUrl}
            </if>
            <if test="resumeUrl != null  and resumeUrl != ''">
                and t.resume_url = #{resumeUrl}
            </if>
            <if test="remarks != null  and remarks != ''">
                and t.remarks = #{remarks}
            </if>
            <if test="resignationRequestDate != null ">
                and t.resignation_request_date = #{resignationRequestDate}
            </if>
            <if test="expectedResignationDate != null ">
                and t.expected_resignation_date = #{expectedResignationDate}
            </if>
            <if test="resignationType != null  and resignationType != ''">
                and t.resignation_type = #{resignationType}
            </if>
            <if test="resignationReason != null  and resignationReason != ''">
                and t.resignation_reason = #{resignationReason}
            </if>
            <if test="resignationAttachmentUrl != null  and resignationAttachmentUrl != ''">
                and t.resignation_attachment_url = #{resignationAttachmentUrl}
            </if>
            <if test="userId != null">
                and t.user_id = #{userId}
            </if>

            <if test="params.startBoardDate != null and params.startBoardDate != '' and params.endBoardDate != null and params.endBoardDate != ''">
                and t.board_date between #{params.startBoardDate} and #{params.endBoardDate}
            </if>

            <if test="entryType != null and entryType != ''">
                and t1.type = #{entryType}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <select id="selectEmployeeInfoById" parameterType="Long"
            resultMap="EmployeeInfoResult2">
        <include refid="selectEmployeeInfoVo"/>,
        t1.id AS t1_id,
        t1.employee_id AS t1_employee_id,
        t1.type AS t1_type,
        t1.status_date AS t1_status_date,
        t1.result AS t1_result,
        t1.remark AS t1_remark,
        t1.file_urls AS t1_file_urls
        FROM t_employee_info t
        LEFT JOIN t_employee_entry_status t1 ON t1.employee_id = t.id
        where t.id = #{id}
        ORDER BY t1.status_date
    </select>

    <select id="selectDimissionEmployeeInfoList" resultType="com.ruoyi.custom.admin.employee.domain.EmployeeInfo">
        <include refid="selectEmployeeInfoVo"/>
        FROM t_employee_info t
        <where>
            and t.status IS NOT NULL
            <if test="name != null  and name != ''">
                and t.name like concat('%', #{name}, '%')
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="params.startResignationRequestDate != null and params.startResignationRequestDate != '' and params.endResignationRequestDate != null and params.endResignationRequestDate != ''">
                and t.resignation_request_date between #{params.startResignationRequestDate} and
                #{params.endResignationRequestDate}
            </if>
        </where>
    </select>

    <insert id="insertEmployeeInfo" parameterType="EmployeeInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_employee_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="name != null and name != ''">name,
            </if>
            <if test="gender != null and gender != ''">gender,
            </if>
            <if test="dateOfBirth != null">date_of_birth,
            </if>
            <if test="idCardNumber != null and idCardNumber != ''">id_card_number,
            </if>
            <if test="politicalStatus != null and politicalStatus != ''">political_status,
            </if>
            <if test="maritalStatus != null and maritalStatus != ''">marital_status,
            </if>
            <if test="nativePlace != null">native_place,
            </if>
            <if test="householdType != null">household_type,
            </if>
            <if test="university != null">university,
            </if>
            <if test="graduationDate != null">graduation_date,
            </if>
            <if test="highestEducation != null">highest_education,
            </if>
            <if test="major != null">major,
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,
            </if>
            <if test="emergencyContact != null">emergency_contact,
            </if>
            <if test="emergencyContactPhone != null">emergency_contact_phone,
            </if>
            <if test="homeAddress != null">home_address,
            </if>
            <if test="deptId != null">dept_id,
            </if>
            <if test="deptName != null and deptName != ''">dept_name,
            </if>
            <if test="boardDate != null">board_date,</if>
            <if test="regularizationDate != null">regularization_date,</if>
            <if test="postIds != null">post_ids,
            </if>
            <if test="postNames != null and postNames != ''">post_names,
            </if>
            <if test="directSuperiorId != null">direct_superior_id,
            </if>
            <if test="directSuperiorName != null">direct_superior_name,
            </if>
            <if test="bankAccount != null">bank_account,
            </if>
            <if test="idPhotoUrl != null">id_photo_url,
            </if>
            <if test="resumeUrl != null">resume_url,
            </if>
            <if test="remarks != null">remarks,
            </if>
            <if test="resignationRequestDate != null">resignation_request_date,
            </if>
            <if test="expectedResignationDate != null">expected_resignation_date,
            </if>
            <if test="resignationType != null">resignation_type,
            </if>
            <if test="resignationReason != null">resignation_reason,
            </if>
            <if test="status != null">status,
            </if>
            <if test="resignationAttachmentUrl != null">resignation_attachment_url,
            </if>
            <if test="userId != null">user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="gender != null and gender != ''">#{gender},
            </if>
            <if test="dateOfBirth != null">#{dateOfBirth},
            </if>
            <if test="idCardNumber != null and idCardNumber != ''">#{idCardNumber},
            </if>
            <if test="politicalStatus != null and politicalStatus != ''">#{politicalStatus},
            </if>
            <if test="maritalStatus != null and maritalStatus != ''">#{maritalStatus},
            </if>
            <if test="nativePlace != null">#{nativePlace},
            </if>
            <if test="householdType != null">#{householdType},
            </if>
            <if test="university != null">#{university},
            </if>
            <if test="graduationDate != null">#{graduationDate},
            </if>
            <if test="highestEducation != null">#{highestEducation},
            </if>
            <if test="major != null">#{major},
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},
            </if>
            <if test="emergencyContact != null">#{emergencyContact},
            </if>
            <if test="emergencyContactPhone != null">#{emergencyContactPhone},
            </if>
            <if test="homeAddress != null">#{homeAddress},
            </if>
            <if test="deptId != null">#{deptId},
            </if>
            <if test="deptName != null and deptName != ''">#{deptName},
            </if>
            <if test="boardDate != null">board_date,</if>
            <if test="regularizationDate != null">regularization_date,</if>
            <if test="postIds != null">#{postIds, typeHandler=com.ruoyi.custom.config.mybatis.handler.LongArrayTypeHandler},
            </if>
            <if test="postNames != null and postNames != ''">#{postNames},
            </if>
            <if test="directSuperiorId != null">#{directSuperiorId},
            </if>
            <if test="directSuperiorName != null">#{directSuperiorName},
            </if>
            <if test="bankAccount != null">#{bankAccount},
            </if>
            <if test="idPhotoUrl != null">#{idPhotoUrl},
            </if>
            <if test="resumeUrl != null">#{resumeUrl},
            </if>
            <if test="remarks != null">#{remarks},
            </if>
            <if test="resignationRequestDate != null">#{resignationRequestDate},
            </if>
            <if test="expectedResignationDate != null">#{expectedResignationDate},
            </if>
            <if test="resignationType != null">#{resignationType},
            </if>
            <if test="resignationReason != null">#{resignationReason},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="resignationAttachmentUrl != null">#{resignationAttachmentUrl},
            </if>
            <if test="userId != null">#{userId},
            </if>
        </trim>
    </insert>

    <update id="updateEmployeeInfo" parameterType="EmployeeInfo">
        update t_employee_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="gender != null and gender != ''">gender =
                #{gender},
            </if>
            <if test="dateOfBirth != null">date_of_birth =
                #{dateOfBirth},
            </if>
            <if test="idCardNumber != null and idCardNumber != ''">id_card_number =
                #{idCardNumber},
            </if>
            <if test="politicalStatus != null and politicalStatus != ''">political_status =
                #{politicalStatus},
            </if>
            <if test="maritalStatus != null and maritalStatus != ''">marital_status =
                #{maritalStatus},
            </if>
            <if test="nativePlace != null">native_place =
                #{nativePlace},
            </if>
            <if test="householdType != null">household_type =
                #{householdType},
            </if>
            <if test="university != null">university =
                #{university},
            </if>
            <if test="graduationDate != null">graduation_date =
                #{graduationDate},
            </if>
            <if test="highestEducation != null">highest_education =
                #{highestEducation},
            </if>
            <if test="major != null">major =
                #{major},
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number =
                #{phoneNumber},
            </if>
            <if test="emergencyContact != null">emergency_contact =
                #{emergencyContact},
            </if>
            <if test="emergencyContactPhone != null">emergency_contact_phone =
                #{emergencyContactPhone},
            </if>
            <if test="homeAddress != null">home_address =
                #{homeAddress},
            </if>
            <if test="deptId != null">dept_id =
                #{deptId},
            </if>
            <if test="deptName != null and deptName != ''">dept_name =
                #{deptName},
            </if>
            <if test="boardDate != null">board_date =
                #{boardDate},
            </if>
            <if test="regularizationDate != null">regularization_date =
                #{regularizationDate},
            </if>
            <if test="postIds != null">post_ids =
                #{postIds, typeHandler=com.ruoyi.custom.config.mybatis.handler.LongArrayTypeHandler},
            </if>
            <if test="postNames != null">post_names =
                #{postNames},
            </if>
            <if test="directSuperiorId != null">direct_superior_id =
                #{directSuperiorId},
            </if>
            <if test="directSuperiorName != null">direct_superior_name =
                #{directSuperiorName},
            </if>
            <if test="bankAccount != null">bank_account =
                #{bankAccount},
            </if>
            <if test="idPhotoUrl != null">id_photo_url =
                #{idPhotoUrl},
            </if>
            <if test="resumeUrl != null">resume_url =
                #{resumeUrl},
            </if>
            <if test="remarks != null">remarks =
                #{remarks},
            </if>
            <if test="resignationRequestDate != null">resignation_request_date =
                #{resignationRequestDate},
            </if>
            <if test="expectedResignationDate != null">expected_resignation_date =
                #{expectedResignationDate},
            </if>
            <if test="resignationType != null">resignation_type =
                #{resignationType},
            </if>
            <if test="resignationReason != null">resignation_reason =
                #{resignationReason},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="resignationAttachmentUrl != null">resignation_attachment_url =
                #{resignationAttachmentUrl},
            </if>
            <if test="userId != null">user_id =
                #{userId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeInfoById" parameterType="Long">
        DELETE
        FROM t_employee_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteEmployeeInfoByIds" parameterType="String">
        delete from t_employee_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getNextIdWithSimulation" resultType="java.lang.Long">
        <![CDATA[
        SET @next_id = NULL;
        START TRANSACTION;
        INSERT INTO t_employee_info (name)
        VALUES ('TEMP'); -- 插入临时记录
        SET @next_id = LAST_INSERT_ID(); -- 获取刚生成的ID
        ROLLBACK; -- 回滚事务，不保存数据
        SELECT @next_id AS next_id; -- 返回ID
        ]]>
    </select>

</mapper>
