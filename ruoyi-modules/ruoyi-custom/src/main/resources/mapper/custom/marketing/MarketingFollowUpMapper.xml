<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.MarketingFollowUpMapper">

    <resultMap type="MarketingFollowUp" id="MarketingFollowUpResult">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="planTime" column="plan_time"/>
        <result property="followUpItems" column="follow_up_items"/>
        <result property="followUpTime" column="follow_up_time"/>
        <result property="followUpStatus" column="follow_up_status"/>
        <result property="followUpSituation" column="follow_up_situation"/>
        <result property="followerId" column="follower_id"/>
        <result property="follower" column="follower"/>

        <association property="marketingCustomerInfo" javaType="MarketingCustomerInfo">
            <result property="id" column="customer_id"/>
            <result property="consultantName" column="consultant_name"/>
            <result property="relationshipWithElder" column="relationship_with_elder"/>
            <result property="consultantPhone" column="consultant_phone"/>
            <result property="consultationDate" column="consultation_date"/>
            <result property="consultationMethod" column="consultation_method"/>
            <result property="sourceChannel" column="source_channel"/>
            <result property="marketer" column="marketer"/>
            <result property="marketerId" column="marketer_id"/>
            <result property="consultationContent" column="consultation_content"/>
            <result property="elderName" column="elder_name"/>
            <result property="elderGender" column="elder_gender"/>
            <result property="elderAge" column="elder_age"/>
            <result property="elderBirthday" column="elder_birthday"/>
            <result property="elderPhone" column="elder_phone"/>
            <result property="idCardNumber" column="id_card_number"/>
            <result property="nation" column="nation"/>
            <result property="maritalStatus" column="marital_status"/>
            <result property="residenceStatus" column="residence_status"/>
            <result property="homeAddress" column="home_address"/>
            <result property="remarks" column="remarks"/>
            <result property="customerType" column="customer_type"/>
            <result property="failedTime" column="failed_time"/>
        </association>
    </resultMap>

    <sql id="selectMarketingFollowUpVo">
        SELECT t.id,
               t.customer_id,
               t.plan_time,
               t.follow_up_items,
               t.follow_up_time,
               t.follow_up_status,
               t.follow_up_situation,
               t.follower_id,
               t.follower,

               t1.consultant_name,
               t1.relationship_with_elder,
               t1.consultant_phone,
               t1.consultation_date,
               t1.consultation_method,
               t1.source_channel,
               t1.marketer,
               t1.marketer_id,
               t1.consultation_content,
               t1.elder_name,
               t1.elder_gender,
               t1.elder_age,
               t1.elder_birthday,
               t1.elder_phone,
               t1.id_card_number,
               t1.nation,
               t1.marital_status,
               t1.residence_status,
               t1.home_address,
               t1.remarks,
               t1.customer_type,
               t1.failed_time

        FROM t_marketing_follow_up t
                 LEFT JOIN t_marketing_customer_info t1 ON t.customer_id = t1.id
    </sql>

    <select id="selectMarketingFollowUpList" parameterType="MarketingFollowUp" resultMap="MarketingFollowUpResult">
        <include refid="selectMarketingFollowUpVo"/>
        <where>
            <if test="customerId != null ">
                and t.customer_id = #{customerId}
            </if>
            <if test="params.startPlanTime != null and params.startPlanTime != '' and params.endPlanTime != null and params.endPlanTime != ''">
                and t.plan_time between #{params.startPlanTime} and #{params.endPlanTime}
            </if>
            <if test="followUpItems != null  and followUpItems != ''">
                and t.follow_up_items = #{followUpItems}
            </if>
            <if test="followUpTime != null ">
                and t.follow_up_time = #{followUpTime}
            </if>
            <if test="followUpStatus != null  and followUpStatus != ''">
                and t.follow_up_status = #{followUpStatus}
            </if>
            <if test="followUpSituation != null  and followUpSituation != ''">
                and t.follow_up_situation = #{followUpSituation}
            </if>
            <if test="params.elderName != null and params.elderName != ''">
                and t1.elder_name like concat('%', #{params.elderName}, '%')
            </if>
            <if test="params.marketer != null and params.marketer != ''">
                and t1.marketer like concat('%', #{params.marketer}, '%')
            </if>
                <if test="params.marketerId != null">
                and t1.marketer_id = #{params.marketerId}
            </if>
        </where>
        ORDER BY
        CASE follow_up_status
        WHEN '0' THEN 1
        WHEN '2' THEN 2
        WHEN '1' THEN 3
        ELSE 4
        END ASC,
        plan_time DESC
    </select>

    <select id="selectMarketingFollowUpById" parameterType="Long"
            resultMap="MarketingFollowUpResult">
        <include refid="selectMarketingFollowUpVo"/>
        where t.id = #{id}
    </select>

    <select id="selectMarketingFollowUpCount" resultType="java.lang.Long">
        select count(1) from t_marketing_follow_up t
        LEFT JOIN t_marketing_customer_info t1 on t.customer_id = t1.id
        <where>
            <if test="followUpStatus != null and followUpStatus != ''">
                and t.follow_up_status = #{followUpStatus}
            </if>
            <if test="params.marketerId != null">
                and t1.marketer_id = #{params.marketerId}
            </if>
        </where>
    </select>

    <insert id="insertMarketingFollowUp" parameterType="MarketingFollowUp" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_marketing_follow_up
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,
            </if>
            <if test="planTime != null">plan_time,
            </if>
            <if test="followUpItems != null">follow_up_items,
            </if>
            <if test="followUpTime != null">follow_up_time,
            </if>
            <if test="followUpStatus != null and followUpStatus != ''">follow_up_status,
            </if>
            <if test="followUpSituation != null">follow_up_situation,
            </if>
            <if test="followerId != null">follower_id,
            </if>
            <if test="follower != null">follower,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},
            </if>
            <if test="planTime != null">#{planTime},
            </if>
            <if test="followUpItems != null">#{followUpItems},
            </if>
            <if test="followUpTime != null">#{followUpTime},
            </if>
            <if test="followUpStatus != null and followUpStatus != ''">#{followUpStatus},
            </if>
            <if test="followUpSituation != null">#{followUpSituation},
            </if>
            <if test="followerId != null">#{followerId},
            </if>
            <if test="follower != null">#{follower},
            </if>
        </trim>
    </insert>

    <insert id="insertBatchMarketingFollowUp">
        insert into t_marketing_follow_up
        (customer_id,plan_time,follow_up_items,follow_up_time,follow_up_status,follow_up_situation,follower_id,follower)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId},#{item.planTime},#{item.followUpItems},#{item.followUpTime},#{item.followUpStatus},#{item.followUpSituation},#{item.followerId},#{item.follower})
        </foreach>
    </insert>

    <update id="updateMarketingFollowUp" parameterType="MarketingFollowUp">
        update t_marketing_follow_up
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id =
                #{customerId},
            </if>
            <if test="planTime != null">plan_time =
                #{planTime},
            </if>
            <if test="followUpItems != null">follow_up_items =
                #{followUpItems},
            </if>
            <if test="followUpTime != null">follow_up_time =
                #{followUpTime},
            </if>
            <if test="followUpStatus != null and followUpStatus != ''">follow_up_status =
                #{followUpStatus},
            </if>
            <if test="followUpSituation != null">follow_up_situation =
                #{followUpSituation},
            </if>
            <if test="followerId != null">follower_id =
                #{followerId},
            </if>
            <if test="follower != null">follower =
                #{follower},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMarketingFollowUpById" parameterType="Long">
        DELETE
        FROM t_marketing_follow_up
        WHERE id = #{id}
    </delete>

    <delete id="deleteMarketingFollowUpByIds" parameterType="String">
        delete from t_marketing_follow_up where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMarketingFollowUpCustomId" parameterType="Long">
        DELETE
        FROM t_marketing_follow_up
        WHERE customer_id = #{id}
          AND follow_up_status = '0'
    </delete>
</mapper>
