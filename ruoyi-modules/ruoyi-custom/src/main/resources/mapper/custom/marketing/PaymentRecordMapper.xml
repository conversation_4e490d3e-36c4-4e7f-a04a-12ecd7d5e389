<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.PaymentRecordMapper">

    <resultMap type="PaymentRecord" id="PaymentRecordResult">
        <result property="id" column="id"/>
        <result property="billNumber" column="bill_number"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="elderlyPhone" column="elderly_phone"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="contractCycle" column="contract_cycle"/>
        <result property="careLevel" column="care_level"/>
        <result property="bedName" column="bed_name"/>
        <result property="leaveDuration" column="leave_duration"/>
        <result property="leaveDates" column="leave_dates"/>
        <result property="totalCost" column="total_cost"/>
        <result property="totalPaidCost" column="total_paid_cost"/>
        <result property="paidDetails" column="paid_details" typeHandler="com.ruoyi.custom.config.mybatis.handler.PaidDetailListJsonTypeHandler"/>
        <result property="refundCost" column="refund_cost"/>
        <result property="offerCost" column="offer_cost"/>
        <result property="accountAddCost" column="account_add_cost"/>
        <result property="paymentTime" column="payment_time"/>
        <result property="feeType" column="fee_type"/>
        <result property="paymentStatus" column="payment_status"/>
        <result property="remark" column="remark"/>
        <result property="dischargeDate" column="discharge_date"/>
        <result property="details" column="details"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.DetailListJsonTypeHandler"/>
    </resultMap>

    <sql id="selectPaymentRecordVo">
        SELECT id,
               bill_number,
               contract_number,
               elderly_id,
               elderly_name,
               elderly_phone,
               contract_start_date,
               contract_end_date,
               contract_cycle,
               care_level,
               bed_name,
               leave_duration,
               leave_dates,
               total_cost,
               (SELECT IFNULL(SUM(JSON_EXTRACT(paid_detail.value, '$.paidCost')), 0)
                FROM JSON_TABLE(
                    paid_details,
                    '$[*]' COLUMNS (
                        value JSON PATH '$'
                    )
                ) paid_detail
               ) AS total_paid_cost,
               paid_details,
               refund_cost,
               offer_cost,
               account_add_cost,
               payment_time,
               fee_type,
               payment_status,
               remark,
               discharge_date,
               details
        FROM t_payment_record
    </sql>

    <select id="selectPaymentRecordList" parameterType="PaymentRecord" resultMap="PaymentRecordResult">
        <include refid="selectPaymentRecordVo"/>
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="billNumber != null and billNumber != ''">
                and bill_number = #{billNumber}
            </if>
            <if test="contractNumber != null and contractNumber != ''">
                and contract_number = #{contractNumber}
            </if>
            <if test="elderlyId != null  and elderlyId != ''">
                and elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null  and elderlyName != ''">
                and elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="elderlyPhone != null  and elderlyPhone != ''">
                and elderly_phone = #{elderlyPhone}
            </if>
            <if test="contractStartDate != null ">
                and contract_start_date = #{contractStartDate}
            </if>
            <if test="contractEndDate != null ">
                and contract_end_date = #{contractEndDate}
            </if>
            <if test="contractCycle != null ">
                and contract_cycle = #{contractCycle}
            </if>
            <if test="careLevel != null  and careLevel != ''">
                and care_level = #{careLevel}
            </if>
            <if test="bedName != null  and bedName != ''">
                and bed_name like concat('%', #{bedName}, '%')
            </if>
            <if test="totalCost != null ">
                and total_cost = #{totalCost}
            </if>
            <if test="accountAddCost != null ">
                and account_add_cost = #{accountAddCost}
            </if>
            <if test="paymentTime != null ">
                and payment_time = #{paymentTime}
            </if>
            <if test="params.startPaymentTime != null and params.endPaymentTime != null">
                and date_format(payment_time,'%y-%m-%d') between date_format(#{params.startPaymentTime} ,'%y-%m-%d') and
                date_format(#{params.endPaymentTime} ,'%y-%m-%d')
            </if>
            <if test="feeType != null and feeType != ''">
                and fee_type = #{feeType}
            </if>
            <if test="paymentStatus != null and paymentStatus != ''">
                and payment_status = #{paymentStatus}
            </if>
            <if test="details != null  and details != ''">
                and details = #{details}
            </if>
        </where>
        ORDER BY payment_time DESC
    </select>

    <select id="selectPaymentRecordById" parameterType="String"
            resultMap="PaymentRecordResult">
        <include refid="selectPaymentRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectPaymentRecordByBillNumber" parameterType="String"
            resultMap="PaymentRecordResult">
        <include refid="selectPaymentRecordVo"/>
        where bill_number = #{billNumber}
    </select>

    <insert id="insertPaymentRecord" parameterType="PaymentRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_payment_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="billNumber != null and billNumber != ''">bill_number,
            </if>
            <if test="contractNumber != null and contractNumber != ''">contract_number,
            </if>
            <if test="elderlyId != null and elderlyId != ''">elderly_id,
            </if>
            <if test="elderlyName != null">elderly_name,
            </if>
            <if test="elderlyPhone != null">elderly_phone,
            </if>
            <if test="contractStartDate != null">contract_start_date,
            </if>
            <if test="contractEndDate != null">contract_end_date,
            </if>
            <if test="contractCycle != null">contract_cycle,
            </if>
            <if test="careLevel != null">care_level,
            </if>
            <if test="bedName != null">bed_name,
            </if>
            <if test="leaveDuration != null">leave_duration,
            </if>
            <if test="leaveDates != null">leave_dates,
            </if>
            <if test="totalCost != null">total_cost,
            </if>
            <if test="paidDetails != null">paid_details,
            </if>
            <if test="refundCost != null">refund_cost,
            </if>
            <if test="offerCost != null">offer_cost,
            </if>
            <if test="accountAddCost != null">account_add_cost,
            </if>
            <if test="paymentTime != null">payment_time,
            </if>
            <if test="feeType != null">fee_type,
            </if>
            <if test="paymentStatus != null">payment_status,
            </if>
            <if test="remark != null">remark,
            </if>
            <if test="dischargeDate != null">discharge_date,
            </if>
            <if test="details != null">details,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="billNumber != null and billNumber != ''">#{billNumber},
            </if>
            <if test="contractNumber != null and contractNumber != ''">#{contractNumber},
            </if>
            <if test="elderlyId != null and elderlyId != ''">#{elderlyId},
            </if>
            <if test="elderlyName != null">#{elderlyName},
            </if>
            <if test="elderlyPhone != null">#{elderlyPhone},
            </if>
            <if test="contractStartDate != null">#{contractStartDate},
            </if>
            <if test="contractEndDate != null">#{contractEndDate},
            </if>
            <if test="contractCycle != null">#{contractCycle},
            </if>
            <if test="careLevel != null">#{careLevel},
            </if>
            <if test="bedName != null">#{bedName},
            </if>
            <if test="leaveDuration != null">#{leaveDuration},
            </if>
            <if test="leaveDates != null">#{leaveDates},
            </if>
            <if test="totalCost != null">#{totalCost},
            </if>
            <if test="paidDetails != null">
                #{paidDetails,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.PaidDetailListJsonTypeHandler},
            </if>
            <if test="refundCost != null">#{refundCost},
            </if>
            <if test="offerCost != null">#{offerCost},
            </if>
            <if test="accountAddCost != null">#{accountAddCost},
            </if>
            <if test="paymentTime != null">#{paymentTime},
            </if>
            <if test="feeType != null">#{feeType},
            </if>
            <if test="paymentStatus != null">#{paymentStatus},
            </if>
            <if test="remark != null">#{remark},
            </if>
            <if test="dischargeDate != null">#{dischargeDate},
            </if>
            <if test="details != null">
                #{details,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.DetailListJsonTypeHandler},
            </if>
        </trim>
    </insert>

    <update id="updatePaymentRecord" parameterType="PaymentRecord">
        update t_payment_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="billNumber != null and billNumber != ''">bill_number =
                #{billNumber},
            </if>
            <if test="contractNumber != null and contractNumber != ''">contract_number =
                #{contractNumber},
            </if>
            <if test="elderlyId != null and elderlyId != ''">elderly_id =
                #{elderlyId},
            </if>
            <if test="elderlyName != null">elderly_name =
                #{elderlyName},
            </if>
            <if test="elderlyPhone != null">elderly_phone =
                #{elderlyPhone},
            </if>
            <if test="contractStartDate != null">contract_start_date =
                #{contractStartDate},
            </if>
            <if test="contractEndDate != null">contract_end_date =
                #{contractEndDate},
            </if>
            <if test="contractCycle != null">contract_cycle =
                #{contractCycle},
            </if>
            <if test="careLevel != null">care_level =
                #{careLevel},
            </if>
            <if test="bedName != null">bed_name =
                #{bedName},
            </if>
            <if test="leaveDuration != null">leave_duration =
                #{leaveDuration},
            </if>
            <if test="leaveDates != null">leave_dates =
                #{leaveDates},
            </if>
            <if test="totalCost != null">total_cost =
                #{totalCost},
            </if>
            <if test="paidDetails != null">
                paid_details =
                #{paidDetails,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.PaidDetailListJsonTypeHandler},
            </if>
            <if test="refundCost != null">refund_cost =
                #{refundCost},
            </if>
            <if test="offerCost != null">offer_cost =
                #{offerCost},
            </if>
            <if test="accountAddCost != null">account_add_cost =
                #{accountAddCost},
            </if>
            <if test="paymentTime != null">payment_time =
                #{paymentTime},
            </if>
            <if test="feeType != null">fee_type =
                #{feeType},
            </if>
            <if test="paymentStatus != null">payment_status =
                #{paymentStatus},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
            <if test="dischargeDate != null">discharge_date =
                #{dischargeDate},
            </if>
            <if test="details != null">details =
                #{details,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.DetailListJsonTypeHandler},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentRecordById" parameterType="String">
        DELETE
        FROM t_payment_record
        WHERE id = #{id}
    </delete>

    <delete id="deletePaymentRecordByIds" parameterType="String">
        delete from t_payment_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectLastInfoByContractNumber"
            resultMap="PaymentRecordResult">
        <include refid="selectPaymentRecordVo"/>
        WHERE contract_number = #{contractNumber}
        ORDER BY payment_time DESC
        LIMIT 1
    </select>

    <select id="paymentRemindList" resultType="com.ruoyi.custom.admin.marketing.resp.PaymentRemindResp">
        SELECT p.id,
               p.contract_number                                 AS contractNumber,
               p.elderly_id                                      AS elderlyId,
               p.elderly_name                                    AS elderlyName,
               p.contract_start_date                             AS contractStartDate,
               p.contract_end_date                               AS contractEndDate,
               MIN(FROM_UNIXTIME(jt.estimatedExpiryDate / 1000)) AS estimatedExpiryDate
        FROM t_payment_record p
                 JOIN (
            -- 获取每个 contract_number 的最大 payment_time
            SELECT contract_number, MAX(payment_time) AS max_payment_time
            FROM t_payment_record
            WHERE details IS NOT NULL
              AND JSON_VALID(details)
            GROUP BY contract_number) latest
                      ON p.contract_number = latest.contract_number AND p.payment_time = latest.max_payment_time
                 JOIN
             JSON_TABLE(
                     p.details,
                     '$[*]' COLUMNS (
                         estimatedExpiryDate VARCHAR(20) PATH '$.estimatedExpiryDate'
                         )
             ) AS jt ON jt.estimatedExpiryDate IS NOT NULL AND jt.estimatedExpiryDate != ''
        <where>
            fee_type = 1
            <if test="elderlyName != null and elderlyName != ''">
                AND p.elderly_name LIKE CONCAT('%', #{elderlyName}, '%')
            </if>
            <if test="params.startEstimatedExpiryDate != null and params.endEstimatedExpiryDate != null">
                AND date_format(FROM_UNIXTIME(jt.estimatedExpiryDate / 1000),'%Y-%m-%d') BETWEEN date_format(#{params.startEstimatedExpiryDate},'%Y-%m-%d') AND date_format(#{params.endEstimatedExpiryDate},'%y-%m-%d')
            </if >
            <if test="params.estimatedExpiryDate != null and params.estimatedExpiryDate != ''">
                <![CDATA[
                    and date_format(FROM_UNIXTIME(jt.estimatedExpiryDate / 1000),'%Y-%m-%d')  <  date_format(#{params.estimatedExpiryDate},'%Y-%m-%d')
                ]]>

            </if>
        </where>
        GROUP BY p.id,
                 p.elderly_id,
                 p.elderly_name,
                 p.contract_start_date,
                 p.contract_end_date
        ORDER BY estimatedExpiryDate ASC
    </select>

    <select id="feeStatistics" resultType="com.ruoyi.custom.admin.marketing.resp.FeeStatisticsResp">
        SELECT
        p.id,
        p.contract_number AS contractNumber,
        p.elderly_name AS elderlyName,
        p.elderly_phone AS elderlyPhone,
        P.bed_name AS bedNumber,
        p.fee_type AS feeType,
        P.payment_time AS paymentTime,
        detail.typeName AS paymentType,
        detail.paymentAmount,
        detail.remarks AS remark
        FROM t_payment_record p
        JOIN JSON_TABLE(
        p.details,
        '$[*]' COLUMNS (
        type VARCHAR(50) PATH '$.type',
        typeName VARCHAR(100) PATH '$.typeName',
        paymentAmount DECIMAL(10,2) PATH '$.paymentAmount',
        remarks VARCHAR(255) PATH '$.remarks'
        )
        ) AS detail
        <where>
            p.details IS NOT NULL AND JSON_VALID(p.details) AND detail.paymentAmount IS NOT NULL AND detail.paymentAmount != 0
            <if test="id != null and id != ''">
                AND p.id = #{id}
            </if>
            <if test="elderlyName != null and elderlyName != ''">
                AND p.elderly_name LIKE CONCAT('%', #{elderlyName}, '%')
            </if>
            <if test="elderlyPhone != null and elderlyPhone != ''">
                AND p.elderly_phone LIKE CONCAT('%', #{elderlyPhone}, '%')
            </if>
            <if test="feeType != null and feeType != ''">
                AND p.fee_type = #{feeType}
            </if>
            <if test="params.startPaymentTime != null and params.endPaymentTime != null">
                AND date_format(p.payment_time, '%y-%m-%d') BETWEEN date_format(#{params.startPaymentTime} ,'%y-%m-%d')
                AND date_format(#{params.endPaymentTime} ,'%y-%m-%d')
            </if>
            <if test="paymentType != null and paymentType != ''">
                AND detail.typeName like CONCAT('%', #{paymentType}, '%')
            </if>
        </where>
        ORDER BY p.id DESC
    </select>

    <select id="selectMaxId" resultType="java.lang.String">
        SELECT MAX(id)
        FROM t_payment_record
    </select>

    <!-- 收入分类统计 -->
    <select id="getIncomeClassification" resultType="java.util.Map">
        SELECT * FROM (
            -- 医疗保障金
            SELECT '医疗保障金' AS category, COALESCE(SUM(detail.paymentAmount), 0) AS totalAmount
            FROM t_payment_record p
            JOIN JSON_TABLE(
                p.details,
                '$[*]' COLUMNS (
                    type VARCHAR(50) PATH '$.type',
                    paymentAmount DECIMAL(10,2) PATH '$.paymentAmount'
                )
            ) AS detail ON detail.type = 'medicalSecurityFee'
            WHERE p.fee_type IN ('1', '2')
              AND detail.paymentAmount IS NOT NULL
              AND detail.paymentAmount > 0

            UNION ALL

            -- 床位费
            SELECT '床位费' AS category, COALESCE(SUM(detail.paymentAmount), 0) AS totalAmount
            FROM t_payment_record p
            JOIN JSON_TABLE(
                p.details,
                '$[*]' COLUMNS (
                    type VARCHAR(50) PATH '$.type',
                    paymentAmount DECIMAL(10,2) PATH '$.paymentAmount'
                )
            ) AS detail ON detail.type = 'bedFee'
            WHERE p.fee_type IN ('1', '2')
              AND detail.paymentAmount IS NOT NULL
              AND detail.paymentAmount > 0

            UNION ALL

            -- 护理费
            SELECT '护理费' AS category, COALESCE(SUM(detail.paymentAmount), 0) AS totalAmount
            FROM t_payment_record p
            JOIN JSON_TABLE(
                p.details,
                '$[*]' COLUMNS (
                    type VARCHAR(50) PATH '$.type',
                    paymentAmount DECIMAL(10,2) PATH '$.paymentAmount'
                )
            ) AS detail ON detail.type = 'careFee'
            WHERE p.fee_type IN ('1', '2')
              AND detail.paymentAmount IS NOT NULL
              AND detail.paymentAmount > 0

            UNION ALL

            -- 空调费
            SELECT '空调费' AS category, COALESCE(SUM(detail.paymentAmount), 0) AS totalAmount
            FROM t_payment_record p
            JOIN JSON_TABLE(
                p.details,
                '$[*]' COLUMNS (
                    type VARCHAR(50) PATH '$.type',
                    paymentAmount DECIMAL(10,2) PATH '$.paymentAmount'
                )
            ) AS detail ON detail.type = 'acFee'
            WHERE p.fee_type IN ('1', '2')
              AND detail.paymentAmount IS NOT NULL
              AND detail.paymentAmount > 0

            UNION ALL

            -- 增值服务费
            SELECT '增值服务费' AS category, COALESCE(SUM(bill.fee), 0) AS totalAmount
            FROM t_user_value_added_service_bill bill
            WHERE bill.is_confirm = '1'

            UNION ALL

            -- 选购服务费（暂时设为0）
            SELECT '选购服务费' AS category, 0 AS totalAmount
        ) AS income_stats
        ORDER BY
            CASE category
                WHEN '医疗保障金' THEN 1
                WHEN '床位费' THEN 2
                WHEN '护理费' THEN 3
                WHEN '空调费' THEN 4
                WHEN '增值服务费' THEN 5
                WHEN '选购服务费' THEN 6
                ELSE 7
            END
    </select>

    <!-- 各缴费方式累计缴费统计 -->
    <select id="getPaymentMethodStatistics" resultType="java.util.Map">
        SELECT
            CASE paidDetail.paymentMethod
                WHEN '0' THEN '现金'
                WHEN '1' THEN '微信'
                WHEN '2' THEN '支付宝'
                ELSE CONCAT('未知(', paidDetail.paymentMethod, ')')
            END AS paymentMethod,
            COALESCE(SUM(paidDetail.paidCost), 0) AS totalAmount
        FROM t_payment_record p
        JOIN JSON_TABLE(
            p.paid_details,
            '$[*]' COLUMNS (
                paymentMethod VARCHAR(10) PATH '$.paymentMethod',
                paidCost DECIMAL(10,2) PATH '$.paidCost'
            )
        ) AS paidDetail
        WHERE p.paid_details IS NOT NULL
          AND JSON_VALID(p.paid_details)
          AND paidDetail.paidCost IS NOT NULL
          AND paidDetail.paidCost > 0
          AND paidDetail.paymentMethod IS NOT NULL
        GROUP BY paidDetail.paymentMethod
        ORDER BY
            CASE paidDetail.paymentMethod
                WHEN '0' THEN 0  -- 现金
                WHEN '1' THEN 1  -- 微信
                WHEN '2' THEN 2  -- 支付宝
            END
    </select>

    <!-- 缴费管理列表ResultMap -->
    <resultMap type="com.ruoyi.custom.admin.marketing.resp.PaymentManagementResp" id="PaymentManagementResult">
        <result property="paymentId" column="payment_id"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="elderlyPhone" column="elderly_phone"/>
        <result property="contractSignDate" column="contract_sign_date"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="contractResponsiblePerson" column="contract_responsible_person"/>
        <result property="type" column="type"/>
        <result property="paymentStatus" column="payment_status"/>
    </resultMap>

    <!-- 离园管理列表ResultMap -->
    <resultMap type="com.ruoyi.custom.admin.marketing.resp.PaymentSettlementManagementResp" id="PaymentSettlementManagementResult">
        <result property="paymentSettlementId" column="payment_settlement_id"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="customerName" column="customer_name"/>
        <result property="elderlyPhone" column="elderly_phone"/>
        <result property="liveStatus" column="live_status"/>
        <result property="careLevel" column="care_level"/>
        <result property="roomType" column="room_type"/>
        <result property="bedNumber" column="bed_number"/>
        <result property="paymentStatus" column="payment_status"/>
    </resultMap>

    <!-- 查询缴费管理列表 -->
    <select id="selectPaymentManagementList" parameterType="PaymentRecord" resultMap="PaymentManagementResult">
        SELECT * FROM (
            -- 入住和续签：数据来源PaymentRecord，且feeType=1
            SELECT
                pr.id as payment_id,
                pr.contract_number,
                pr.elderly_name,
                pr.elderly_phone,
                ci.contract_sign_date,
                pr.contract_start_date,
                pr.contract_end_date,
                ci.contract_entry_user as contract_responsible_person,
                CASE
                    WHEN pr.contract_number LIKE '%续%' THEN '2'
                    ELSE '1'
                END as type,
                pr.payment_status,
                pr.payment_time as sort_time
            FROM t_payment_record pr
            LEFT JOIN t_contract_info ci ON pr.contract_number = ci.contract_number
            WHERE pr.fee_type = '1'

            UNION ALL

            -- 更换套餐：数据来源PaymentChangeRecord
            SELECT
                pcr.id as payment_id,
                pcr.contract_number,
                pcr.elderly_name,
                ei.phone as elderly_phone,
                ci.contract_sign_date,
                pcr.contract_start_date,
                pcr.contract_end_date,
                ci.contract_entry_user as contract_responsible_person,
                '3' as type,
                COALESCE(pcr.payment_status, '1') as payment_status,
                pcr.create_time as sort_time
            FROM t_payment_change_record pcr
            LEFT JOIN t_contract_info ci ON pcr.contract_number = ci.contract_number
            LEFT JOIN t_elderly_people_info ei ON pcr.elderly_id = ei.id

            UNION ALL

            -- 离园：数据来源PaymentRecord，且feeType=2
            SELECT
                pr.id as payment_id,
                pr.contract_number,
                pr.elderly_name,
                pr.elderly_phone,
                ci.contract_sign_date,
                pr.contract_start_date,
                pr.contract_end_date,
                ci.contract_entry_user as contract_responsible_person,
                '4' as type,
                pr.payment_status,
                pr.payment_time as sort_time
            FROM t_payment_record pr
            LEFT JOIN t_contract_info ci ON pr.contract_number = ci.contract_number
            WHERE pr.fee_type = '2'
        ) AS management_list
        <where>
            <if test="contractNumber != null and contractNumber != ''">
                AND contract_number like concat('%', #{contractNumber}, '%')
            </if>
            <if test="elderlyName != null and elderlyName != ''">
                AND elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="elderlyPhone != null and elderlyPhone != ''">
                AND elderly_phone like concat('%', #{elderlyPhone}, '%')
            </if>
            <if test="params != null">
                <if test="params.beginContractEndDate != null and params.beginContractEndDate != ''">
                    AND date_format(contract_end_date,'%Y-%m-%d') &gt;= date_format(#{params.beginContractEndDate},'%Y-%m-%d')
                </if>
                <if test="params.endContractEndDate != null and params.endContractEndDate != ''">
                    AND date_format(contract_end_date,'%Y-%m-%d') &lt;= date_format(#{params.endContractEndDate},'%Y-%m-%d')
                </if>
                <if test="params.type != null and params.type != ''">
                    AND type = #{params.type}
                </if>
            </if>
        </where>
        ORDER BY sort_time DESC
    </select>

    <!-- 查询离园管理列表 -->
    <select id="selectPaymentSettlementManagementList" parameterType="PaymentSettlementRecordSummary" resultMap="PaymentSettlementManagementResult">
        SELECT
            psrs.payment_record_id as payment_settlement_id,
            psrs.contract_number,
            psrs.customer_name,
            pr.elderly_phone,
            psrs.live_status,
            psrs.care_level,
            psrs.room_type,
            psrs.bed_number,
            pr.payment_status
        FROM t_payment_settlement_record_summary psrs
        LEFT JOIN t_payment_record pr ON psrs.payment_record_id = pr.id
        WHERE psrs.del_flag = '0'
        <if test="contractNumber != null and contractNumber != ''">
            AND psrs.contract_number like concat('%', #{contractNumber}, '%')
        </if>
        <if test="customerName != null and customerName != ''">
            AND psrs.customer_name like concat('%', #{customerName}, '%')
        </if>
        <if test="params.elderlyPhone != null and params.elderlyPhone != ''">
            AND pr.elderly_phone like concat('%', #{params.elderlyPhone}, '%')
        </if>
        <if test="params.paymentStatus != null and params.paymentStatus != ''">
            AND pr.payment_status = #{params.paymentStatus}
        </if>
        <if test="params.beginDischargeDate != null and params.beginDischargeDate != '' and params.endDischargeDate != null and params.endDischargeDate != ''">
            AND date_format(psrs.discharge_date,'%Y-%m-%d') between date_format(#{params.beginDischargeDate},'%Y-%m-%d') and date_format(#{params.endDischargeDate},'%Y-%m-%d')
        </if>
        ORDER BY psrs.create_time DESC
    </select>

</mapper>
