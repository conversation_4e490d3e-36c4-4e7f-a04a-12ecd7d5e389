<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.PaymentRecordImageMapper">

    <resultMap type="PaymentRecordImage" id="PaymentRecordImageResult">
        <result property="id" column="id"/>
        <result property="tempGenerationId" column="temp_generation_id"/>
        <result property="originalId" column="original_id"/>
        <result property="billNumber" column="bill_number"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="elderlyPhone" column="elderly_phone"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="contractCycle" column="contract_cycle"/>
        <result property="careLevel" column="care_level"/>
        <result property="bedName" column="bed_name"/>
        <result property="leaveDuration" column="leave_duration"/>
        <result property="leaveDates" column="leave_dates"/>
        <result property="refundCost" column="refund_cost"/>
        <result property="offerCost" column="offer_cost"/>
        <result property="totalCost" column="total_cost"/>
        <result property="paidDetails" column="paid_details" typeHandler="com.ruoyi.common.core.utils.MybatisJsonTypeHandler"/>
        <result property="accountAddCost" column="account_add_cost"/>
        <result property="paymentTime" column="payment_time"/>
        <result property="feeType" column="fee_type"/>
        <result property="paymentStatus" column="payment_status"/>
        <result property="remark" column="remark"/>
        <result property="dischargeDate" column="discharge_date"/>
        <result property="details" column="details" typeHandler="com.ruoyi.common.core.utils.MybatisJsonTypeHandler"/>
    </resultMap>

    <sql id="selectPaymentRecordImageVo">
        select id, temp_generation_id, original_id, bill_number, contract_number, elderly_id, elderly_name, elderly_phone, contract_start_date, contract_end_date, contract_cycle, care_level, bed_name, leave_duration, leave_dates, refund_cost, offer_cost, total_cost, paid_details, account_add_cost, payment_time, fee_type, payment_status, remark, discharge_date, details from t_payment_record_image
    </sql>

    <select id="selectPaymentRecordImageById" parameterType="String" resultMap="PaymentRecordImageResult">
        <include refid="selectPaymentRecordImageVo"/>
        where id = #{id}
    </select>

    <select id="selectPaymentRecordImageList" parameterType="PaymentRecordImage" resultMap="PaymentRecordImageResult">
        <include refid="selectPaymentRecordImageVo"/>
        <where>
            <if test="billNumber != null  and billNumber != ''">and bill_number = #{billNumber}</if>
            <if test="contractNumber != null  and contractNumber != ''">and contract_number = #{contractNumber}</if>
            <if test="elderlyId != null  and elderlyId != ''">and elderly_id = #{elderlyId}</if>
            <if test="elderlyName != null  and elderlyName != ''">and elderly_name like concat('%', #{elderlyName}, '%')</if>
            <if test="elderlyPhone != null  and elderlyPhone != ''">and elderly_phone = #{elderlyPhone}</if>
            <if test="contractStartDate != null ">and contract_start_date = #{contractStartDate}</if>
            <if test="contractEndDate != null ">and contract_end_date = #{contractEndDate}</if>
            <if test="contractCycle != null ">and contract_cycle = #{contractCycle}</if>
            <if test="careLevel != null  and careLevel != ''">and care_level = #{careLevel}</if>
            <if test="bedName != null  and bedName != ''">and bed_name = #{bedName}</if>
            <if test="leaveDuration != null ">and leave_duration = #{leaveDuration}</if>
            <if test="leaveDates != null  and leaveDates != ''">and leave_dates = #{leaveDates}</if>
            <if test="refundCost != null ">and refund_cost = #{refundCost}</if>
            <if test="offerCost != null ">and offer_cost = #{offerCost}</if>
            <if test="totalCost != null ">and total_cost = #{totalCost}</if>
            <if test="accountAddCost != null ">and account_add_cost = #{accountAddCost}</if>
            <if test="paymentTime != null ">and payment_time = #{paymentTime}</if>
            <if test="feeType != null  and feeType != ''">and fee_type = #{feeType}</if>
            <if test="paymentStatus != null  and paymentStatus != ''">and payment_status = #{paymentStatus}</if>
            <if test="remark != null  and remark != ''">and remark = #{remark}</if>
            <if test="dischargeDate != null ">and discharge_date = #{dischargeDate}</if>
        </where>
    </select>

    <insert id="insertPaymentRecordImage" parameterType="PaymentRecordImage">
        insert into t_payment_record_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="tempGenerationId != null">temp_generation_id,</if>
            <if test="originalId != null">original_id,</if>
            <if test="billNumber != null">bill_number,</if>
            <if test="contractNumber != null">contract_number,</if>
            <if test="elderlyId != null">elderly_id,</if>
            <if test="elderlyName != null">elderly_name,</if>
            <if test="elderlyPhone != null">elderly_phone,</if>
            <if test="contractStartDate != null">contract_start_date,</if>
            <if test="contractEndDate != null">contract_end_date,</if>
            <if test="contractCycle != null">contract_cycle,</if>
            <if test="careLevel != null">care_level,</if>
            <if test="bedName != null">bed_name,</if>
            <if test="leaveDuration != null">leave_duration,</if>
            <if test="leaveDates != null">leave_dates,</if>
            <if test="refundCost != null">refund_cost,</if>
            <if test="offerCost != null">offer_cost,</if>
            <if test="totalCost != null">total_cost,</if>
            <if test="paidDetails != null">paid_details,</if>
            <if test="accountAddCost != null">account_add_cost,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="feeType != null">fee_type,</if>
            <if test="paymentStatus != null">payment_status,</if>
            <if test="remark != null">remark,</if>
            <if test="dischargeDate != null">discharge_date,</if>
            <if test="details != null">details,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="tempGenerationId != null">#{tempGenerationId},</if>
            <if test="originalId != null">#{originalId},</if>
            <if test="billNumber != null">#{billNumber},</if>
            <if test="contractNumber != null">#{contractNumber},</if>
            <if test="elderlyId != null">#{elderlyId},</if>
            <if test="elderlyName != null">#{elderlyName},</if>
            <if test="elderlyPhone != null">#{elderlyPhone},</if>
            <if test="contractStartDate != null">#{contractStartDate},</if>
            <if test="contractEndDate != null">#{contractEndDate},</if>
            <if test="contractCycle != null">#{contractCycle},</if>
            <if test="careLevel != null">#{careLevel},</if>
            <if test="bedName != null">#{bedName},</if>
            <if test="leaveDuration != null">#{leaveDuration},</if>
            <if test="leaveDates != null">#{leaveDates},</if>
            <if test="refundCost != null">#{refundCost},</if>
            <if test="offerCost != null">#{offerCost},</if>
            <if test="totalCost != null">#{totalCost},</if>
            <if test="paidDetails != null">#{paidDetails, typeHandler=com.ruoyi.common.core.utils.MybatisJsonTypeHandler},</if>
            <if test="accountAddCost != null">#{accountAddCost},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="paymentStatus != null">#{paymentStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="dischargeDate != null">#{dischargeDate},</if>
            <if test="details != null">#{details, typeHandler=com.ruoyi.common.core.utils.MybatisJsonTypeHandler},</if>
        </trim>
    </insert>

    <update id="updatePaymentRecordImage" parameterType="PaymentRecordImage">
        update t_payment_record_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="tempGenerationId != null">temp_generation_id = #{tempGenerationId},</if>
            <if test="originalId != null">original_id = #{originalId},</if>
            <if test="billNumber != null">bill_number = #{billNumber},</if>
            <if test="contractNumber != null">contract_number = #{contractNumber},</if>
            <if test="elderlyId != null">elderly_id = #{elderlyId},</if>
            <if test="elderlyName != null">elderly_name = #{elderlyName},</if>
            <if test="elderlyPhone != null">elderly_phone = #{elderlyPhone},</if>
            <if test="contractStartDate != null">contract_start_date = #{contractStartDate},</if>
            <if test="contractEndDate != null">contract_end_date = #{contractEndDate},</if>
            <if test="contractCycle != null">contract_cycle = #{contractCycle},</if>
            <if test="careLevel != null">care_level = #{careLevel},</if>
            <if test="bedName != null">bed_name = #{bedName},</if>
            <if test="leaveDuration != null">leave_duration = #{leaveDuration},</if>
            <if test="leaveDates != null">leave_dates = #{leaveDates},</if>
            <if test="refundCost != null">refund_cost = #{refundCost},</if>
            <if test="offerCost != null">offer_cost = #{offerCost},</if>
            <if test="totalCost != null">total_cost = #{totalCost},</if>
            <if test="paidDetails != null">paid_details = #{paidDetails, typeHandler=com.ruoyi.common.core.utils.MybatisJsonTypeHandler},</if>
            <if test="accountAddCost != null">account_add_cost = #{accountAddCost},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="feeType != null">fee_type = #{feeType},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="dischargeDate != null">discharge_date = #{dischargeDate},</if>
            <if test="details != null">details = #{details, typeHandler=com.ruoyi.common.core.utils.MybatisJsonTypeHandler},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentRecordImageById" parameterType="String">
        delete from t_payment_record_image where id = #{id}
    </delete>

    <delete id="deletePaymentRecordImageByIds" parameterType="String">
        delete from t_payment_record_image where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据临时生成ID查询镜像记录 -->
    <select id="selectPaymentRecordImageByTempGenerationId" parameterType="String" resultMap="PaymentRecordImageResult">
        <include refid="selectPaymentRecordImageVo"/>
        where temp_generation_id = #{tempGenerationId}
    </select>

    <!-- 根据原始记录ID查询镜像记录 -->
    <select id="selectPaymentRecordImageByOriginalId" parameterType="String" resultMap="PaymentRecordImageResult">
        <include refid="selectPaymentRecordImageVo"/>
        where original_id = #{originalId}
    </select>
</mapper>
