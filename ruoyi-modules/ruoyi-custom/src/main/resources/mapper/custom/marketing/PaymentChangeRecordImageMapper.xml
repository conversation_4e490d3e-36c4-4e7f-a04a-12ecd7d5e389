<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.PaymentChangeRecordImageMapper">

    <resultMap type="PaymentChangeRecordImage" id="PaymentChangeRecordImageResult">
        <result property="id" column="id"/>
        <result property="tempGenerationId" column="temp_generation_id"/>
        <result property="originalId" column="original_id"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="liveId" column="live_id"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="contractCycle" column="contract_cycle"/>
        <result property="careLevel" column="care_level"/>
        <result property="bedName" column="bed_name"/>
        <result property="accountAddCost" column="account_add_cost"/>
        <result property="remark" column="remark"/>
        <result property="paymentStatus" column="payment_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="details" column="details" typeHandler="com.ruoyi.common.core.utils.MybatisJsonTypeHandler"/>
    </resultMap>

    <sql id="selectPaymentChangeRecordImageVo">
        select id, temp_generation_id, original_id, contract_number, live_id, elderly_id, elderly_name,
               contract_start_date, contract_end_date, contract_cycle, care_level, bed_name, account_add_cost,
               remark, payment_status, details, create_time, create_by, update_time, update_by
        from t_payment_change_record_image
    </sql>

    <select id="selectPaymentChangeRecordImageById" parameterType="String" resultMap="PaymentChangeRecordImageResult">
        <include refid="selectPaymentChangeRecordImageVo"/>
        where id = #{id}
    </select>

    <select id="selectPaymentChangeRecordImageList" parameterType="PaymentChangeRecordImage" resultMap="PaymentChangeRecordImageResult">
        <include refid="selectPaymentChangeRecordImageVo"/>
        <where>
            <if test="tempGenerationId != null and tempGenerationId != ''">
                and temp_generation_id = #{tempGenerationId}
            </if>
            <if test="originalId != null and originalId != ''">
                and original_id = #{originalId}
            </if>
            <if test="contractNumber != null and contractNumber != ''">
                and contract_number like concat('%', #{contractNumber}, '%')
            </if>
            <if test="elderlyId != null and elderlyId != ''">
                and elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null and elderlyName != ''">
                and elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="paymentStatus != null and paymentStatus != ''">
                and payment_status = #{paymentStatus}
            </if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertPaymentChangeRecordImage" parameterType="PaymentChangeRecordImage">
        insert into t_payment_change_record_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="tempGenerationId != null">temp_generation_id,</if>
            <if test="originalId != null">original_id,</if>
            <if test="contractNumber != null">contract_number,</if>
            <if test="liveId != null">live_id,</if>
            <if test="elderlyId != null">elderly_id,</if>
            <if test="elderlyName != null">elderly_name,</if>
            <if test="contractStartDate != null">contract_start_date,</if>
            <if test="contractEndDate != null">contract_end_date,</if>
            <if test="contractCycle != null">contract_cycle,</if>
            <if test="careLevel != null">care_level,</if>
            <if test="bedName != null">bed_name,</if>
            <if test="accountAddCost != null">account_add_cost,</if>
            <if test="remark != null">remark,</if>
            <if test="paymentStatus != null">payment_status,</if>
            <if test="details != null">details,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="tempGenerationId != null">#{tempGenerationId},</if>
            <if test="originalId != null">#{originalId},</if>
            <if test="contractNumber != null">#{contractNumber},</if>
            <if test="liveId != null">#{liveId},</if>
            <if test="elderlyId != null">#{elderlyId},</if>
            <if test="elderlyName != null">#{elderlyName},</if>
            <if test="contractStartDate != null">#{contractStartDate},</if>
            <if test="contractEndDate != null">#{contractEndDate},</if>
            <if test="contractCycle != null">#{contractCycle},</if>
            <if test="careLevel != null">#{careLevel},</if>
            <if test="bedName != null">#{bedName},</if>
            <if test="accountAddCost != null">#{accountAddCost},</if>
            <if test="remark != null">#{remark},</if>
            <if test="paymentStatus != null">#{paymentStatus},</if>
            <if test="details != null">#{details},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updatePaymentChangeRecordImage" parameterType="PaymentChangeRecordImage">
        update t_payment_change_record_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="tempGenerationId != null">temp_generation_id = #{tempGenerationId},</if>
            <if test="originalId != null">original_id = #{originalId},</if>
            <if test="contractNumber != null">contract_number = #{contractNumber},</if>
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="elderlyId != null">elderly_id = #{elderlyId},</if>
            <if test="elderlyName != null">elderly_name = #{elderlyName},</if>
            <if test="contractStartDate != null">contract_start_date = #{contractStartDate},</if>
            <if test="contractEndDate != null">contract_end_date = #{contractEndDate},</if>
            <if test="contractCycle != null">contract_cycle = #{contractCycle},</if>
            <if test="careLevel != null">care_level = #{careLevel},</if>
            <if test="bedName != null">bed_name = #{bedName},</if>
            <if test="accountAddCost != null">account_add_cost = #{accountAddCost},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="details != null">details = #{details},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentChangeRecordImageById" parameterType="String">
        delete from t_payment_change_record_image where id = #{id}
    </delete>

    <delete id="deletePaymentChangeRecordImageByIds" parameterType="String">
        delete from t_payment_change_record_image where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据临时生成ID查询镜像记录 -->
    <select id="selectPaymentChangeRecordImageByTempGenerationId" parameterType="String" resultMap="PaymentChangeRecordImageResult">
        <include refid="selectPaymentChangeRecordImageVo"/>
        where temp_generation_id = #{tempGenerationId}
    </select>

    <!-- 根据原始记录ID查询镜像记录 -->
    <select id="selectPaymentChangeRecordImageByOriginalId" parameterType="String" resultMap="PaymentChangeRecordImageResult">
        <include refid="selectPaymentChangeRecordImageVo"/>
        where original_id = #{originalId}
    </select>
</mapper>
