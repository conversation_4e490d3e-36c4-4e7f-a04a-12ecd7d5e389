<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.PaymentRecordSummaryMapper">

    <resultMap type="PaymentRecordSummary" id="PaymentRecordSummaryResult">
        <result property="id"    column="id"    />
        <result property="paymentRecordId"    column="payment_record_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="liveStatus"    column="live_status"    />
        <result property="careLevel"    column="care_level"    />
        <result property="bedNumber"    column="bed_number"    />
        <result property="totalPayment"    column="total_payment"    />
        <result property="discount"    column="discount"    />
        <result property="roomType"    column="room_type"    />
        <result property="contractNumber"    column="contract_number"    />
        <result property="contractDateFormatted"    column="contract_date_formatted"    />
        <result property="contractDate"    column="contract_date"    />
        <result property="feeCalculationDetails"    column="fee_calculation_details"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPaymentRecordSummaryVo">
        select id, payment_record_id, customer_name, live_status, care_level, bed_number, total_payment, discount, room_type, contract_number, contract_date_formatted, contract_date, fee_calculation_details, del_flag, create_by, create_time, update_by, update_time, remark from t_payment_record_summary
    </sql>

    <select id="selectPaymentRecordSummaryList" parameterType="PaymentRecordSummary" resultMap="PaymentRecordSummaryResult">
        <include refid="selectPaymentRecordSummaryVo"/>
        <where>
            <if test="paymentRecordId != null  and paymentRecordId != ''"> and payment_record_id = #{paymentRecordId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="liveStatus != null  and liveStatus != ''"> and live_status = #{liveStatus}</if>
            <if test="careLevel != null  and careLevel != ''"> and care_level = #{careLevel}</if>
            <if test="bedNumber != null  and bedNumber != ''"> and bed_number like concat('%', #{bedNumber}, '%')</if>
            <if test="roomType != null  and roomType != ''"> and room_type = #{roomType}</if>
            <if test="contractNumber != null  and contractNumber != ''"> and contract_number like concat('%', #{contractNumber}, '%')</if>
            <if test="contractDate != null "> and contract_date = #{contractDate}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectPaymentRecordSummaryById" parameterType="String" resultMap="PaymentRecordSummaryResult">
        <include refid="selectPaymentRecordSummaryVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectPaymentRecordSummaryByPaymentRecordId" parameterType="String" resultMap="PaymentRecordSummaryResult">
        <include refid="selectPaymentRecordSummaryVo"/>
        where payment_record_id = #{paymentRecordId} and del_flag = '0'
    </select>

    <insert id="insertPaymentRecordSummary" parameterType="PaymentRecordSummary">
        insert into t_payment_record_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="paymentRecordId != null">payment_record_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="liveStatus != null">live_status,</if>
            <if test="careLevel != null">care_level,</if>
            <if test="bedNumber != null">bed_number,</if>
            <if test="totalPayment != null">total_payment,</if>
            <if test="discount != null">discount,</if>
            <if test="roomType != null">room_type,</if>
            <if test="contractNumber != null">contract_number,</if>
            <if test="contractDateFormatted != null">contract_date_formatted,</if>
            <if test="contractDate != null">contract_date,</if>
            <if test="feeCalculationDetails != null">fee_calculation_details,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="paymentRecordId != null">#{paymentRecordId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="liveStatus != null">#{liveStatus},</if>
            <if test="careLevel != null">#{careLevel},</if>
            <if test="bedNumber != null">#{bedNumber},</if>
            <if test="totalPayment != null">#{totalPayment},</if>
            <if test="discount != null">#{discount},</if>
            <if test="roomType != null">#{roomType},</if>
            <if test="contractNumber != null">#{contractNumber},</if>
            <if test="contractDateFormatted != null">#{contractDateFormatted},</if>
            <if test="contractDate != null">#{contractDate},</if>
            <if test="feeCalculationDetails != null">#{feeCalculationDetails},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePaymentRecordSummary" parameterType="PaymentRecordSummary">
        update t_payment_record_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentRecordId != null">payment_record_id = #{paymentRecordId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="liveStatus != null">live_status = #{liveStatus},</if>
            <if test="careLevel != null">care_level = #{careLevel},</if>
            <if test="bedNumber != null">bed_number = #{bedNumber},</if>
            <if test="totalPayment != null">total_payment = #{totalPayment},</if>
            <if test="discount != null">discount = #{discount},</if>
            <if test="roomType != null">room_type = #{roomType},</if>
            <if test="contractNumber != null">contract_number = #{contractNumber},</if>
            <if test="contractDateFormatted != null">contract_date_formatted = #{contractDateFormatted},</if>
            <if test="contractDate != null">contract_date = #{contractDate},</if>
            <if test="feeCalculationDetails != null">fee_calculation_details = #{feeCalculationDetails},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentRecordSummaryById" parameterType="String">
        update t_payment_record_summary set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deletePaymentRecordSummaryByIds" parameterType="String">
        update t_payment_record_summary set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletePaymentRecordSummaryByPaymentRecordId" parameterType="String">
        update t_payment_record_summary set del_flag = '1' where payment_record_id = #{paymentRecordId}
    </delete>
</mapper>
