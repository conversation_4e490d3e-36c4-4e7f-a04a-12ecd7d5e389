<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryScrapMapper">

    <resultMap type="PharmacyInventoryScrap" id="PharmacyInventoryScrapResult">
        <result property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="inventoryId" column="inventory_id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="pharmacyId" column="pharmacy_id"/>
        <result property="pharmacyName" column="pharmacy_name"/>
        <result property="batch" column="batch"/>
        <result property="scrapQuantity" column="scrap_quantity"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="reason" column="reason"/>
        <result property="scrapTime" column="scrap_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectPharmacyInventoryScrapVo">
        SELECT id,
               serial_number,
               inventory_id,
               warehouse_id,
               warehouse_name,
               pharmacy_id,
               pharmacy_name,
               batch,
               scrap_quantity,
               operator_id,
               operator_name,
               reason,
               scrap_time,
               create_time
        FROM t_pharmacy_inventory_scrap
    </sql>

    <select id="selectPharmacyInventoryScrapList" parameterType="PharmacyInventoryScrap"
            resultMap="PharmacyInventoryScrapResult">
        <include refid="selectPharmacyInventoryScrapVo"/>
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="inventoryId != null ">
                and inventory_id = #{inventoryId}
            </if>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="warehouseName != null  and warehouseName != ''">
                and warehouse_name like concat('%', #{warehouseName}, '%')
            </if>
            <if test="pharmacyId != null ">
                and pharmacy_id = #{pharmacyId}
            </if>
            <if test="pharmacyName != null  and pharmacyName != ''">
                and pharmacy_name like concat('%', #{pharmacyName}, '%')
            </if>
            <if test="batch != null ">
                and batch = #{batch}
            </if>
            <if test="scrapQuantity != null ">
                and scrap_quantity = #{scrapQuantity}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
            <if test="operatorName != null  and operatorName != ''">
                and operator_name like concat('%', #{operatorName}, '%')
            </if>
            <if test="reason != null  and reason != ''">
                and reason = #{reason}
            </if>
            <if test="scrapTime != null ">
                and scrap_time = #{scrapTime}
            </if>
        </where>
    </select>

    <select id="selectPharmacyInventoryScrapById" parameterType="Long"
            resultMap="PharmacyInventoryScrapResult">
        <include refid="selectPharmacyInventoryScrapVo"/>
        where id = #{id}
    </select>

    <select id="selectPharmacyInventoryScrapList2" parameterType="PharmacyInventoryScrap"
            resultMap="PharmacyInventoryScrapResult">
        SELECT id,
        serial_number,
        warehouse_id,
        warehouse_name,
        GROUP_CONCAT(DISTINCT pharmacy_name) AS pharmacy_name,
        operator_id,
        operator_name,
        reason,
        scrap_time,
        create_time
        FROM t_pharmacy_inventory_scrap
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
            <if test="reason != null and reason != ''">
                and reason = #{reason}
            </if>
            <if test="params.beginScrapTime != null and params.endScrapTime != null">
                and scrap_time between #{params.beginScrapTime} and #{params.endScrapTime}
            </if>
        </where>
        GROUP BY serial_number
        <if test="pharmacyName != null and pharmacyName != ''">
            HAVING GROUP_CONCAT(DISTINCT pharmacy_name) like concat('%', #{pharmacyName}, '%')
        </if>
        <if test="params.content != null and params.content != ''">
            HAVING GROUP_CONCAT(DISTINCT pharmacy_name) like concat('%', #{params.content}, '%') or serial_number like concat('%', #{params.content}, '%')
        </if>
    </select>

    <insert id="insertPharmacyInventoryScrap" parameterType="PharmacyInventoryScrap" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_pharmacy_inventory_scrap
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number,
            </if>
            <if test="inventoryId != null">inventory_id,
            </if>
            <if test="warehouseId != null">warehouse_id,
            </if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,
            </if>
            <if test="pharmacyId != null">pharmacy_id,
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">pharmacy_name,
            </if>
            <if test="batch != null">batch,
            </if>
            <if test="scrapQuantity != null">scrap_quantity,
            </if>
            <if test="operatorId != null">operator_id,
            </if>
            <if test="operatorName != null and operatorName != ''">operator_name,
            </if>
            <if test="reason != null and reason != ''">reason,
            </if>
            <if test="scrapTime != null">scrap_time,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},
            </if>
            <if test="inventoryId != null">#{inventoryId},
            </if>
            <if test="warehouseId != null">#{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},
            </if>
            <if test="pharmacyId != null">#{pharmacyId},
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">#{pharmacyName},
            </if>
            <if test="batch != null">#{batch},
            </if>
            <if test="scrapQuantity != null">#{scrapQuantity},
            </if>
            <if test="operatorId != null">#{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},
            </if>
            <if test="reason != null and reason != ''">#{reason},
            </if>
            <if test="scrapTime != null">#{scrapTime},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch">
        INSERT INTO t_pharmacy_inventory_scrap (serial_number, inventory_id, warehouse_id, warehouse_name, pharmacy_id,
        pharmacy_name, batch, scrap_quantity, operator_id, operator_name, reason, scrap_time, create_time) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.serialNumber}, #{item.inventoryId}, #{item.warehouseId}, #{item.warehouseName}, #{item.pharmacyId},
            #{item.pharmacyName}, #{item.batch}, #{item.scrapQuantity}, #{item.operatorId}, #{item.operatorName},
            #{item.reason}, #{item.scrapTime}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updatePharmacyInventoryScrap" parameterType="PharmacyInventoryScrap">
        update t_pharmacy_inventory_scrap
        <trim prefix="SET" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number =
                #{serialNumber},
            </if>
            <if test="inventoryId != null">inventory_id =
                #{inventoryId},
            </if>
            <if test="warehouseId != null">warehouse_id =
                #{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name =
                #{warehouseName},
            </if>
            <if test="pharmacyId != null">pharmacy_id =
                #{pharmacyId},
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">pharmacy_name =
                #{pharmacyName},
            </if>
            <if test="batch != null">batch =
                #{batch},
            </if>
            <if test="scrapQuantity != null">scrap_quantity =
                #{scrapQuantity},
            </if>
            <if test="operatorId != null">operator_id =
                #{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">operator_name =
                #{operatorName},
            </if>
            <if test="reason != null and reason != ''">reason =
                #{reason},
            </if>
            <if test="scrapTime != null">scrap_time =
                #{scrapTime},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePharmacyInventoryScrapById" parameterType="Long">
        DELETE
        FROM t_pharmacy_inventory_scrap
        WHERE id = #{id}
    </delete>

    <delete id="deletePharmacyInventoryScrapByIds" parameterType="String">
        delete from t_pharmacy_inventory_scrap where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
