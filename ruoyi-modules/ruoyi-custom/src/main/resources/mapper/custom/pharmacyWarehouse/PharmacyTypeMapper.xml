<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyTypeMapper">

    <resultMap type="PharmacyType" id="PharmacyTypeResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectPharmacyTypeVo">
        SELECT id, name, create_time
        FROM t_pharmacy_type
    </sql>

    <select id="selectPharmacyTypeList" parameterType="PharmacyType" resultMap="PharmacyTypeResult">
        <include refid="selectPharmacyTypeVo"/>
        <where>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>

    <select id="selectPharmacyTypeById" parameterType="Long"
            resultMap="PharmacyTypeResult">
        <include refid="selectPharmacyTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertPharmacyType" parameterType="PharmacyType" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_pharmacy_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <update id="updatePharmacyType" parameterType="PharmacyType">
        update t_pharmacy_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePharmacyTypeById" parameterType="Long">
        DELETE
        FROM t_pharmacy_type
        WHERE id = #{id}
    </delete>

    <delete id="deletePharmacyTypeByIds" parameterType="String">
        delete from t_pharmacy_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
