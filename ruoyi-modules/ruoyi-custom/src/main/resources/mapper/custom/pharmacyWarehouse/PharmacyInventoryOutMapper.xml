<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryOutMapper">

    <resultMap type="PharmacyInventoryOut" id="PharmacyInventoryOutResult">
        <result property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="inventoryId" column="inventory_id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="pharmacyId" column="pharmacy_id"/>
        <result property="pharmacyName" column="pharmacy_name"/>
        <result property="batch" column="batch"/>
        <result property="quantity" column="quantity"/>
        <result property="remain" column="remain"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="destination" column="destination"/>
        <result property="outTime" column="out_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectPharmacyInventoryOutVo">
        SELECT id,
               serial_number,
               inventory_id,
               warehouse_id,
               warehouse_name,
               pharmacy_id,
               pharmacy_name,
               batch,
               quantity,
               remain,
               operator_id,
               operator_name,
               destination,
               out_time,
               create_time
        FROM t_pharmacy_inventory_out
    </sql>

    <select id="selectPharmacyInventoryOutList" parameterType="PharmacyInventoryOut"
            resultMap="PharmacyInventoryOutResult">
        <include refid="selectPharmacyInventoryOutVo"/>
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="inventoryId != null ">
                and inventory_id = #{inventoryId}
            </if>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="warehouseName != null  and warehouseName != ''">
                and warehouse_name like concat('%', #{warehouseName}, '%')
            </if>
            <if test="pharmacyId != null ">
                and pharmacy_id = #{pharmacyId}
            </if>
            <if test="pharmacyName != null  and pharmacyName != ''">
                and pharmacy_name like concat('%', #{pharmacyName}, '%')
            </if>
            <if test="batch != null ">
                and batch = #{batch}
            </if>
            <if test="quantity != null ">
                and quantity = #{quantity}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
            <if test="operatorName != null  and operatorName != ''">
                and operator_name like concat('%', #{operatorName}, '%')
            </if>
            <if test="destination != null  and destination != ''">
                and destination = #{destination}
            </if>
            <if test="outTime != null ">
                and out_time = #{outTime}
            </if>
        </where>
    </select>

    <select id="selectPharmacyInventoryOutById" parameterType="Long"
            resultMap="PharmacyInventoryOutResult">
        <include refid="selectPharmacyInventoryOutVo"/>
        where id = #{id}
    </select>

    <select id="selectPharmacyInventoryOutList2" parameterType="PharmacyInventoryOut"
            resultMap="PharmacyInventoryOutResult">
        SELECT id,
        serial_number,
        warehouse_id,
        warehouse_name,
        GROUP_CONCAT(DISTINCT pharmacy_name) AS pharmacy_name,
        operator_id,
        operator_name,
        destination,
        out_time,
        create_time
        FROM t_pharmacy_inventory_in
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
            <if test="destination != null and destination != ''">
                and destination = #{destination}
            </if>
            <if test="params.beginOutTime != null and params.endOutTime != null">
                and out_time between #{params.beginOutTime} and #{params.endOutTime}
            </if>
        </where>
        GROUP BY serial_number
        <if test="pharmacyName != null and pharmacyName != ''">
            HAVING GROUP_CONCAT(DISTINCT pharmacy_name) like concat('%', #{pharmacyName}, '%')
        </if>
        <if test="params.content != null and params.content != ''">
            HAVING GROUP_CONCAT(DISTINCT pharmacy_name) like concat('%', #{params.content}, '%') or serial_number like concat('%', #{params.content}, '%')
        </if>
    </select>

    <insert id="insertPharmacyInventoryOut" parameterType="PharmacyInventoryOut" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_pharmacy_inventory_out
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number,
            </if>
            <if test="inventoryId != null">inventory_id,
            </if>
            <if test="warehouseId != null">warehouse_id,
            </if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,
            </if>
            <if test="pharmacyId != null">pharmacy_id,
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">pharmacy_name,
            </if>
            <if test="batch != null">batch,
            </if>
            <if test="quantity != null">quantity,
            </if>
            <if test="remain != null">remain,
            </if>
            <if test="operatorId != null">operator_id,
            </if>
            <if test="operatorName != null and operatorName != ''">operator_name,
            </if>
            <if test="destination != null and destination != ''">destination,
            </if>
            <if test="outTime != null">out_time,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},
            </if>
            <if test="inventoryId != null">#{inventoryId},
            </if>
            <if test="warehouseId != null">#{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},
            </if>
            <if test="pharmacyId != null">#{pharmacyId},
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">#{pharmacyName},
            </if>
            <if test="batch != null">#{batch},
            </if>
            <if test="quantity != null">#{quantity},
            </if>
            <if test="remain != null">#{remain},
            </if>
            <if test="operatorId != null">#{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},
            </if>
            <if test="destination != null and destination != ''">#{destination},
            </if>
            <if test="outTime != null">#{outTime},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch">
        INSERT INTO t_pharmacy_inventory_out (serial_number,inventory_id,warehouse_id,warehouse_name,pharmacy_id,pharmacy_name,batch,quantity,remain,operator_id,operator_name,destination,out_time,create_time) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.serialNumber},#{item.inventoryId},#{item.warehouseId},#{item.warehouseName},#{item.pharmacyId},#{item.pharmacyName},#{item.batch},#{item.quantity},#{item.remain},#{item.operatorId},#{item.operatorName},#{item.destination},#{item.outTime},#{item.createTime})
        </foreach>
    </insert>

    <update id="updatePharmacyInventoryOut" parameterType="PharmacyInventoryOut">
        update t_pharmacy_inventory_out
        <trim prefix="SET" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number =
                #{serialNumber},
            </if>
            <if test="inventoryId != null">inventory_id =
                #{inventoryId},
            </if>
            <if test="warehouseId != null">warehouse_id =
                #{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name =
                #{warehouseName},
            </if>
            <if test="pharmacyId != null">pharmacy_id =
                #{pharmacyId},
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">pharmacy_name =
                #{pharmacyName},
            </if>
            <if test="batch != null">batch =
                #{batch},
            </if>
            <if test="quantity != null">quantity =
                #{quantity},
            </if>
            <if test="remain != null">remain =
                #{remain},
            </if>
            <if test="operatorId != null">operator_id =
                #{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">operator_name =
                #{operatorName},
            </if>
            <if test="destination != null and destination != ''">destination =
                #{destination},
            </if>
            <if test="outTime != null">out_time =
                #{outTime},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePharmacyInventoryOutById" parameterType="Long">
        DELETE
        FROM t_pharmacy_inventory_out
        WHERE id = #{id}
    </delete>

    <delete id="deletePharmacyInventoryOutByIds" parameterType="String">
        delete from t_pharmacy_inventory_out where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
