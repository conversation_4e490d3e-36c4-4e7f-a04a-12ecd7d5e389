<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PrescriptionDetailMapper">

    <resultMap type="PrescriptionDetail" id="PrescriptionDetailResult">
        <result property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="prescriptionTime" column="prescription_time"/>
        <result property="inventoryId" column="inventory_id"/>
        <result property="medicineId" column="medicine_id"/>
        <result property="medicineName" column="medicine_name"/>
        <result property="batchNumber" column="batch_number"/>
        <result property="specification" column="specification"/>
        <result property="dosageUsage" column="dosage_usage"/>
        <result property="estimatedCourseDays" column="estimated_course_days"/>
        <result property="remark" column="remark"/>

        <result property="bedName" column="bedName"/>
    </resultMap>

    <sql id="selectPrescriptionDetailVo">
        SELECT id,
               serial_number,
               elderly_id,
               elderly_name,
               doctor_id,
               doctor_name,
               prescription_time,
               inventory_id,
               medicine_id,
               medicine_name,
               batch_number,
               specification,
               dosage_usage,
               estimated_course_days,
               remark
        FROM t_prescription_detail
    </sql>

    <select id="selectPrescriptionDetailList" parameterType="PrescriptionDetail" resultMap="PrescriptionDetailResult">
        <include refid="selectPrescriptionDetailVo"/>
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="elderlyId != null ">
                and elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null  and elderlyName != ''">
                and elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="doctorId != null ">
                and doctor_id = #{doctorId}
            </if>
            <if test="doctorName != null  and doctorName != ''">
                and doctor_name like concat('%', #{doctorName}, '%')
            </if>
            <if test="prescriptionTime != null ">
                and prescription_time = #{prescriptionTime}
            </if>
            <if test="inventoryId != null ">
                and inventory_id = #{inventoryId}
            </if>
            <if test="medicineId != null ">
                and medicine_id = #{medicineId}
            </if>
            <if test="medicineName != null  and medicineName != ''">
                and medicine_name like concat('%', #{medicineName}, '%')
            </if>
            <if test="batchNumber != null  and batchNumber != ''">
                and batch_number = #{batchNumber}
            </if>
            <if test="specification != null  and specification != ''">
                and specification = #{specification}
            </if>
            <if test="dosageUsage != null  and dosageUsage != ''">
                and dosage_usage = #{dosageUsage}
            </if>
            <if test="estimatedCourseDays != null ">
                and estimated_course_days = #{estimatedCourseDays}
            </if>
        </where>
    </select>

    <select id="selectPrescriptionDetailById" parameterType="Long"
            resultMap="PrescriptionDetailResult">
        <include refid="selectPrescriptionDetailVo"/>
        where id = #{id}
    </select>

    <select id="selectPrescriptionDetailList2" parameterType="PrescriptionDetail"
            resultMap="PrescriptionDetailResult">
        SELECT
        a.id,
        a.serial_number,
        a.elderly_id,
        a.elderly_name,
        a.doctor_id,
        a.doctor_name,
        GROUP_CONCAT(DISTINCT a.medicine_name) AS medicine_name,
        a.prescription_time,
        a.create_time,

        CONCAT(bud3.`name`,'-',bud2.`name`,'-',bud1.`name`,'-',bedinfo.bed_name) as bedName

        FROM t_prescription_detail a
        LEFT JOIN
        (SELECT *
        FROM (SELECT *,
        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
        FROM t_live_base_info) t
        WHERE rn = 1) b ON a.id = b.user_id
        LEFT JOIN t_live_bed_records bed on bed.live_id=b.id
        LEFT JOIN t_bed_base_info bedinfo on bed.bed_id=bedinfo.id
        LEFT JOIN t_storied_building_info bud1 on bed.room_id= bud1.id
        LEFT JOIN t_storied_building_info bud2 on bud1.parent_id= bud2.id
        LEFT JOIN t_storied_building_info bud3 on bud2.parent_id= bud3.id
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and a.serial_number = #{serialNumber}
            </if>
            <if test="elderlyId != null ">
                and a.elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null and elderlyName != '' ">
                and a.elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="params.beginPrescriptionTime != null and params.endPrescriptionTime != null">
                and a.prescription_time between #{params.beginPrescriptionTime} and #{params.endPrescriptionTime}
            </if>
        </where>
        GROUP BY a.serial_number
        <if test="medicineName != null and medicineName != ''">
            HAVING GROUP_CONCAT(DISTINCT medicine_name) like concat('%', #{medicineName}, '%')
        </if>
    </select>

    <resultMap type="PrescriptionDetailRespVo" id="PrescriptionDetailResult3">
        <id property="serialNumber" column="serial_number"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="prescriptionTime" column="prescription_time"/>
        <result property="bedName" column="bedName"/>

        <collection property="detailList"
                    ofType="com.ruoyi.custom.admin.pharmacyWarehouse.resp.PrescriptionDetailRespVo$Detail"
                    select="selectDetailBySerialNumber"
                    column="serial_number">
            <id column="id" property="id"/>
            <result column="inventory_id" property="inventoryId"/>
            <result column="medicine_id" property="medicineId"/>
            <result column="medicine_name" property="medicineName"/>
            <result column="batch_number" property="batchNumber"/>
            <result column="specification" property="specification"/>
            <result column="dosage_usage" property="dosageUsage"/>
            <result column="estimated_course_days" property="estimatedCourseDays"/>
            <result column="remark" property="remark"/>
        </collection>
    </resultMap>

    <select id="selectPrescriptionDetailList3"
            resultMap="PrescriptionDetailResult3">
        SELECT serial_number,
               elderly_id,
               elderly_name,
               doctor_id,
               doctor_name,
               prescription_time,

               CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', bedinfo.bed_name) AS bedName

        FROM t_prescription_detail a
                 LEFT JOIN
             (SELECT *
              FROM (SELECT *,
                           ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
                    FROM t_live_base_info) t
              WHERE rn = 1) b ON a.elderly_id = b.user_id
                 LEFT JOIN t_live_bed_records bed ON bed.live_id = b.id
                 LEFT JOIN t_bed_base_info bedinfo ON bed.bed_id = bedinfo.id
                 LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
                 LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
                 LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and a.serial_number = #{serialNumber}
            </if>
            <if test="elderlyId != null ">
                and a.elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null and elderlyName != '' ">
                and a.elderly_name like concat('%', #{elderlyName}, '%')
            </if>
        </where>

    </select>

    <select id="selectDetailBySerialNumber" resultType="com.ruoyi.custom.admin.pharmacyWarehouse.resp.PrescriptionDetailRespVo$Detail">
        SELECT
            id,
            inventory_id,
            medicine_id,
            medicine_name,
            batch_number,
            specification,
            dosage_usage,
            estimated_course_days,
            remark
        FROM
            t_prescription_detail
        WHERE
            serial_number = #{serialNumber}
    </select>

    <insert id="insertPrescriptionDetail" parameterType="PrescriptionDetail" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_prescription_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number,
            </if>
            <if test="elderlyId != null">elderly_id,
            </if>
            <if test="elderlyName != null and elderlyName != ''">elderly_name,
            </if>
            <if test="doctorId != null">doctor_id,
            </if>
            <if test="doctorName != null and doctorName != ''">doctor_name,
            </if>
            <if test="prescriptionTime != null">prescription_time,
            </if>
            <if test="inventoryId != null">inventory_id,
            </if>
            <if test="medicineId != null">medicine_id,
            </if>
            <if test="medicineName != null and medicineName != ''">medicine_name,
            </if>
            <if test="batchNumber != null">batch_number,
            </if>
            <if test="specification != null">specification,
            </if>
            <if test="dosageUsage != null">dosage_usage,
            </if>
            <if test="estimatedCourseDays != null">estimated_course_days,
            </if>
            <if test="remark != null">remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},
            </if>
            <if test="elderlyId != null">#{elderlyId},
            </if>
            <if test="elderlyName != null and elderlyName != ''">#{elderlyName},
            </if>
            <if test="doctorId != null">#{doctorId},
            </if>
            <if test="doctorName != null and doctorName != ''">#{doctorName},
            </if>
            <if test="prescriptionTime != null">#{prescriptionTime},
            </if>
            <if test="inventoryId != null">#{inventoryId},
            </if>
            <if test="medicineId != null">#{medicineId},
            </if>
            <if test="medicineName != null and medicineName != ''">#{medicineName},
            </if>
            <if test="batchNumber != null">#{batchNumber},
            </if>
            <if test="specification != null">#{specification},
            </if>
            <if test="dosageUsage != null">#{dosageUsage},
            </if>
            <if test="estimatedCourseDays != null">#{estimatedCourseDays},
            </if>
            <if test="remark != null">#{remark},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch">
        INSERT INTO t_prescription_detail (serial_number, elderly_id, elderly_name, doctor_id, doctor_name,
        prescription_time, inventory_id, medicine_id, medicine_name, batch_number, specification, dosage_usage,
        estimated_course_days, remark) VALUES
        <foreach collection="items" item="item" separator=",">
            (#{item.serialNumber}, #{item.elderlyId}, #{item.elderlyName}, #{item.doctorId}, #{item.doctorName},
            #{item.prescriptionTime}, #{item.inventoryId}, #{item.medicineId}, #{item.medicineName},
            #{item.batchNumber},
            #{item.specification}, #{item.dosageUsage}, #{item.estimatedCourseDays}, #{item.remark})
        </foreach>
    </insert>

    <update id="updatePrescriptionDetail" parameterType="PrescriptionDetail">
        update t_prescription_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number =
                #{serialNumber},
            </if>
            <if test="elderlyId != null">elderly_id =
                #{elderlyId},
            </if>
            <if test="elderlyName != null and elderlyName != ''">elderly_name =
                #{elderlyName},
            </if>
            <if test="doctorId != null">doctor_id =
                #{doctorId},
            </if>
            <if test="doctorName != null and doctorName != ''">doctor_name =
                #{doctorName},
            </if>
            <if test="prescriptionTime != null">prescription_time =
                #{prescriptionTime},
            </if>
            <if test="inventoryId != null">inventory_id =
                #{inventoryId},
            </if>
            <if test="medicineId != null">medicine_id =
                #{medicineId},
            </if>
            <if test="medicineName != null and medicineName != ''">medicine_name =
                #{medicineName},
            </if>
            <if test="batchNumber != null">batch_number =
                #{batchNumber},
            </if>
            <if test="specification != null">specification =
                #{specification},
            </if>
            <if test="dosageUsage != null">dosage_usage =
                #{dosageUsage},
            </if>
            <if test="estimatedCourseDays != null">estimated_course_days =
                #{estimatedCourseDays},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePrescriptionDetailById" parameterType="Long">
        DELETE
        FROM t_prescription_detail
        WHERE id = #{id}
    </delete>

    <delete id="deletePrescriptionDetailByIds" parameterType="String">
        delete from t_prescription_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBySerialNumber">
        DELETE
        FROM t_prescription_detail
        WHERE serial_number = #{serialNumber}
    </delete>
</mapper>
