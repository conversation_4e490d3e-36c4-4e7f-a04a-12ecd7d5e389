<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyManagementMapper">

    <resultMap type="PharmacyManagement" id="PharmacyManagementResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="typeId" column="type_id"/>
        <result property="createTime" column="create_time"/>

        <result property="typeName" column="type_name"/>
    </resultMap>

    <sql id="selectPharmacyManagementVo">
        SELECT t.id,
               t.name,
               t.type_id,
               t.create_time,
               t1.name AS type_name
        FROM t_pharmacy_management t
                 LEFT JOIN t_pharmacy_type t1 ON t.type_id = t1.id
    </sql>

    <select id="selectPharmacyManagementList" parameterType="PharmacyManagement" resultMap="PharmacyManagementResult">
        <include refid="selectPharmacyManagementVo"/>
        <where>
            <if test="name != null  and name != ''">
                and t.name like concat('%', #{name}, '%')
            </if>
            <if test="typeId != null ">
                and t.type_id = #{typeId}
            </if>
        </where>
    </select>

    <select id="selectPharmacyManagementById" parameterType="Long"
            resultMap="PharmacyManagementResult">
        <include refid="selectPharmacyManagementVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertPharmacyManagement" parameterType="PharmacyManagement" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_pharmacy_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,
            </if>
            <if test="typeId != null">type_id,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="typeId != null">#{typeId},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <update id="updatePharmacyManagement" parameterType="PharmacyManagement">
        update t_pharmacy_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="typeId != null">type_id =
                #{typeId},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePharmacyManagementById" parameterType="Long">
        DELETE
        FROM t_pharmacy_management
        WHERE id = #{id}
    </delete>

    <delete id="deletePharmacyManagementByIds" parameterType="String">
        delete from t_pharmacy_management where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
