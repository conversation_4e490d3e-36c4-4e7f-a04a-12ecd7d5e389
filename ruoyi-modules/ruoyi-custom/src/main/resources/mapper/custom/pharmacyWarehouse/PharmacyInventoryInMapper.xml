<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryInMapper">

    <resultMap type="PharmacyInventoryIn" id="PharmacyInventoryInResult">
        <result property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="inventoryId" column="inventory_id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="pharmacyId" column="pharmacy_id"/>
        <result property="pharmacyName" column="pharmacy_name"/>
        <result property="batch" column="batch"/>
        <result property="specifications" column="specifications"/>
        <result property="unit" column="unit"/>
        <result property="quantity" column="quantity"/>
        <result property="marketPrice" column="market_price"/>
        <result property="costPrice" column="cost_price"/>
        <result property="productionTime" column="production_time"/>
        <result property="shelfLife" column="shelf_life"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="source" column="source"/>
        <result property="inTime" column="in_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectPharmacyInventoryInVo">
        SELECT id,
               serial_number,
               inventory_id,
               warehouse_id,
               warehouse_name,
               pharmacy_id,
               pharmacy_name,
               batch,
               specifications,
               unit,
               quantity,
               market_price,
               cost_price,
               production_time,
               shelf_life,
               operator_id,
               operator_name,
               source,
               in_time,
               create_time
        FROM t_pharmacy_inventory_in
    </sql>

    <select id="selectPharmacyInventoryInList" parameterType="PharmacyInventoryIn"
            resultMap="PharmacyInventoryInResult">
        <include refid="selectPharmacyInventoryInVo"/>
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="inventoryId != null ">
                and inventory_id = #{inventoryId}
            </if>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="warehouseName != null  and warehouseName != ''">
                and warehouse_name like concat('%', #{warehouseName}, '%')
            </if>
            <if test="pharmacyId != null ">
                and pharmacy_id = #{pharmacyId}
            </if>
            <if test="pharmacyName != null  and pharmacyName != ''">
                and pharmacy_name like concat('%', #{pharmacyName}, '%')
            </if>
            <if test="batch != null ">
                and batch = #{batch}
            </if>
            <if test="specifications != null  and specifications != ''">
                and specifications = #{specifications}
            </if>
            <if test="unit != null  and unit != ''">
                and unit = #{unit}
            </if>
            <if test="quantity != null ">
                and quantity = #{quantity}
            </if>
            <if test="marketPrice != null ">
                and market_price = #{marketPrice}
            </if>
            <if test="costPrice != null ">
                and cost_price = #{costPrice}
            </if>
            <if test="productionTime != null ">
                and production_time = #{productionTime}
            </if>
            <if test="shelfLife != null ">
                and shelf_life = #{shelfLife}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
            <if test="operatorName != null  and operatorName != ''">
                and operator_name like concat('%', #{operatorName}, '%')
            </if>
            <if test="source != null  and source != ''">
                and source = #{source}
            </if>
            <if test="inTime != null ">
                and in_time = #{inTime}
            </if>
        </where>
    </select>

    <select id="selectPharmacyInventoryInById" parameterType="Long"
            resultMap="PharmacyInventoryInResult">
        <include refid="selectPharmacyInventoryInVo"/>
        where id = #{id}
    </select>

    <select id="selectPharmacyInventoryInList2" parameterType="PharmacyInventoryIn"
            resultMap="PharmacyInventoryInResult">
        SELECT id,
        serial_number,
        warehouse_id,
        warehouse_name,
        GROUP_CONCAT(DISTINCT pharmacy_name) AS pharmacy_name,
        operator_id,
        operator_name,
        source,
        in_time,
        create_time
        FROM t_pharmacy_inventory_in
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
            <if test="source != null and source != ''">
                and source = #{source}
            </if>
            <if test="params.beginInTime != null and params.endInTime != null">
                and in_time between #{params.beginInTime} and #{params.endInTime}
            </if>
        </where>
        GROUP BY serial_number
        <if test="pharmacyName != null and pharmacyName != ''">
            HAVING GROUP_CONCAT(DISTINCT pharmacy_name) like concat('%', #{pharmacyName}, '%')
        </if>
        <if test="params.content != null and params.content != ''">
            HAVING GROUP_CONCAT(DISTINCT pharmacy_name) like concat('%', #{params.content}, '%') or serial_number like concat('%', #{params.content}, '%')
        </if>
    </select>

    <insert id="insertPharmacyInventoryIn" parameterType="PharmacyInventoryIn" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_pharmacy_inventory_in
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number,
            </if>
            <if test="inventoryId != null">inventory_id,
            </if>
            <if test="warehouseId != null">warehouse_id,
            </if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,
            </if>
            <if test="pharmacyId != null">pharmacy_id,
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">pharmacy_name,
            </if>
            <if test="batch != null">batch,
            </if>
            <if test="specifications != null and specifications != ''">specifications,
            </if>
            <if test="unit != null and unit != ''">unit,
            </if>
            <if test="quantity != null">quantity,
            </if>
            <if test="marketPrice != null">market_price,
            </if>
            <if test="costPrice != null">cost_price,
            </if>
            <if test="productionTime != null">production_time,
            </if>
            <if test="shelfLife != null">shelf_life,
            </if>
            <if test="operatorId != null">operator_id,
            </if>
            <if test="operatorName != null and operatorName != ''">operator_name,
            </if>
            <if test="source != null and source != ''">source,
            </if>
            <if test="inTime != null">in_time,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},
            </if>
            <if test="inventoryId != null">#{inventoryId},
            </if>
            <if test="warehouseId != null">#{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},
            </if>
            <if test="pharmacyId != null">#{pharmacyId},
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">#{pharmacyName},
            </if>
            <if test="batch != null">#{batch},
            </if>
            <if test="specifications != null and specifications != ''">#{specifications},
            </if>
            <if test="unit != null and unit != ''">#{unit},
            </if>
            <if test="quantity != null">#{quantity},
            </if>
            <if test="marketPrice != null">#{marketPrice},
            </if>
            <if test="costPrice != null">#{costPrice},
            </if>
            <if test="productionTime != null">#{productionTime},
            </if>
            <if test="shelfLife != null">#{shelfLife},
            </if>
            <if test="operatorId != null">#{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},
            </if>
            <if test="source != null and source != ''">#{source},
            </if>
            <if test="inTime != null">#{inTime},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch">
        INSERT INTO t_pharmacy_inventory_in
        (serial_number,inventory_id,warehouse_id,warehouse_name,pharmacy_id,pharmacy_name,batch,specifications,unit,quantity,market_price,cost_price,production_time,shelf_life,operator_id,operator_name,source,in_time,create_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.serialNumber},#{item.inventoryId},#{item.warehouseId},#{item.warehouseName},#{item.pharmacyId},#{item.pharmacyName},#{item.batch},#{item.specifications},#{item.unit},#{item.quantity},#{item.marketPrice},#{item.costPrice},#{item.productionTime},#{item.shelfLife},#{item.operatorId},#{item.operatorName},#{item.source},#{item.inTime},#{item.createTime})
        </foreach>
    </insert>

    <update id="updatePharmacyInventoryIn" parameterType="PharmacyInventoryIn">
        update t_pharmacy_inventory_in
        <trim prefix="SET" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number =
                #{serialNumber},
            </if>
            <if test="inventoryId != null">inventory_id =
                #{inventoryId},
            </if>
            <if test="warehouseId != null">warehouse_id =
                #{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name =
                #{warehouseName},
            </if>
            <if test="pharmacyId != null">pharmacy_id =
                #{pharmacyId},
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">pharmacy_name =
                #{pharmacyName},
            </if>
            <if test="batch != null">batch =
                #{batch},
            </if>
            <if test="specifications != null and specifications != ''">specifications =
                #{specifications},
            </if>
            <if test="unit != null and unit != ''">unit =
                #{unit},
            </if>
            <if test="quantity != null">quantity =
                #{quantity},
            </if>
            <if test="marketPrice != null">market_price =
                #{marketPrice},
            </if>
            <if test="costPrice != null">cost_price =
                #{costPrice},
            </if>
            <if test="productionTime != null">production_time =
                #{productionTime},
            </if>
            <if test="shelfLife != null">shelf_life =
                #{shelfLife},
            </if>
            <if test="operatorId != null">operator_id =
                #{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">operator_name =
                #{operatorName},
            </if>
            <if test="source != null and source != ''">source =
                #{source},
            </if>
            <if test="inTime != null">in_time =
                #{inTime},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePharmacyInventoryInById" parameterType="Long">
        DELETE
        FROM t_pharmacy_inventory_in
        WHERE id = #{id}
    </delete>

    <delete id="deletePharmacyInventoryInByIds" parameterType="String">
        delete from t_pharmacy_inventory_in where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
