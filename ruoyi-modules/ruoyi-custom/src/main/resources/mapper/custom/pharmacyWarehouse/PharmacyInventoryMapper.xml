<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryMapper">

    <resultMap type="PharmacyInventory" id="PharmacyInventoryResult">
        <result property="id" column="id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="pharmacyId" column="pharmacy_id"/>
        <result property="batch" column="batch"/>
        <result property="specifications" column="specifications"/>
        <result property="unit" column="unit"/>
        <result property="quantity" column="quantity"/>
        <result property="costPrice" column="cost_price"/>
        <result property="marketPrice" column="market_price"/>
        <result property="productionTime" column="production_time"/>
        <result property="shelfLife" column="shelf_life"/>

        <result property="warehouseName" column="warehouse_name"/>

        <result property="pharmacyName" column="pharmacy_name"/>

        <result property="pharmacyTypeName" column="type_name"/>
    </resultMap>

    <sql id="selectPharmacyInventoryVo">
        SELECT t.id,
               t.warehouse_id,
               t.pharmacy_id,
               t.batch,
               t.specifications,
               t.unit,
               t.quantity,
               t.cost_price,
               t.market_price,
               t.production_time,
               t.shelf_life,

               t2.name warehouse_name,

               t3.name pharmacy_name,

               t4.name type_name
        FROM t_pharmacy_inventory t
                 LEFT JOIN t_pharmacy_warehouse t2 ON t.warehouse_id = t2.id
                 LEFT JOIN t_pharmacy_management t3 ON t.pharmacy_id = t3.id
                 LEFT JOIN t_pharmacy_type t4 ON t3.type_id = t4.id
    </sql>

    <select id="selectPharmacyInventoryList" parameterType="PharmacyInventory" resultMap="PharmacyInventoryResult">
        <include refid="selectPharmacyInventoryVo"/>
        <where>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="pharmacyId != null ">
                and pharmacy_id = #{pharmacyId}
            </if>
            <if test="batch != null ">
                and batch = #{batch}
            </if>
            <if test="specifications != null  and specifications != ''">
                and specifications = #{specifications}
            </if>
            <if test="unit != null  and unit != ''">
                and unit = #{unit}
            </if>
            <if test="quantity != null ">
                and quantity = #{quantity}
            </if>
            <if test="costPrice != null ">
                and cost_price = #{costPrice}
            </if>
            <if test="marketPrice != null ">
                and market_price = #{marketPrice}
            </if>
            <if test="productionTime != null ">
                and production_time = #{productionTime}
            </if>
            <if test="shelfLife != null ">
                and shelf_life = #{shelfLife}
            </if>
        </where>
    </select>

    <select id="selectPharmacyInventoryById" parameterType="Long"
            resultMap="PharmacyInventoryResult">
        <include refid="selectPharmacyInventoryVo"/>
        where id = #{id}
    </select>

    <select id="selectPharmacyInventoryList2" parameterType="PharmacyInventory"
            resultMap="PharmacyInventoryResult">
        SELECT t.pharmacy_id,
        t3.name pharmacy_name,
        t4.name type_name,
        SUM(t.quantity) AS quantity
        FROM t_pharmacy_inventory t
        LEFT JOIN t_pharmacy_warehouse t2 ON t.warehouse_id = t2.id
        LEFT JOIN t_pharmacy_management t3 ON t.pharmacy_id = t3.id
        LEFT JOIN t_pharmacy_type t4 ON t3.type_id = t4.id
        <where>
            <if test="pharmacyName != null and pharmacyName != '' ">
                and t3.name LIKE CONCAT('%',#{pharmacyName},'%')
            </if>
            <if test="typeId != null ">
                and t3.type_id = #{typeId}
            </if>
        </where>
        GROUP BY t.pharmacy_id
        <if test="params.beginQuantity != null and params.endQuantity != null">
            HAVING SUM(t.quantity) BETWEEN #{params.beginQuantity} AND #{params.endQuantity}
        </if>
        ORDER BY quantity DESC
    </select>

    <select id="selectPharmacyInventoryByUnionId"
            resultMap="PharmacyInventoryResult">
        <include refid="selectPharmacyInventoryVo"/>
        WHERE warehouse_id = #{warehouseId}
        AND pharmacy_id = #{pharmacyId}
        AND batch = #{batch}
    </select>

    <select id="getWarehouseListInInventory" resultType="PharmacyWarehouse">
        SELECT id, name, location, manager_id, manager
        FROM t_pharmacy_warehouse
        WHERE id IN (SELECT DISTINCT warehouse_id FROM t_pharmacy_inventory)
    </select>

    <select id="getPharmacyListInInventory"
            resultType="com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyManagement">
        SELECT t.id,
               t.name,
               t.type_id,
               t.create_time
        FROM t_pharmacy_management t
        WHERE t.id IN (SELECT DISTINCT pharmacy_id FROM t_pharmacy_inventory WHERE warehouse_id = #{warehouseId})
    </select>

    <select id="getBatchList" resultType="java.lang.Integer">
        SELECT batch
        FROM t_pharmacy_inventory
        WHERE warehouse_id = #{warehouseId}
          AND pharmacy_id = #{pharmacyId}
    </select>

    <select id="getCurrQuantityById" resultType="java.lang.Integer">
        SELECT quantity
        FROM t_pharmacy_inventory
        WHERE id = #{inventoryId} FOR
        UPDATE
    </select>

    <insert id="insertPharmacyInventory" parameterType="PharmacyInventory" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_pharmacy_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id,
            </if>
            <if test="pharmacyId != null">pharmacy_id,
            </if>
            <if test="batch != null">batch,
            </if>
            <if test="specifications != null and specifications != ''">specifications,
            </if>
            <if test="unit != null and unit != ''">unit,
            </if>
            <if test="quantity != null">quantity,
            </if>
            <if test="costPrice != null">cost_price,
            </if>
            <if test="marketPrice != null">market_price,
            </if>
            <if test="productionTime != null">production_time,
            </if>
            <if test="shelfLife != null">shelf_life,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">#{warehouseId},
            </if>
            <if test="pharmacyId != null">#{pharmacyId},
            </if>
            <if test="batch != null">#{batch},
            </if>
            <if test="specifications != null and specifications != ''">#{specifications},
            </if>
            <if test="unit != null and unit != ''">#{unit},
            </if>
            <if test="quantity != null">#{quantity},
            </if>
            <if test="costPrice != null">#{costPrice},
            </if>
            <if test="marketPrice != null">#{marketPrice},
            </if>
            <if test="productionTime != null">#{productionTime},
            </if>
            <if test="shelfLife != null">#{shelfLife},
            </if>
        </trim>
    </insert>

    <update id="updatePharmacyInventory" parameterType="PharmacyInventory">
        update t_pharmacy_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id =
                #{warehouseId},
            </if>
            <if test="pharmacyId != null">pharmacy_id =
                #{pharmacyId},
            </if>
            <if test="batch != null">batch =
                #{batch},
            </if>
            <if test="specifications != null and specifications != ''">specifications =
                #{specifications},
            </if>
            <if test="unit != null and unit != ''">unit =
                #{unit},
            </if>
            <if test="quantity != null">quantity =
                #{quantity},
            </if>
            <if test="costPrice != null">cost_price =
                #{costPrice},
            </if>
            <if test="marketPrice != null">market_price =
                #{marketPrice},
            </if>
            <if test="productionTime != null">production_time =
                #{productionTime},
            </if>
            <if test="shelfLife != null">shelf_life =
                #{shelfLife},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateQuantity">
        UPDATE t_pharmacy_inventory
        SET quantity = quantity + #{quantity}
        WHERE id = #{id}
    </update>

    <delete id="deletePharmacyInventoryById" parameterType="Long">
        DELETE
        FROM t_pharmacy_inventory
        WHERE id = #{id}
    </delete>

    <delete id="deletePharmacyInventoryByIds" parameterType="String">
        delete from t_pharmacy_inventory where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
