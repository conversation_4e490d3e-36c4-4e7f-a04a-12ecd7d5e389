<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryCheckMapper">

    <resultMap type="PharmacyInventoryCheck" id="PharmacyInventoryCheckResult">
        <result property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="checkName" column="check_name"/>
        <result property="inventoryId" column="inventory_id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="pharmacyId" column="pharmacy_id"/>
        <result property="pharmacyName" column="pharmacy_name"/>
        <result property="batch" column="batch"/>
        <result property="checkQuantity" column="check_quantity"/>
        <result property="realQuantity" column="real_quantity"/>
        <result property="profitLossQuantity" column="profit_loss_quantity"/>
        <result property="planStartDate" column="plan_start_date"/>
        <result property="planEndDate" column="plan_end_date"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>

        <result property="planTimeStr" column="plan_time_str"/>
    </resultMap>

    <sql id="selectPharmacyInventoryCheckVo">
        SELECT id,
               serial_number,
               check_name,
               inventory_id,
               warehouse_id,
               warehouse_name,
               pharmacy_id,
               pharmacy_name,
               batch,
               check_quantity,
               real_quantity,
               profit_loss_quantity,
               plan_start_date,
               plan_end_date,
               operator_id,
               operator_name,
               status,
               create_time
        FROM t_pharmacy_inventory_check
    </sql>

    <select id="selectPharmacyInventoryCheckList" parameterType="PharmacyInventoryCheck"
            resultMap="PharmacyInventoryCheckResult">
        <include refid="selectPharmacyInventoryCheckVo"/>
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="checkName != null  and checkName != ''">
                and check_name like concat('%', #{checkName}, '%')
            </if>
            <if test="inventoryId != null ">
                and inventory_id = #{inventoryId}
            </if>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="warehouseName != null  and warehouseName != ''">
                and warehouse_name like concat('%', #{warehouseName}, '%')
            </if>
            <if test="pharmacyId != null ">
                and pharmacy_id = #{pharmacyId}
            </if>
            <if test="pharmacyName != null  and pharmacyName != ''">
                and pharmacy_name like concat('%', #{pharmacyName}, '%')
            </if>
            <if test="batch != null ">
                and batch = #{batch}
            </if>
            <if test="checkQuantity != null ">
                and check_quantity = #{checkQuantity}
            </if>
            <if test="planStartDate != null ">
                and plan_start_date = #{planStartDate}
            </if>
            <if test="planEndDate != null ">
                and plan_end_date = #{planEndDate}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
            <if test="operatorName != null  and operatorName != ''">
                and operator_name like concat('%', #{operatorName}, '%')
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectPharmacyInventoryCheckById" parameterType="Long"
            resultMap="PharmacyInventoryCheckResult">
        <include refid="selectPharmacyInventoryCheckVo"/>
        where id = #{id}
    </select>

    <select id="selectPharmacyInventoryCheckList2" parameterType="PharmacyInventoryCheck"
            resultMap="PharmacyInventoryCheckResult">
        SELECT id,
        serial_number,
        warehouse_id,
        warehouse_name,
        check_name,
        operator_id,
        operator_name,
        concat(DATE_FORMAT(plan_start_date,'%Y-%m-%d'), ' ~ ', DATE_FORMAT(plan_end_date,'%Y-%m-%d')) AS plan_time_str,
        status,
        create_time
        FROM t_pharmacy_inventory_check
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="checkName != null and checkName != ''">
                and check_name like concat('%', #{checkName}, '%')
            </if>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
            <if test="operatorId != null ">
                and operator_id = #{operatorId}
            </if>
            <if test="planStartDate != null and planEndDate != null">
                plan_start_date >= #{planStartDate} and plan_end_date <![CDATA[ <= #{planEndDate}
            ]]></if>
        </where>
        GROUP BY serial_number
        ORDER BY serial_number DESC
    </select>

    <insert id="insertPharmacyInventoryCheck" parameterType="PharmacyInventoryCheck" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_pharmacy_inventory_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number,
            </if>
            <if test="checkName != null and checkName != ''">check_name,
            </if>
            <if test="inventoryId != null">inventory_id,
            </if>
            <if test="warehouseId != null">warehouse_id,
            </if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,
            </if>
            <if test="pharmacyId != null">pharmacy_id,
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">pharmacy_name,
            </if>
            <if test="batch != null">batch,
            </if>
            <if test="checkQuantity != null">check_quantity,
            </if>
            <if test="realQuantity != null">real_quantity,
            </if>
            <if test="profitLossQuantity != null">profit_loss_quantity,
            </if>
            <if test="planStartDate != null">plan_start_date,
            </if>
            <if test="planEndDate != null">plan_end_date,
            </if>
            <if test="operatorId != null">operator_id,
            </if>
            <if test="operatorName != null and operatorName != ''">operator_name,
            </if>
            <if test="status != null and status != ''">status,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},
            </if>
            <if test="checkName != null and checkName != ''">#{checkName},
            </if>
            <if test="inventoryId != null">#{inventoryId},
            </if>
            <if test="warehouseId != null">#{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},
            </if>
            <if test="pharmacyId != null">#{pharmacyId},
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">#{pharmacyName},
            </if>
            <if test="batch != null">#{batch},
            </if>
            <if test="checkQuantity != null">#{checkQuantity},
            </if>
            <if test="realQuantity != null">#{realQuantity},
            </if>
            <if test="profitLossQuantity != null">#{profitLossQuantity},
            </if>
            <if test="planStartDate != null">#{planStartDate},
            </if>
            <if test="planEndDate != null">#{planEndDate},
            </if>
            <if test="operatorId != null">#{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},
            </if>
            <if test="status != null and status != ''">#{status},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch">
        INSERT INTO t_pharmacy_inventory_check (serial_number, check_name, inventory_id, warehouse_id, warehouse_name,
        pharmacy_id, pharmacy_name, batch, check_quantity, real_quantity, profit_loss_quantity, plan_start_date,
        plan_end_date, operator_id, operator_name, status, create_time) VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.serialNumber}, #{item.checkName}, #{item.inventoryId}, #{item.warehouseId}, #{item.warehouseName},
            #{item.pharmacyId}, #{item.pharmacyName}, #{item.batch}, #{item.checkQuantity}, #{item.realQuantity},
            #{item.profitLossQuantity}, #{item.planStartDate}, #{item.planEndDate}, #{item.operatorId},
            #{item.operatorName}, #{item.status}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updatePharmacyInventoryCheck" parameterType="PharmacyInventoryCheck">
        update t_pharmacy_inventory_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number =
                #{serialNumber},
            </if>
            <if test="checkName != null and checkName != ''">check_name =
                #{checkName},
            </if>
            <if test="inventoryId != null">inventory_id =
                #{inventoryId},
            </if>
            <if test="warehouseId != null">warehouse_id =
                #{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name =
                #{warehouseName},
            </if>
            <if test="pharmacyId != null">pharmacy_id =
                #{pharmacyId},
            </if>
            <if test="pharmacyName != null and pharmacyName != ''">pharmacy_name =
                #{pharmacyName},
            </if>
            <if test="batch != null">batch =
                #{batch},
            </if>
            <if test="checkQuantity != null">check_quantity =
                #{checkQuantity},
            </if>
            <if test="realQuantity != null">real_quantity =
                #{realQuantity},
            </if>
            <if test="profitLossQuantity != null">profit_loss_quantity =
                #{profitLossQuantity},
            </if>
            <if test="planStartDate != null">plan_start_date =
                #{planStartDate},
            </if>
            <if test="planEndDate != null">plan_end_date =
                #{planEndDate},
            </if>
            <if test="operatorId != null">operator_id =
                #{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">operator_name =
                #{operatorName},
            </if>
            <if test="status != null and status != ''">status =
                #{status},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePharmacyInventoryCheckById" parameterType="Long">
        DELETE
        FROM t_pharmacy_inventory_check
        WHERE id = #{id}
    </delete>

    <delete id="deletePharmacyInventoryCheckByIds" parameterType="String">
        delete from t_pharmacy_inventory_check where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBySerialNumber">
        DELETE
        FROM t_pharmacy_inventory_check
        WHERE serial_number = #{serialNumber}
    </delete>
</mapper>
