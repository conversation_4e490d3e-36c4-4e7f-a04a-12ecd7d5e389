<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.assessment.mapper.AssessmentTemplateMapper">

    <resultMap type="AssessmentTemplate" id="AssessmentTemplateResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="standard" column="standard"/>
        <result property="type" column="type"/>
        <result property="description" column="description"/>
        <result property="indicator" column="indicator"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
        <result property="createTime" column="create_time"/>
        <result property="grade" column="grade"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>

        <result property="typeName" column="type_name"/>
    </resultMap>

    <sql id="selectAssessmentTemplateVo">
        SELECT t.id,
               t.name,
               t.standard,
               t.type,
               t.description,
               t.indicator,
               t.grade,
               t.create_time,
               t.update_time,
               t.del_flag,
               t.create_by,
               t.update_by,

               t2.name AS type_name
        FROM t_assessment_template AS t
                 LEFT JOIN t_assessment_template_type AS t2 ON t.type = t2.id
    </sql>

    <select id="selectAssessmentTemplateList" parameterType="AssessmentTemplate" resultMap="AssessmentTemplateResult">
        <include refid="selectAssessmentTemplateVo"/>
        <where>
            t.del_flag = '0' AND t2.del_flag = '0'
            <if test="name != null  and name != ''">
                and t.name like concat('%', #{name}, '%')
            </if>
            <if test="standard != null  and standard != ''">
                and t.standard = #{standard}
            </if>
            <if test="type != null ">
                and t.type = #{type}
            </if>
            <if test="description != null  and description != ''">
                and t.description = #{description}
            </if>
        </where>
    </select>

    <select id="selectAssessmentTemplateById" parameterType="Long"
            resultMap="AssessmentTemplateResult">
        <include refid="selectAssessmentTemplateVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertAssessmentTemplate" parameterType="AssessmentTemplate" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_assessment_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,
            </if>
            <if test="standard != null and standard != ''">standard,
            </if>
            <if test="type != null">type,
            </if>
            <if test="description != null">description,
            </if>
            <if test="indicator != null">indicator,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="standard != null and standard != ''">#{standard},
            </if>
            <if test="type != null">#{type},
            </if>
            <if test="description != null">#{description},
            </if>
            <if test="indicator != null">
                #{indicator, jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
        </trim>
    </insert>

    <update id="updateAssessmentTemplate" parameterType="AssessmentTemplate">
        update t_assessment_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="standard != null and standard != ''">standard =
                #{standard},
            </if>
            <if test="type != null">type =
                #{type},
            </if>
            <if test="description != null">description =
                #{description},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssessmentTemplateById" parameterType="Long">
        DELETE
        FROM t_assessment_template
        WHERE id = #{id}
    </delete>

    <delete id="deleteAssessmentTemplateByIds">
        UPDATE t_assessment_template
        SET del_flag = #{params.delFlag}, update_by = #{params.updateBy}, update_time = #{params.updateTime}
        WHERE id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectIndicatorByTemplateId" resultType="Indicator">
        SELECT t.id AS templateId,
               jt.id,
               jt.indicator,
               jt.sort,
               jt.topScoreTotal
        FROM t_assessment_template t,
             JSON_TABLE(
                     t.indicator,
                     '$[*]' COLUMNS (
                         id VARCHAR(64) PATH '$.id',
                         indicator VARCHAR(255) PATH '$.indicator',
                         sort INT PATH '$.sort',
                         topScoreTotal INT PATH '$.topScoreTotal'
                         )
             ) AS jt
        WHERE t.id = #{templateId}
        ORDER BY jt.sort DESC
    </select>


    <select id="selectIndicatorByIndicatorId" resultType="Indicator">
        SELECT jt.id,
               jt.indicator,
               jt.sort,
               jt.topScoreTotal
        FROM t_assessment_template t,
             JSON_TABLE(
                     t.indicator,
                     '$[*]' COLUMNS (
                         id VARCHAR(64) PATH '$.id',
                         indicator VARCHAR(255) PATH '$.indicator',
                         sort INT PATH '$.sort',
                         topScoreTotal INT PATH '$.topScoreTotal'
                         )
             ) AS jt
        WHERE t.id = #{templateId}
          AND jt.id = #{id}
    </select>

    <update id="appendIndicatorToJson" parameterType="Indicator">
        UPDATE t_assessment_template
        SET indicator = JSON_ARRAY_APPEND(
                COALESCE(indicator, JSON_ARRAY()),
                '$',
                JSON_OBJECT(
                        'id', UUID(),
                        'indicator', #{indicator},
                        'sort', #{sort},
                        'subject', JSON_ARRAY()
                )
                        )
        WHERE id = #{templateId}
    </update>

    <update id="editIndicatorToJson" parameterType="Indicator">
        UPDATE t_assessment_template
        SET indicator = JSON_SET(
                indicator,
                REPLACE(JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{id}, NULL, '$[*].id')), 'id', 'indicator'),
                #{indicator},
                REPLACE(JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{id}, NULL, '$[*].id')), 'id', 'sort'),
                #{sort}
                        )
        WHERE id = #{templateId}
    </update>

    <update id="deleteIndicatorById" parameterType="Indicator">
        UPDATE t_assessment_template
        SET indicator = JSON_REMOVE(indicator,
                                    REPLACE(JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{id}, NULL, '$[*].id')), '.id',
                                            ''))
        WHERE id = #{templateId}
    </update>

    <!--    根据模板id和指标id查询标题列表-->
    <select id="selectQuestionByCondition" resultType="com.ruoyi.custom.admin.assessment.domain.Question">
        SELECT jt2.id,
               jt2.title,
               jt2.sort
        FROM t_assessment_template t,
             JSON_TABLE(
                     t.indicator,
                     '$[*]' COLUMNS (
                         id VARCHAR(64) PATH '$.id',
                         indicator VARCHAR(255) PATH '$.indicator',
                         subject JSON PATH '$.subject'
                         )
             ) AS jt,
             JSON_TABLE(
                     jt.subject,
                     '$[*]' COLUMNS (
                         id VARCHAR(64) PATH '$.id',
                         title VARCHAR(255) PATH '$.title',
                         sort INT PATH '$.sort'
                         )
             ) AS jt2
        WHERE t.id = #{templateId}
          AND jt.id = #{assessmentId}
        ORDER BY jt2.sort
    </select>

    <select id="selectQuestionWithDetails" resultMap="questionResultMap">
        WITH PathCTE AS (SELECT t.id AS templateId,
                                REPLACE(
                                        JSON_UNQUOTE(
                                                JSON_SEARCH(t.indicator, 'one', #{id}, NULL, '$[*].subject[*].id')
                                        ),
                                        '.id',
                                        ''
                                )    AS questionPath
                         FROM t_assessment_template t
                         WHERE t.id = #{templateId}
                           AND JSON_SEARCH(t.indicator, 'one', #{id}, NULL, '$[*].subject[*].id') IS NOT NULL)
        SELECT p.templateId,
               JSON_UNQUOTE(JSON_EXTRACT(t.indicator, CONCAT(p.questionPath, '.id')))    AS questionId,
               JSON_UNQUOTE(JSON_EXTRACT(t.indicator, CONCAT(p.questionPath, '.title'))) AS title,
               JSON_EXTRACT(t.indicator, CONCAT(p.questionPath, '.sort'))                AS sort,
               JSON_EXTRACT(t.indicator, CONCAT(p.questionPath, '.detail'))              AS detailArray
        FROM t_assessment_template t
                 JOIN
             PathCTE p ON t.id = p.templateId
        ORDER BY sort DESC
    </select>

    <resultMap id="questionResultMap" type="Question">
        <result property="templateId" column="templateId"/>
        <result property="id" column="questionId"/>
        <result property="title" column="title"/>
        <result property="sort" column="sort" javaType="java.lang.Integer"/>
        <collection
                property="detail"
                ofType="QuestionDetail"
                select="parseDetails"
                column="{detailArray=detailArray}"/>
    </resultMap>

    <select id="parseDetails" resultType="QuestionDetail">
        SELECT JSON_UNQUOTE(JSON_EXTRACT(detail, '$.id'))      AS id,
               JSON_EXTRACT(detail, '$.score')                 AS score,
               JSON_UNQUOTE(JSON_EXTRACT(detail, '$.content')) AS content
        FROM JSON_TABLE(
                     #{detailArray},
                     '$[*]' COLUMNS (detail JSON PATH '$')
             ) AS details
    </select>

    <update id="appendQuestionToJson">
        UPDATE t_assessment_template
        SET indicator = JSON_SET(
        indicator,
        REPLACE(
        JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{assessmentId}, NULL, '$[*].id')),
        'id',
        'subject'
        ),
        JSON_ARRAY_APPEND(
        JSON_EXTRACT(
        indicator,
        REPLACE(
        JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{assessmentId}, NULL, '$[*].id')),
        'id',
        'subject'
        )
        ),
        '$',
        JSON_OBJECT(
        'id', UUID(),
        'title', #{title},
        'sort', (
        SELECT IFNULL(
        MAX(
        CAST(
        JSON_EXTRACT(item, '$.sort') AS UNSIGNED
        )
        ) + 1,
        1
        )
        FROM JSON_TABLE(
        JSON_EXTRACT(
        indicator,
        REPLACE(
        JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{assessmentId}, NULL, '$[*].id')),
        'id',
        'subject'
        )
        ),
        '$[*]' COLUMNS (
        item JSON PATH '$'
        )
        ) AS jt
        ),
        'detail',
        <if test="detail != null and !detail.isEmpty()">
            JSON_ARRAY(
            <foreach collection="detail" item="detail" separator=",">
                JSON_OBJECT(
                'id', UUID(),
                'score', #{detail.score},
                'content', #{detail.content}
                )
            </foreach>
            )
        </if>
        <if test="detail == null or detail.isEmpty()">
            JSON_ARRAY()
        </if>
        )
        )
        )
        WHERE id = #{templateId}
    </update>

    <!--    根据模板id、指标id、id(标题id)修改-->
    <update id="editQuestionToJson">
        UPDATE t_assessment_template
        SET indicator = JSON_SET(
        indicator
        <if test="title != null and title != ''">
            , CONCAT(
            REPLACE(
            JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{id})),
            '.id',
            ''
            ),
            '.title'
            ),
            #{title}
        </if>
        <if test="sort != null">
            , CONCAT(
            REPLACE(
            JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{id})),
            '.id',
            ''
            ),
            '.sort'
            ),
            #{sort}
        </if>
        <if test="detail != null and !detail.isEmpty()">
            , CONCAT(
            REPLACE(
            JSON_UNQUOTE(JSON_SEARCH(indicator, 'one', #{id})),
            '.id',
            ''
            ),
            '.detail'
            ),
            JSON_ARRAY(
            <foreach collection="detail" item="detail" separator=",">
                JSON_OBJECT(
                'id', UUID(),
                'score', #{detail.score},
                'content', #{detail.content}
                )
            </foreach>
            )
        </if>
        )
        WHERE id = #{templateId}
    </update>

    <update id="deleteQuestionById">
        UPDATE t_assessment_template
        SET indicator = JSON_REMOVE(
                indicator,
            -- 1. 定位目标对象路径（如 "$[0].subject[2]"）
                REPLACE(
                        JSON_UNQUOTE(
                                JSON_SEARCH(indicator, 'one', #{id})
                        ),
                        '.id', -- 将 ".id" 替换为空字符串
                        ''
                )
                        )
        WHERE id = #{templateId};
    </update>

    <select id="gradeGetInfo" resultMap="gradeResultMap">
        WITH PathCTE AS (SELECT t.id            AS templateId,
                                #{assessmentId} AS assessmentId,
                                REPLACE(
                                        JSON_UNQUOTE(
                                                JSON_SEARCH(t.indicator, 'one', #{assessmentId}, NULL, '$[*].id')
                                        ),
                                        '.id',
                                        '.grade' -- 定位到 grade 对象
                                )               AS gradePath,
                                REPLACE(
                                        JSON_UNQUOTE(
                                                JSON_SEARCH(t.indicator, 'one', #{assessmentId}, NULL, '$[*].id')
                                        ),
                                        '.id',
                                        '.topScoreTotal' -- 定位到 topScoreTotal 对象
                                )               AS topScoreTotalPath
                         FROM t_assessment_template t
                         WHERE t.id = #{templateId}
                           AND JSON_SEARCH(t.indicator, 'one', #{assessmentId}, NULL, '$[*].id') IS NOT NULL)
        SELECT p.templateId,
               p.assessmentId,
               FLOOR(JSON_EXTRACT(t.indicator, p.topScoreTotalPath))                     AS topScoreTotal,
               -- 提取各个评分区间
               JSON_UNQUOTE(JSON_EXTRACT(t.indicator, CONCAT(p.gradePath, '.full')))     AS full,
               JSON_UNQUOTE(JSON_EXTRACT(t.indicator, CONCAT(p.gradePath, '.mild')))     AS mild,
               JSON_UNQUOTE(JSON_EXTRACT(t.indicator, CONCAT(p.gradePath, '.moderate'))) AS moderate,
               JSON_UNQUOTE(JSON_EXTRACT(t.indicator, CONCAT(p.gradePath, '.severe')))   AS severe
        FROM t_assessment_template t
                 JOIN
             PathCTE p ON t.id = p.templateId
    </select>

    <!-- 结果映射（使用自定义类型处理器） -->
    <resultMap id="gradeResultMap" type="com.ruoyi.custom.admin.assessment.domain.Grade">
        <result property="templateId" column="templateId"/>
        <result property="assessmentId" column="assessmentId"/>
        <result property="topScoreTotal" column="topScoreTotal"/>
        <result property="full" column="full"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
        <result property="mild" column="mild"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
        <result property="moderate" column="moderate"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
        <result property="severe" column="severe"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
    </resultMap>

    <select id="gradeGetInfoOverall" resultMap="gradeResultMap">
        SELECT #{templateId}                                   AS templateId,
               FLOOR(JSON_EXTRACT(grade, '$.topScoreTotal'))   AS topScoreTotal,
               JSON_UNQUOTE(JSON_EXTRACT(grade, '$.full'))     AS full,
               JSON_UNQUOTE(JSON_EXTRACT(grade, '$.mild'))     AS mild,
               JSON_UNQUOTE(JSON_EXTRACT(grade, '$.moderate')) AS moderate,
               JSON_UNQUOTE(JSON_EXTRACT(grade, '$.severe'))   AS severe
        FROM t_assessment_template
        WHERE id = #{templateId}
    </select>

    <update id="gradeSave">
        WITH PathCTE AS (SELECT REPLACE(
                                        JSON_UNQUOTE(
                                                JSON_SEARCH(indicator, 'one', #{assessmentId}, NULL, '$[*].id')
                                        ),
                                        '.id',
                                        '.grade'
                                ) AS basePath,   -- 计算基础路径（如 "$[0].grade"）
                                REPLACE(
                                        JSON_UNQUOTE(
                                                JSON_SEARCH(indicator, 'one', #{assessmentId}, NULL, '$[*].id')
                                        ),
                                        '.id',
                                        ''
                                ) AS elementPath -- 获取元素基础路径（如 "$[0]"）
                         FROM t_assessment_template
                         WHERE id = #{templateId})
        UPDATE t_assessment_template
        SET indicator = JSON_SET(
            -- 首先确保元素存在grade对象，如果不存在则创建空对象
                JSON_SET(
                        COALESCE(indicator, JSON_OBJECT()),
                        (SELECT CONCAT(elementPath, '.grade') FROM PathCTE),
                        COALESCE(
                                JSON_EXTRACT(indicator, (SELECT CONCAT(elementPath, '.grade') FROM PathCTE)),
                                JSON_OBJECT()
                        )
                ),
            -- 动态拼接完整路径设置grade的各个属性
                CONCAT((SELECT basePath FROM PathCTE), '.full'),
                #{full, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                CONCAT((SELECT basePath FROM PathCTE), '.mild'),
                #{mild, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                CONCAT((SELECT basePath FROM PathCTE), '.moderate'),
                #{moderate, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                CONCAT((SELECT basePath FROM PathCTE), '.severe'),
                #{severe, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            -- 更新topScoreTotal
                CONCAT((SELECT elementPath FROM PathCTE), '.topScoreTotal'),
                FLOOR(JSON_EXTRACT(#{full, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                                   '$[1]') +
                      JSON_EXTRACT(#{mild, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                                   '$[1]') +
                      JSON_EXTRACT(
                              #{moderate, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                              '$[1]') +
                      JSON_EXTRACT(#{severe, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                                   '$[1]')
                )
                        )
        WHERE id = #{templateId}
    </update>

    <update id="gradeSaveOverall">
        UPDATE t_assessment_template
        SET grade = JSON_SET(
                COALESCE(grade, JSON_OBJECT()),
                '$.full',
                #{full,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                '$.mild',
                #{mild,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                '$.moderate',
                #{moderate,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                '$.severe',
                #{severe,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                '$.topScoreTotal',
                FLOOR((JSON_EXTRACT(
                               #{full,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                               '$[1]') +
                       JSON_EXTRACT(
                               #{mild,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                               '$[1]') +
                       JSON_EXTRACT(
                               #{moderate,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                               '$[1]') +
                       JSON_EXTRACT(
                               #{severe,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
                               '$[1]')
                    ))
                    )
        WHERE id = #{templateId}
    </update>

    <update id="questionSortAdjust">
        WITH Path1 AS (SELECT REPLACE(
                                      JSON_UNQUOTE(
                                              JSON_SEARCH(indicator, 'one', #{sourceId}, NULL, '$[*].subject[*].id')
                                      ),
                                      '.id',
                                      '.sort'
                              ) AS basePath
                       FROM t_assessment_template
                       WHERE id = #{templateId}),
             Path2 AS (SELECT REPLACE(
                                      JSON_UNQUOTE(
                                              JSON_SEARCH(indicator, 'one', #{targetId}, NULL, '$[*].subject[*].id')
                                      ),
                                      '.id',
                                      '.sort'
                              ) AS basePath
                       FROM t_assessment_template
                       WHERE id = #{templateId})
        UPDATE t_assessment_template
        SET indicator = JSON_SET(
                COALESCE(indicator, JSON_ARRAY()),
                (SELECT basePath FROM Path1),
                (SELECT JSON_EXTRACT(indicator, (SELECT basePath FROM Path2))),
                (SELECT basePath FROM Path2),
                (SELECT JSON_EXTRACT(indicator, (SELECT basePath FROM Path1)))
                        )
        WHERE id = #{templateId}
    </update>


</mapper>
