<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.frontPage.mapper.FrontPageMapper">

    <select id="getUserNumber" resultType="cn.hutool.json.JSONObject">
        select
        SUM(case when status = '0' then 1 else 1 end) as totalNum,
        SUM(case when status = '0' then 1 else 0 end) as checknum
        FROM t_elderly_people_info where del_flag = '0'
    </select>
    <select id="getLeaveNumber" resultType="cn.hutool.json.JSONObject">
        select count(id) as LeaveNum from t_live_leave_records where state = '0' and del_flag = '0'
    </select>

    <select id="getRoomNum" resultType="cn.hutool.json.JSONObject">
        SELECT count(id) as roomNum from t_storied_building_info where type = '3' and del_flag = '0'
    </select>

    <select id="getBedNum" resultType="cn.hutool.json.JSONObject">
        SELECT
        SUM( CASE WHEN bed_state = '1' THEN 1 ELSE 1 END ) AS totalBedNum,
        SUM( CASE WHEN bed_state = '1' THEN 1 ELSE 0 END ) AS freeBedNum
        FROM
        t_bed_base_info
        WHERE
        del_flag = '0'
    </select>


</mapper>
