<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.alarm.mapper.DeviceAlarmMapper">

    <resultMap type="DeviceAlarm" id="DeviceAlarmResult">
        <result property="id" column="id"/>
        <result property="elderId" column="elder_id"/>
        <result property="elderName" column="elder_name"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="alarmType" column="alarm_type"/>
    </resultMap>

    <sql id="selectDeviceAlarmVo">
        SELECT id, elder_id, elder_name, alarm_time, alarm_type
        FROM t_device_alarm
    </sql>

    <select id="selectDeviceAlarmList" parameterType="DeviceAlarm" resultMap="DeviceAlarmResult">
        <include refid="selectDeviceAlarmVo"/>
        <where>
            <if test="elderId != null  and elderId != ''">
                and elder_id = #{elderId}
            </if>
            <if test="elderName != null  and elderName != ''">
                and elder_name like concat('%', #{elderName}, '%')
            </if>
            <if test="alarmTime != null ">
                and alarm_time = #{alarmTime}
            </if>
            <if test="alarmType != null  and alarmType != ''">
                and alarm_type = #{alarmType}
            </if>
            <if test="params.beginAlarmTime != null and params.endAlarmTime!=null">and alarm_time BETWEEN
                date_format(#{params.beginAlarmTime},'%y-%m-%d') and date_format(#{params.endAlarmTime},'%y-%m-%d')
            </if>
        </where>
    </select>

    <select id="selectDeviceAlarmById" parameterType="Long"
            resultMap="DeviceAlarmResult">
        <include refid="selectDeviceAlarmVo"/>
        where id = #{id}
    </select>

    <insert id="insertDeviceAlarm" parameterType="DeviceAlarm" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_device_alarm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null and elderId != ''">elder_id,
            </if>
            <if test="elderName != null and elderName != ''">elder_name,
            </if>
            <if test="alarmTime != null">alarm_time,
            </if>
            <if test="alarmType != null and alarmType != ''">alarm_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null and elderId != ''">#{elderId},
            </if>
            <if test="elderName != null and elderName != ''">#{elderName},
            </if>
            <if test="alarmTime != null">#{alarmTime},
            </if>
            <if test="alarmType != null and alarmType != ''">#{alarmType},
            </if>
        </trim>
    </insert>

    <update id="updateDeviceAlarm" parameterType="DeviceAlarm">
        update t_device_alarm
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null and elderId != ''">elder_id =
                #{elderId},
            </if>
            <if test="elderName != null and elderName != ''">elder_name =
                #{elderName},
            </if>
            <if test="alarmTime != null">alarm_time =
                #{alarmTime},
            </if>
            <if test="alarmType != null and alarmType != ''">alarm_type =
                #{alarmType},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceAlarmById" parameterType="Long">
        DELETE
        FROM t_device_alarm
        WHERE id = #{id}
    </delete>

    <delete id="deleteDeviceAlarmByIds" parameterType="String">
        delete from t_device_alarm where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
