<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.medicalInspection.mapper.MedicalInspectionRecordMapper">

    <resultMap type="MedicalInspectionRecord" id="MedicalInspectionRecordResult">
        <result property="id" column="id"/>
        <result property="inspectionId" column="inspection_id"/>
        <result property="inspectorId" column="inspector_id"/>
        <result property="inspectorName" column="inspector_name"/>
        <result property="livingEnvironment" column="living_environment"/>
        <result property="physicalCondition" column="physical_condition"/>
        <result property="otherConditions" column="other_conditions"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="createdTime" column="created_time"/>
    </resultMap>

    <sql id="selectMedicalInspectionRecordVo">
        SELECT id,
               inspection_id,
               inspector_id,
               inspector_name,
               living_environment,
               physical_condition,
               other_conditions,
               image_urls,
               created_time
        FROM t_medical_inspection_record
    </sql>

    <select id="selectMedicalInspectionRecordList" parameterType="MedicalInspectionRecord"
            resultMap="MedicalInspectionRecordResult">
        <include refid="selectMedicalInspectionRecordVo"/>
        <where>
            <if test="inspectionId != null ">
                and inspection_id = #{inspectionId}
            </if>
            <if test="inspectorId != null ">
                and inspector_id = #{inspectorId}
            </if>
            <if test="inspectorName != null  and inspectorName != ''">
                and inspector_name like concat('%', #{inspectorName}, '%')
            </if>
            <if test="livingEnvironment != null  and livingEnvironment != ''">
                and living_environment = #{livingEnvironment}
            </if>
            <if test="physicalCondition != null  and physicalCondition != ''">
                and physical_condition = #{physicalCondition}
            </if>
            <if test="otherConditions != null  and otherConditions != ''">
                and other_conditions = #{otherConditions}
            </if>
            <if test="imageUrls != null  and imageUrls != ''">
                and image_urls = #{imageUrls}
            </if>
            <if test="createdTime != null ">
                and created_time = #{createdTime}
            </if>
        </where>
    </select>

    <select id="selectMedicalInspectionRecordById" parameterType="Long"
            resultMap="MedicalInspectionRecordResult">
        <include refid="selectMedicalInspectionRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertMedicalInspectionRecord" parameterType="MedicalInspectionRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_medical_inspection_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inspectionId != null">inspection_id,
            </if>
            <if test="inspectorId != null">inspector_id,
            </if>
            <if test="inspectorName != null and inspectorName != ''">inspector_name,
            </if>
            <if test="livingEnvironment != null">living_environment,
            </if>
            <if test="physicalCondition != null">physical_condition,
            </if>
            <if test="otherConditions != null">other_conditions,
            </if>
            <if test="imageUrls != null">image_urls,
            </if>
            <if test="createdTime != null">created_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inspectionId != null">#{inspectionId},
            </if>
            <if test="inspectorId != null">#{inspectorId},
            </if>
            <if test="inspectorName != null and inspectorName != ''">#{inspectorName},
            </if>
            <if test="livingEnvironment != null">#{livingEnvironment},
            </if>
            <if test="physicalCondition != null">#{physicalCondition},
            </if>
            <if test="otherConditions != null">#{otherConditions},
            </if>
            <if test="imageUrls != null">#{imageUrls},
            </if>
            <if test="createdTime != null">#{createdTime},
            </if>
        </trim>
    </insert>

    <update id="updateMedicalInspectionRecord" parameterType="MedicalInspectionRecord">
        update t_medical_inspection_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="inspectionId != null">inspection_id =
                #{inspectionId},
            </if>
            <if test="inspectorId != null">inspector_id =
                #{inspectorId},
            </if>
            <if test="inspectorName != null and inspectorName != ''">inspector_name =
                #{inspectorName},
            </if>
            <if test="livingEnvironment != null">living_environment =
                #{livingEnvironment},
            </if>
            <if test="physicalCondition != null">physical_condition =
                #{physicalCondition},
            </if>
            <if test="otherConditions != null">other_conditions =
                #{otherConditions},
            </if>
            <if test="imageUrls != null">image_urls =
                #{imageUrls},
            </if>
            <if test="createdTime != null">created_time =
                #{createdTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMedicalInspectionRecordById" parameterType="Long">
        DELETE
        FROM t_medical_inspection_record
        WHERE id = #{id}
    </delete>

    <delete id="deleteMedicalInspectionRecordByIds" parameterType="String">
        delete from t_medical_inspection_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
