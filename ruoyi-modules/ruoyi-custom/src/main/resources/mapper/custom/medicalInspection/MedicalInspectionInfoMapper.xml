<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.medicalInspection.mapper.MedicalInspectionInfoMapper">

    <resultMap type="MedicalInspectionInfo" id="MedicalInspectionInfoResult">
        <result property="id" column="id"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="inspectionFrequency" column="inspection_frequency"/>
        <result property="inspectionContent" column="inspection_content"/>
        <result property="specialAttention" column="special_attention"/>
        <result property="assignedPersonnelIds" column="assigned_personnel_ids"/>
        <result property="assignedPersonnelNames" column="assigned_personnel_names"/>

    </resultMap>

    <sql id="selectMedicalInspectionInfoVo">
        SELECT id,
               elderly_id,
               elderly_name,
               inspection_frequency,
               inspection_content,
               special_attention,
               assigned_personnel_ids,
               assigned_personnel_names,

               CONCAT(bud3.`name`,'-',bud2.`name`,'-',bud1.`name`,'-',bedinfo.bed_name) as bedName
        FROM t_medical_inspection_info a
                 LEFT JOIN (SELECT *
                            FROM (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
                                  FROM t_live_base_info) t
                            WHERE rn = 1) b ON a.elderly_id = b.user_id
                 LEFT JOIN t_live_bed_records bed on bed.live_id=b.id
                 LEFT JOIN t_bed_base_info bedinfo on bed.bed_id=bedinfo.id
                 LEFT JOIN t_storied_building_info bud1 on bed.room_id= bud1.id
                 LEFT JOIN t_storied_building_info bud2 on bud1.parent_id= bud2.id
                 LEFT JOIN t_storied_building_info bud3 on bud2.parent_id= bud3.id
    </sql>

    <select id="selectMedicalInspectionInfoList" parameterType="MedicalInspectionInfo"
            resultMap="MedicalInspectionInfoResult">
        <include refid="selectMedicalInspectionInfoVo"/>
        <where>
            <if test="elderlyId != null  and elderlyId != ''">
                and elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null  and elderlyName != ''">
                and elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="inspectionFrequency != null  and inspectionFrequency != ''">
                and inspection_frequency = #{inspectionFrequency}
            </if>
            <if test="inspectionContent != null  and inspectionContent != ''">
                and inspection_content = #{inspectionContent}
            </if>
            <if test="specialAttention != null  and specialAttention != ''">
                and special_attention = #{specialAttention}
            </if>
            <if test="assignedPersonnelIds != null  and assignedPersonnelIds != ''">
                and assigned_personnel_ids = #{assignedPersonnelIds}
            </if>
            <if test="params.userId != null">
                and FIND_IN_SET(#{params.userId}, assigned_personnel_ids) > 0
            </if>
        </where>
    </select>

    <select id="selectMedicalInspectionInfoById" parameterType="Long"
            resultMap="MedicalInspectionInfoResult">
        <include refid="selectMedicalInspectionInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertMedicalInspectionInfo" parameterType="MedicalInspectionInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_medical_inspection_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">elderly_id,
            </if>
            <if test="elderlyName != null and elderlyName != ''">elderly_name,
            </if>
            <if test="inspectionFrequency != null and inspectionFrequency != ''">inspection_frequency,
            </if>
            <if test="inspectionContent != null and inspectionContent != ''">inspection_content,
            </if>
            <if test="specialAttention != null">special_attention,
            </if>
            <if test="assignedPersonnelIds != null and assignedPersonnelIds != ''">assigned_personnel_ids,
            </if>
            <if test="assignedPersonnelNames != null and assignedPersonnelNames != ''">assigned_personnel_names,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">#{elderlyId},
            </if>
            <if test="elderlyName != null and elderlyName != ''">#{elderlyName},
            </if>
            <if test="inspectionFrequency != null and inspectionFrequency != ''">#{inspectionFrequency},
            </if>
            <if test="inspectionContent != null and inspectionContent != ''">#{inspectionContent},
            </if>
            <if test="specialAttention != null">#{specialAttention},
            </if>
            <if test="assignedPersonnelIds != null and assignedPersonnelIds != ''">#{assignedPersonnelIds},
            </if>
            <if test="assignedPersonnelNames != null and assignedPersonnelNames != ''">#{assignedPersonnelNames},
            </if>
        </trim>
    </insert>

    <update id="updateMedicalInspectionInfo" parameterType="MedicalInspectionInfo">
        update t_medical_inspection_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">elderly_id =
                #{elderlyId},
            </if>
            <if test="elderlyName != null and elderlyName != ''">elderly_name =
                #{elderlyName},
            </if>
            <if test="inspectionFrequency != null and inspectionFrequency != ''">inspection_frequency =
                #{inspectionFrequency},
            </if>
            <if test="inspectionContent != null and inspectionContent != ''">inspection_content =
                #{inspectionContent},
            </if>
            <if test="specialAttention != null">special_attention =
                #{specialAttention},
            </if>
            <if test="assignedPersonnelIds != null and assignedPersonnelIds != ''">assigned_personnel_ids =
                #{assignedPersonnelIds},
            </if>
            <if test="assignedPersonnelNames != null and assignedPersonnelNames != ''">assigned_personnel_names =
                #{assignedPersonnelNames},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMedicalInspectionInfoById" parameterType="Long">
        DELETE
        FROM t_medical_inspection_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteMedicalInspectionInfoByIds" parameterType="String">
        delete from t_medical_inspection_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
