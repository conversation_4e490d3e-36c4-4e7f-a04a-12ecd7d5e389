<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.CapabilityAssessmentInfoMapper">

    <resultMap type="CapabilityAssessmentInfo" id="CapabilityAssessmentInfoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="assessReasonValue" column="assess_reason_value"/>
        <result property="assessReasonLabel" column="assess_reason_label"/>
        <result property="industryStandard" column="industry_standard"/>
        <result property="selfCareAbilityAssessmentValue" column="self_care_ability_assessment_value"/>
        <result property="selfCareAbilityAssessmentLabel" column="self_care_ability_assessment_label"/>
        <result property="athleticAbilityAssessmentValue" column="athletic_ability_assessment_value"/>
        <result property="athleticAbilityAssessmentLabel" column="athletic_ability_assessment_label"/>
        <result property="mentalStateAssessmentValue" column="mental_state_assessment_value"/>
        <result property="mentalStateAssessmentLabel" column="mental_state_assessment_label"/>
        <result property="perceptionSocialEngagementAssessmentValue"
                column="perception_social_engagement_assessment_value"/>
        <result property="perceptionSocialEngagementAssessmentLabel"
                column="perception_social_engagement_assessment_label"/>
        <result property="preliminaryAssessmentLevel" column="preliminary_assessment_level"/>
        <result property="elderlyAbilityFinalLevel" column="elderly_ability_final_level"/>
        <result property="assessorId" column="assessor_id"/>
        <result property="evaluationOpinion" column="evaluation_opinion"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="evaluationTime" column="evaluation_time"/>
        <result property="selfCareAbilityAssessmentScore" column="self_care_ability_assessment_score"/>
        <result property="athleticAbilityAssessmentScore" column="athletic_ability_assessment_score"/>
        <result property="mentalStateAssessmentScore" column="mental_state_assessment_score"/>
        <result property="perceptionSocialEngagementAssessmentScore"
                column="perception_social_engagement_assessment_score"/>
        <result property="userName" column="userName"/>
        <result property="sex" column="sex"/>
        <result property="dateBirth" column="dateBirth"/>
        <result property="assessorName" column="assessorName"/>
        <result property="img" column="img"/>
        <result property="reasonSelfCareAbilityAssessmentValue" column="reasonSelfCareAbilityAssessmentValue"/>
        <result property="reasonAthleticAbilityAssessmentValue" column="reasonAthleticAbilityAssessmentValue"/>
        <result property="reasonMentalStateAssessmentValue" column="reasonMentalStateAssessmentValue"/>
        <result property="reasonPerceptionSocialEngagementAssessmentValue"
                column="reasonPerceptionSocialEngagementAssessmentValue"/>
        <result property="reasonReview" column="reasonReview"/>
        <result property="flag" column="flag"/>
    </resultMap>

    <sql id="selectCapabilityAssessmentInfoVo">
        SELECT
        a.id,
        a.user_id,
        b.name as userName,
        b.sex,
        b.date_birth as dateBirth,
        b.img as img,
        IF(d.mental_state_assessment_value is null,0,1) as flag,
        d.self_care_ability_assessment_value as reasonSelfCareAbilityAssessmentValue,
        d.athletic_ability_assessment_value as reasonAthleticAbilityAssessmentValue,
        d.mental_state_assessment_value as reasonMentalStateAssessmentValue,
        d.perception_social_engagement_assessment_value as reasonPerceptionSocialEngagementAssessmentValue,
        d.reason_review as reasonReview,
        a.assess_reason_value,
        a.assess_reason_label,
        a.industry_standard,
        a.evaluation_time,
        a.self_care_ability_assessment_score,
        a.athletic_ability_assessment_score,
        a.mental_state_assessment_score,
        a.perception_social_engagement_assessment_score,
        a.self_care_ability_assessment_value,
        a.self_care_ability_assessment_label,
        a.athletic_ability_assessment_value,
        a.athletic_ability_assessment_label,
        a.mental_state_assessment_value,
        a.mental_state_assessment_label,
        a.perception_social_engagement_assessment_value,
        a.perception_social_engagement_assessment_label,
        a.preliminary_assessment_level,
        a.elderly_ability_final_level,
        a.assessor_id,
        a.evaluation_opinion,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_capability_assessment_info as a left join t_elderly_people_info as b on a.user_id = b.id left join t_review_records_info as d on d.base_id = a.id

    </sql>

    <select id="selectCapabilityAssessmentInfoList" parameterType="CapabilityAssessmentInfo"
            resultMap="CapabilityAssessmentInfoResult">
        <include refid="selectCapabilityAssessmentInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="userId != null  and userId != ''">and a.user_id = #{userId}</if>
            <if test="assessReasonValue != null  and assessReasonValue != ''">and a.assess_reason_value =
                #{assessReasonValue}
            </if>
            <if test="assessReasonLabel != null  and assessReasonLabel != ''">and a.assess_reason_label =
                #{assessReasonLabel}
            </if>
            <if test="industryStandard != null  and industryStandard != ''">and a.industry_standard =
                #{industryStandard}
            </if>
            <if test="selfCareAbilityAssessmentValue != null ">and a.self_care_ability_assessment_value =
                #{selfCareAbilityAssessmentValue}
            </if>
            <if test="selfCareAbilityAssessmentLabel != null  and selfCareAbilityAssessmentLabel != ''">and
                a.self_care_ability_assessment_label = #{selfCareAbilityAssessmentLabel}
            </if>
            <if test="athleticAbilityAssessmentValue != null ">and a.athletic_ability_assessment_value =
                #{athleticAbilityAssessmentValue}
            </if>
            <if test="athleticAbilityAssessmentLabel != null  and athleticAbilityAssessmentLabel != ''">and
                a.athletic_ability_assessment_label = #{athleticAbilityAssessmentLabel}
            </if>
            <if test="mentalStateAssessmentValue != null ">and a.mental_state_assessment_value =
                #{mentalStateAssessmentValue}
            </if>
            <if test="mentalStateAssessmentLabel != null  and mentalStateAssessmentLabel != ''">and
                a.mental_state_assessment_label = #{mentalStateAssessmentLabel}
            </if>
            <if test="perceptionSocialEngagementAssessmentValue != null ">and
                a.perception_social_engagement_assessment_value = #{perceptionSocialEngagementAssessmentValue}
            </if>
            <if test="perceptionSocialEngagementAssessmentLabel != null  and perceptionSocialEngagementAssessmentLabel != ''">
                and a.perception_social_engagement_assessment_label = #{perceptionSocialEngagementAssessmentLabel}
            </if>
            <if test="preliminaryAssessmentLevel != null  and preliminaryAssessmentLevel != ''">and
                a.preliminary_assessment_level = #{preliminaryAssessmentLevel}
            </if>
            <if test="elderlyAbilityFinalLevel != null ">and a.elderly_ability_final_level =
                #{elderlyAbilityFinalLevel}
            </if>
            <if test="assessorId != null  and assessorId != ''">and a.assessor_id = #{assessorId}</if>
            <if test="evaluationOpinion != null  and evaluationOpinion != ''">and a.evaluation_opinion =
                #{evaluationOpinion}
            </if>
        </where>
        order by a.evaluation_time desc
    </select>

    <select id="selectCapabilityAssessmentInfoById" parameterType="Long" resultMap="CapabilityAssessmentInfoResult">
        <include refid="selectCapabilityAssessmentInfoVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertCapabilityAssessmentInfo" parameterType="CapabilityAssessmentInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_capability_assessment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="assessReasonValue != null">assess_reason_value,</if>
            <if test="assessReasonLabel != null">assess_reason_label,</if>
            <if test="industryStandard != null">industry_standard,</if>
            <if test="selfCareAbilityAssessmentScore != null">self_care_ability_assessment_score,</if>
            <if test="selfCareAbilityAssessmentValue != null">self_care_ability_assessment_value,</if>
            <if test="selfCareAbilityAssessmentLabel != null">self_care_ability_assessment_label,</if>
            <if test="athleticAbilityAssessmentScore != null">athletic_ability_assessment_score,</if>
            <if test="athleticAbilityAssessmentValue != null">athletic_ability_assessment_value,</if>
            <if test="athleticAbilityAssessmentLabel != null">athletic_ability_assessment_label,</if>
            <if test="mentalStateAssessmentScore != null">mental_state_assessment_score,</if>
            <if test="mentalStateAssessmentValue != null">mental_state_assessment_value,</if>
            <if test="mentalStateAssessmentLabel != null">mental_state_assessment_label,</if>
            <if test="perceptionSocialEngagementAssessmentScore != null">
                perception_social_engagement_assessment_score,
            </if>
            <if test="perceptionSocialEngagementAssessmentValue != null">
                perception_social_engagement_assessment_value,
            </if>
            <if test="perceptionSocialEngagementAssessmentLabel != null">
                perception_social_engagement_assessment_label,
            </if>
            <if test="preliminaryAssessmentLevel != null">preliminary_assessment_level,</if>
            <if test="elderlyAbilityFinalLevel != null">elderly_ability_final_level,</if>
            <if test="evaluationTime != null">evaluation_time,</if>
            <if test="assessorId != null">assessor_id,</if>
            <if test="evaluationOpinion != null">evaluation_opinion,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="assessReasonValue != null">#{assessReasonValue},</if>
            <if test="assessReasonLabel != null">#{assessReasonLabel},</if>
            <if test="industryStandard != null">#{industryStandard},</if>
            <if test="selfCareAbilityAssessmentScore != null">#{selfCareAbilityAssessmentScore},</if>
            <if test="selfCareAbilityAssessmentValue != null">#{selfCareAbilityAssessmentValue},</if>
            <if test="selfCareAbilityAssessmentLabel != null">#{selfCareAbilityAssessmentLabel},</if>
            <if test="athleticAbilityAssessmentScore != null">#{athleticAbilityAssessmentScore},</if>
            <if test="athleticAbilityAssessmentValue != null">#{athleticAbilityAssessmentValue},</if>
            <if test="athleticAbilityAssessmentLabel != null">#{athleticAbilityAssessmentLabel},</if>
            <if test="mentalStateAssessmentScore != null">#{mentalStateAssessmentScore},</if>
            <if test="mentalStateAssessmentValue != null">#{mentalStateAssessmentValue},</if>
            <if test="mentalStateAssessmentLabel != null">#{mentalStateAssessmentLabel},</if>
            <if test="perceptionSocialEngagementAssessmentScore != null">#{perceptionSocialEngagementAssessmentScore},
            </if>
            <if test="perceptionSocialEngagementAssessmentValue != null">#{perceptionSocialEngagementAssessmentValue},
            </if>
            <if test="perceptionSocialEngagementAssessmentLabel != null">#{perceptionSocialEngagementAssessmentLabel},
            </if>
            <if test="preliminaryAssessmentLevel != null">#{preliminaryAssessmentLevel},</if>
            <if test="elderlyAbilityFinalLevel != null">#{elderlyAbilityFinalLevel},</if>
            <if test="evaluationTime != null">#{evaluationTime},</if>
            <if test="assessorId != null">#{assessorId},</if>
            <if test="evaluationOpinion != null">#{evaluationOpinion},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCapabilityAssessmentInfo" parameterType="CapabilityAssessmentInfo">
        update t_capability_assessment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="assessReasonValue != null">assess_reason_value = #{assessReasonValue},</if>
            <if test="assessReasonLabel != null">assess_reason_label = #{assessReasonLabel},</if>
            <if test="industryStandard != null">industry_standard = #{industryStandard},</if>
            <if test="selfCareAbilityAssessmentScore != null">self_care_ability_assessment_score =
                #{selfCareAbilityAssessmentScore},
            </if>
            <if test="selfCareAbilityAssessmentValue != null">self_care_ability_assessment_value =
                #{selfCareAbilityAssessmentValue},
            </if>
            <if test="selfCareAbilityAssessmentLabel != null">self_care_ability_assessment_label =
                #{selfCareAbilityAssessmentLabel},
            </if>
            <if test="athleticAbilityAssessmentScore != null">athletic_ability_assessment_score =
                #{athleticAbilityAssessmentScore},
            </if>
            <if test="athleticAbilityAssessmentValue != null">athletic_ability_assessment_value =
                #{athleticAbilityAssessmentValue},
            </if>
            <if test="athleticAbilityAssessmentLabel != null">athletic_ability_assessment_label =
                #{athleticAbilityAssessmentLabel},
            </if>
            <if test="mentalStateAssessmentScore != null">mental_state_assessment_score =
                #{mentalStateAssessmentScore},
            </if>
            <if test="mentalStateAssessmentValue != null">mental_state_assessment_value =
                #{mentalStateAssessmentValue},
            </if>
            <if test="mentalStateAssessmentLabel != null">mental_state_assessment_label =
                #{mentalStateAssessmentLabel},
            </if>
            <if test="perceptionSocialEngagementAssessmentScore != null">perception_social_engagement_assessment_score =
                #{perceptionSocialEngagementAssessmentScore},
            </if>
            <if test="perceptionSocialEngagementAssessmentValue != null">perception_social_engagement_assessment_value =
                #{perceptionSocialEngagementAssessmentValue},
            </if>
            <if test="perceptionSocialEngagementAssessmentLabel != null">perception_social_engagement_assessment_label =
                #{perceptionSocialEngagementAssessmentLabel},
            </if>
            <if test="preliminaryAssessmentLevel != null">preliminary_assessment_level =
                #{preliminaryAssessmentLevel},
            </if>
            <if test="elderlyAbilityFinalLevel != null">elderly_ability_final_level = #{elderlyAbilityFinalLevel},</if>
            <if test="evaluationTime != null">evaluation_time = #{evaluationTime},</if>
            <if test="assessorId != null">assessor_id = #{assessorId},</if>
            <if test="evaluationOpinion != null">evaluation_opinion = #{evaluationOpinion},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCapabilityAssessmentInfoById" parameterType="Long">
        delete from t_capability_assessment_info where id = #{id}
    </delete>

    <delete id="deleteCapabilityAssessmentInfoByIds" parameterType="String">
        update t_capability_assessment_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="updateReviewById" parameterType="OBJECT">
        update t_capability_assessment_info set elderly_ability_final_level = #{level} where id = #{id}
    </update>
</mapper>
