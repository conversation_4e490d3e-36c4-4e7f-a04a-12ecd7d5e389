<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.ElderlyPeopleHealthManagementMapper">

    <resultMap type="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleHealthManagement"
               id="ElderlyPeopleHealthManagementResult">
        <result property="id" column="id"/>
        <result property="chronicDiseases" column="chronic_diseases"/>
        <result property="incapacitationCondition" column="incapacitation_condition"/>
        <result property="bloodType" column="blood_type"/>
        <result property="disabilitySituation" column="disability_situation"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectElderlyPeopleHealthManagementVo">
        select id, chronic_diseases,incapacitation_condition, blood_type, disability_situation, create_time, create_by,
        update_time, update_by, del_flag, remark, user_id from t_elderly_people_health_management
    </sql>

    <select id="selectElderlyPeopleHealthManagementList"
            parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleHealthManagement"
            resultMap="ElderlyPeopleHealthManagementResult">
        <include refid="selectElderlyPeopleHealthManagementVo"/>
        <where>
            del_flag = '0'
            <if test="chronicDiseases != null  and chronicDiseases != ''">and chronic_diseases = #{chronicDiseases}</if>
            <if test="incapacitationCondition != null  and incapacitationCondition != ''">and incapacitation_condition =
                #{incapacitationCondition}
            </if>
            <if test="bloodType != null  and bloodType != ''">and blood_type = #{bloodType}</if>
            <if test="disabilitySituation != null  and disabilitySituation != ''">and disability_situation =
                #{disabilitySituation}
            </if>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectElderlyPeopleHealthManagementById" parameterType="String"
            resultMap="ElderlyPeopleHealthManagementResult">
        <include refid="selectElderlyPeopleHealthManagementVo"/>
        where id = #{id}
    </select>

    <insert id="insertElderlyPeopleHealthManagement"
            parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleHealthManagement">
        insert into t_elderly_people_health_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="chronicDiseases != null">chronic_diseases,</if>
            <if test="incapacitationCondition != null">incapacitation_condition,</if>
            <if test="bloodType != null">blood_type,</if>
            <if test="disabilitySituation != null">disability_situation,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="chronicDiseases != null">#{chronicDiseases},</if>
            <if test="incapacitationCondition != null">#{incapacitationCondition},</if>
            <if test="bloodType != null">#{bloodType},</if>
            <if test="disabilitySituation != null">#{disabilitySituation},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateElderlyPeopleHealthManagement"
            parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleHealthManagement">
        update t_elderly_people_health_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="chronicDiseases != null">chronic_diseases = #{chronicDiseases},</if>
            <if test="incapacitationCondition != null">incapacitation_condition = #{incapacitationCondition},</if>
            <if test="bloodType != null">blood_type = #{bloodType},</if>
            <if test="disabilitySituation != null">disability_situation = #{disabilitySituation},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyPeopleHealthManagementById" parameterType="String">
        delete from t_elderly_people_health_management where id = #{id}
    </delete>

    <delete id="deleteElderlyPeopleHealthManagementByIds" parameterType="String">
        update t_elderly_people_health_management set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
