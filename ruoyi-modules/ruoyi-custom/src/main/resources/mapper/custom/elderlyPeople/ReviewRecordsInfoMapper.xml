<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.ReviewRecordsInfoMapper">

    <resultMap type="ReviewRecordsInfo" id="ReviewRecordsInfoResult">
        <result property="id" column="id"/>
        <result property="baseId" column="base_id"/>
        <result property="selfCareAbilityAssessmentValue" column="self_care_ability_assessment_value"/>
        <result property="selfCareAbilityAssessmentLabel" column="self_care_ability_assessment_label"/>
        <result property="athleticAbilityAssessmentValue" column="athletic_ability_assessment_value"/>
        <result property="athleticAbilityAssessmentLabel" column="athletic_ability_assessment_label"/>
        <result property="mentalStateAssessmentValue" column="mental_state_assessment_value"/>
        <result property="mentalStateAssessmentLabel" column="mental_state_assessment_label"/>
        <result property="perceptionSocialEngagementAssessmentValue"
                column="perception_social_engagement_assessment_value"/>
        <result property="perceptionSocialEngagementAssessmentLabel"
                column="perception_social_engagement_assessment_label"/>
        <result property="preliminaryAssessmentLevel" column="preliminary_assessment_level"/>
        <result property="reasonReview" column="reason_review"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectReviewRecordsInfoVo">
        select id, base_id, self_care_ability_assessment_value, self_care_ability_assessment_label,
        athletic_ability_assessment_value, athletic_ability_assessment_label, mental_state_assessment_value,
        mental_state_assessment_label, perception_social_engagement_assessment_value,
        perception_social_engagement_assessment_label, preliminary_assessment_level, reason_review, create_time,
        create_by, update_time, update_by, del_flag, remark from t_review_records_info
    </sql>

    <select id="selectReviewRecordsInfoList" parameterType="ReviewRecordsInfo" resultMap="ReviewRecordsInfoResult">
        <include refid="selectReviewRecordsInfoVo"/>
        <where>
            del_flag = '0'
            <if test="baseId != null ">and base_id = #{baseId}</if>
            <if test="selfCareAbilityAssessmentValue != null ">and self_care_ability_assessment_value =
                #{selfCareAbilityAssessmentValue}
            </if>
            <if test="selfCareAbilityAssessmentLabel != null  and selfCareAbilityAssessmentLabel != ''">and
                self_care_ability_assessment_label = #{selfCareAbilityAssessmentLabel}
            </if>
            <if test="athleticAbilityAssessmentValue != null ">and athletic_ability_assessment_value =
                #{athleticAbilityAssessmentValue}
            </if>
            <if test="athleticAbilityAssessmentLabel != null  and athleticAbilityAssessmentLabel != ''">and
                athletic_ability_assessment_label = #{athleticAbilityAssessmentLabel}
            </if>
            <if test="mentalStateAssessmentValue != null ">and mental_state_assessment_value =
                #{mentalStateAssessmentValue}
            </if>
            <if test="mentalStateAssessmentLabel != null  and mentalStateAssessmentLabel != ''">and
                mental_state_assessment_label = #{mentalStateAssessmentLabel}
            </if>
            <if test="perceptionSocialEngagementAssessmentValue != null ">and
                perception_social_engagement_assessment_value = #{perceptionSocialEngagementAssessmentValue}
            </if>
            <if test="perceptionSocialEngagementAssessmentLabel != null  and perceptionSocialEngagementAssessmentLabel != ''">
                and perception_social_engagement_assessment_label = #{perceptionSocialEngagementAssessmentLabel}
            </if>
            <if test="preliminaryAssessmentLevel != null  and preliminaryAssessmentLevel != ''">and
                preliminary_assessment_level = #{preliminaryAssessmentLevel}
            </if>
            <if test="reasonReview != null  and reasonReview != ''">and reason_review = #{reasonReview}</if>
        </where>
    </select>

    <select id="selectReviewRecordsInfoById" parameterType="Long" resultMap="ReviewRecordsInfoResult">
        <include refid="selectReviewRecordsInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertReviewRecordsInfo" parameterType="ReviewRecordsInfo">
        insert into t_review_records_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="baseId != null">base_id,</if>
            <if test="selfCareAbilityAssessmentValue != null">self_care_ability_assessment_value,</if>
            <if test="selfCareAbilityAssessmentLabel != null">self_care_ability_assessment_label,</if>
            <if test="athleticAbilityAssessmentValue != null">athletic_ability_assessment_value,</if>
            <if test="athleticAbilityAssessmentLabel != null">athletic_ability_assessment_label,</if>
            <if test="mentalStateAssessmentValue != null">mental_state_assessment_value,</if>
            <if test="mentalStateAssessmentLabel != null">mental_state_assessment_label,</if>
            <if test="perceptionSocialEngagementAssessmentValue != null">
                perception_social_engagement_assessment_value,
            </if>
            <if test="perceptionSocialEngagementAssessmentLabel != null">
                perception_social_engagement_assessment_label,
            </if>
            <if test="preliminaryAssessmentLevel != null">preliminary_assessment_level,</if>
            <if test="reasonReview != null">reason_review,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="baseId != null">#{baseId},</if>
            <if test="selfCareAbilityAssessmentValue != null">#{selfCareAbilityAssessmentValue},</if>
            <if test="selfCareAbilityAssessmentLabel != null">#{selfCareAbilityAssessmentLabel},</if>
            <if test="athleticAbilityAssessmentValue != null">#{athleticAbilityAssessmentValue},</if>
            <if test="athleticAbilityAssessmentLabel != null">#{athleticAbilityAssessmentLabel},</if>
            <if test="mentalStateAssessmentValue != null">#{mentalStateAssessmentValue},</if>
            <if test="mentalStateAssessmentLabel != null">#{mentalStateAssessmentLabel},</if>
            <if test="perceptionSocialEngagementAssessmentValue != null">#{perceptionSocialEngagementAssessmentValue},
            </if>
            <if test="perceptionSocialEngagementAssessmentLabel != null">#{perceptionSocialEngagementAssessmentLabel},
            </if>
            <if test="preliminaryAssessmentLevel != null">#{preliminaryAssessmentLevel},</if>
            <if test="reasonReview != null">#{reasonReview},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateReviewRecordsInfo" parameterType="ReviewRecordsInfo">
        update t_review_records_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="baseId != null">base_id = #{baseId},</if>
            <if test="selfCareAbilityAssessmentValue != null">self_care_ability_assessment_value =
                #{selfCareAbilityAssessmentValue},
            </if>
            <if test="selfCareAbilityAssessmentLabel != null">self_care_ability_assessment_label =
                #{selfCareAbilityAssessmentLabel},
            </if>
            <if test="athleticAbilityAssessmentValue != null">athletic_ability_assessment_value =
                #{athleticAbilityAssessmentValue},
            </if>
            <if test="athleticAbilityAssessmentLabel != null">athletic_ability_assessment_label =
                #{athleticAbilityAssessmentLabel},
            </if>
            <if test="mentalStateAssessmentValue != null">mental_state_assessment_value =
                #{mentalStateAssessmentValue},
            </if>
            <if test="mentalStateAssessmentLabel != null">mental_state_assessment_label =
                #{mentalStateAssessmentLabel},
            </if>
            <if test="perceptionSocialEngagementAssessmentValue != null">perception_social_engagement_assessment_value =
                #{perceptionSocialEngagementAssessmentValue},
            </if>
            <if test="perceptionSocialEngagementAssessmentLabel != null">perception_social_engagement_assessment_label =
                #{perceptionSocialEngagementAssessmentLabel},
            </if>
            <if test="preliminaryAssessmentLevel != null">preliminary_assessment_level =
                #{preliminaryAssessmentLevel},
            </if>
            <if test="reasonReview != null">reason_review = #{reasonReview},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReviewRecordsInfoById" parameterType="Long">
        delete from t_review_records_info where id = #{id}
    </delete>

    <delete id="deleteReviewRecordsInfoByIds" parameterType="String">
        update t_review_records_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getBaseInfo" parameterType="Long" resultMap="ReviewRecordsInfoResult">
        <include refid="selectReviewRecordsInfoVo"/>
        where base_id = #{baseId}
    </select>
</mapper>
