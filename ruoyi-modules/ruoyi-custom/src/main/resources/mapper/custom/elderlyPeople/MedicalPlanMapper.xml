<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.MedicalPlanMapper">

    <resultMap type="MedicalPlan" id="MedicalPlanResult">
        <result property="id" column="id"/>
        <result property="elderId" column="elder_id"/>
        <result property="planType" column="plan_type"/>
        <result property="planTime" column="plan_time"/>
        <result property="planContent" column="plan_content"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>

        <result property="elderName" column="elder_name"/>
    </resultMap>

    <sql id="selectMedicalPlanVo">
        SELECT id,
               elder_id,
               plan_type,
               plan_time,
               plan_content,
               create_by_id,
               create_by,
               create_time
        FROM t_medical_plan
    </sql>

    <select id="selectMedicalPlanList" parameterType="MedicalPlan" resultMap="MedicalPlanResult">
        <include refid="selectMedicalPlanVo"/>
        <where>
            <if test="elderId != null  and elderId != ''">
                and elder_id = #{elderId}
            </if>
            <if test="planType != null  and planType != ''">
                and plan_type = #{planType}
            </if>
            <if test="planTime != null  and planTime != ''">
                and plan_time = #{planTime}
            </if>
            <if test="planContent != null  and planContent != ''">
                and plan_content = #{planContent}
            </if>
            <if test="createById != null ">
                and create_by_id = #{createById}
            </if>
            <if test="createBy != null  and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null ">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <select id="selectMedicalPlanById" parameterType="Long"
            resultMap="MedicalPlanResult">
        <include refid="selectMedicalPlanVo"/>
        where id = #{id}
    </select>

    <select id="selectMedicalPlanListPartition"
            resultType="com.ruoyi.custom.admin.elderlyPeople.domain.MedicalPlan">
        SELECT a.*,
        b.name AS elder_name
        FROM (SELECT id,
        elder_id,
        plan_type,
        plan_time,
        plan_content,
        create_by_id,
        create_by,
        create_time,
        ROW_NUMBER() OVER (PARTITION BY elder_id ORDER BY create_time DESC) AS rn
        FROM t_medical_plan) AS a
        LEFT JOIN t_elderly_people_info b ON a.elder_id = b.id
        <where>
            rn = 1
            <if test="elderName != null and elderName != ''">
                AND b.name LIKE concat('%', #{elderName}, '%')
            </if>
            <if test="elderId != null and elderId != ''">
                AND a.elder_id = #{elderId}
            </if>
            <if test="planType != null and planType != ''">
                AND a.plan_type = #{planType}
            </if>
        </where>
    </select>

    <insert id="insertMedicalPlan" parameterType="MedicalPlan" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_medical_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null and elderId != ''">elder_id,
            </if>
            <if test="planType != null and planType != ''">plan_type,
            </if>
            <if test="planTime != null and planTime != ''">plan_time,
            </if>
            <if test="planContent != null and planContent != ''">plan_content,
            </if>
            <if test="createById != null">create_by_id,
            </if>
            <if test="createBy != null and createBy != ''">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null and elderId != ''">#{elderId},
            </if>
            <if test="planType != null and planType != ''">#{planType},
            </if>
            <if test="planTime != null and planTime != ''">#{planTime},
            </if>
            <if test="planContent != null and planContent != ''">#{planContent},
            </if>
            <if test="createById != null">#{createById},
            </if>
            <if test="createBy != null and createBy != ''">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <update id="updateMedicalPlan" parameterType="MedicalPlan">
        update t_medical_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null and elderId != ''">elder_id =
                #{elderId},
            </if>
            <if test="planType != null and planType != ''">plan_type =
                #{planType},
            </if>
            <if test="planTime != null and planTime != ''">plan_time =
                #{planTime},
            </if>
            <if test="planContent != null and planContent != ''">plan_content =
                #{planContent},
            </if>
            <if test="createById != null">create_by_id =
                #{createById},
            </if>
            <if test="createBy != null and createBy != ''">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMedicalPlanById" parameterType="Long">
        DELETE
        FROM t_medical_plan
        WHERE id = #{id}
    </delete>

    <delete id="deleteMedicalPlanByIds" parameterType="String">
        delete from t_medical_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
