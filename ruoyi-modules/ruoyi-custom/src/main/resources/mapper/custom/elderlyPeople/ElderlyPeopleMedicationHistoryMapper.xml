<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.ElderlyPeopleMedicationHistoryMapper">

    <resultMap type="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleMedicationHistory"
               id="ElderlyPeopleMedicationHistoryResult">
        <result property="id" column="id"/>
        <result property="diseaseName" column="disease_name"/>
        <result property="medicineName" column="medicine_name"/>
        <result property="meteringUsage" column="metering_usage"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>

        <association property="elderlyPeopleInfo" javaType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo">
            <result column="user_id" property="id"/>
            <result column="userName" property="name"/>
            <result column="id_card_num" property="idCardNum"/>
        </association>
    </resultMap>

    <sql id="selectElderlyPeopleMedicationHistoryVo">
        SELECT t.id,
               t.disease_name,
               t.medicine_name,
               t.metering_usage,
               t.begin_time,
               t.end_time,
               t.status,
               t.create_time,
               t.create_by,
               t.update_time,
               t.update_by,
               t.del_flag,
               t.remark,
               t.user_id,

               t1.name AS userName,
               t1.id_card_num

        FROM t_elderly_people_medication_history t
                 LEFT JOIN t_elderly_people_info t1 ON t.user_id = t1.id
    </sql>

    <select id="selectElderlyPeopleMedicationHistoryList"
            parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleMedicationHistory"
            resultMap="ElderlyPeopleMedicationHistoryResult">
        <include refid="selectElderlyPeopleMedicationHistoryVo"/>
        <where>
            t.del_flag = '0'
            <if test="diseaseName != null  and diseaseName != ''">and t.disease_name like concat('%', #{diseaseName},
                '%')
            </if>
            <if test="medicineName != null  and medicineName != ''">and t.medicine_name like concat('%',
                #{medicineName},
                '%')
            </if>
            <if test="meteringUsage != null  and meteringUsage != ''">and t.metering_usage = #{meteringUsage}</if>
            <if test="status != null  and status != ''">and t.status = #{status}</if>
            <if test="userId != null  and userId != ''">and t.user_id = #{userId}</if>

            <if test="params.name != null and params.name != ''">and t1.name like concat('%', #{params.name}, '%')</if>
            <if test="params.idCardNum != null and params.idCardNum != ''">and t1.id_card_num like concat('%',
                #{params.idCardNum}, '%')
            </if>
            ORDER BY t.status , t.create_time DESC
        </where>
    </select>

    <select id="selectElderlyPeopleMedicationHistoryById" parameterType="String"
            resultMap="ElderlyPeopleMedicationHistoryResult">
        <include refid="selectElderlyPeopleMedicationHistoryVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertElderlyPeopleMedicationHistory"
            parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleMedicationHistory">
        insert into t_elderly_people_medication_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="diseaseName != null">disease_name,</if>
            <if test="medicineName != null">medicine_name,</if>
            <if test="meteringUsage != null">metering_usage,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="diseaseName != null">#{diseaseName},</if>
            <if test="medicineName != null">#{medicineName},</if>
            <if test="meteringUsage != null">#{meteringUsage},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateElderlyPeopleMedicationHistory"
            parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleMedicationHistory">
        update t_elderly_people_medication_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="diseaseName != null">disease_name = #{diseaseName},</if>
            <if test="medicineName != null">medicine_name = #{medicineName},</if>
            <if test="meteringUsage != null">metering_usage = #{meteringUsage},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyPeopleMedicationHistoryById" parameterType="String">
        DELETE
        FROM t_elderly_people_medication_history
        WHERE id = #{id}
    </delete>

    <delete id="deleteElderlyPeopleMedicationHistoryByIds" parameterType="String">
        update t_elderly_people_medication_history set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
