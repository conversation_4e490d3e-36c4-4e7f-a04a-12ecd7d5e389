<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.MedicalFeedbackMapper">

    <resultMap type="MedicalFeedback" id="MedicalFeedbackResult">
        <result property="id" column="id"/>
        <result property="planId" column="plan_id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="feedbackTime" column="feedback_time"/>
        <result property="feedbackContent" column="feedback_content"/>
        <result property="photoUrls" column="photo_urls"/>
    </resultMap>

    <sql id="selectMedicalFeedbackVo">
        SELECT id, plan_id, doctor_id, doctor_name, feedback_time, feedback_content, photo_urls
        FROM t_medical_feedback
    </sql>

    <select id="selectMedicalFeedbackList" parameterType="MedicalFeedback" resultMap="MedicalFeedbackResult">
        <include refid="selectMedicalFeedbackVo"/>
        <where>
            <if test="planId != null and planId != ''">
                and plan_id = #{planId}
            </if>
            <if test="doctorId != null ">
                and doctor_id = #{doctorId}
            </if>
            <if test="doctorName != null  and doctorName != ''">
                and doctor_name like concat('%', #{doctorName}, '%')
            </if>
            <if test="feedbackTime != null ">
                and feedback_time = #{feedbackTime}
            </if>
            <if test="feedbackContent != null  and feedbackContent != ''">
                and feedback_content = #{feedbackContent}
            </if>
            <if test="photoUrls != null  and photoUrls != ''">
                and photo_urls = #{photoUrls}
            </if>
        </where>
    </select>

    <select id="selectMedicalFeedbackById" parameterType="Long"
            resultMap="MedicalFeedbackResult">
        <include refid="selectMedicalFeedbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertMedicalFeedback" parameterType="MedicalFeedback" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_medical_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null and planId != ''">
                plan_id,
            </if>
            <if test="doctorId != null">doctor_id,
            </if>
            <if test="doctorName != null and doctorName != ''">doctor_name,
            </if>
            <if test="feedbackTime != null">feedback_time,
            </if>
            <if test="feedbackContent != null and feedbackContent != ''">feedback_content,
            </if>
            <if test="photoUrls != null">photo_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null and planId != ''">
                #{planId},
            </if>
            <if test="doctorId != null">#{doctorId},
            </if>
            <if test="doctorName != null and doctorName != ''">#{doctorName},
            </if>
            <if test="feedbackTime != null">#{feedbackTime},
            </if>
            <if test="feedbackContent != null and feedbackContent != ''">#{feedbackContent},
            </if>
            <if test="photoUrls != null">#{photoUrls},
            </if>
        </trim>
    </insert>

    <update id="updateMedicalFeedback" parameterType="MedicalFeedback">
        update t_medical_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null and planId != ''">plan_id =
                #{planId},
            </if>
            <if test="doctorId != null">doctor_id =
                #{doctorId},
            </if>
            <if test="doctorName != null and doctorName != ''">doctor_name =
                #{doctorName},
            </if>
            <if test="feedbackTime != null">feedback_time =
                #{feedbackTime},
            </if>
            <if test="feedbackContent != null and feedbackContent != ''">feedback_content =
                #{feedbackContent},
            </if>
            <if test="photoUrls != null">photo_urls =
                #{photoUrls},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMedicalFeedbackById" parameterType="Long">
        DELETE
        FROM t_medical_feedback
        WHERE id = #{id}
    </delete>

    <delete id="deleteMedicalFeedbackByIds" parameterType="String">
        delete from t_medical_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
