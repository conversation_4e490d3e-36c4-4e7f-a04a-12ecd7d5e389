<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.care.mapper.CareComboServiceItemsBaseInfoMapper">

    <resultMap type="CareComboServiceItemsBaseInfo" id="CareComboServiceItemsBaseInfoResult">
        <result property="id" column="id"/>
        <result property="comboId" column="combo_id"/>
        <result property="careProjectId" column="care_project_id"/>
        <result property="careProjectName" column="careProjectName"/>
        <result property="cycle" column="cycle"/>
        <result property="frequency" column="frequency"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectCareComboServiceItemsBaseInfoVo">
        SELECT
        a.id,
        a.combo_id,
        a.care_project_id,
        c.care_name as careProjectName,
        a.cycle,
        a.frequency,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_care_combo_service_items_base_info as a left join t_care_project_base_info as c on a.care_project_id = c.id
    </sql>

    <select id="selectCareComboServiceItemsBaseInfoList" parameterType="CareComboServiceItemsBaseInfo"
            resultMap="CareComboServiceItemsBaseInfoResult">
        <include refid="selectCareComboServiceItemsBaseInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="comboId != null ">and a.combo_id = #{comboId}</if>
            <if test="careProjectId != null ">and a.care_project_id = #{careProjectId}</if>
            <if test="cycle != null  and cycle != ''">and a.cycle = #{cycle}</if>
            <if test="frequency != null ">and a.frequency = #{frequency}</if>
            <if test="careProjectName != null and careProjectName != ''">and c.care_name = #{careProjectName}</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectCareComboServiceItemsBaseInfoById" parameterType="Long"
            resultMap="CareComboServiceItemsBaseInfoResult">
        <include refid="selectCareComboServiceItemsBaseInfoVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertCareComboServiceItemsBaseInfo" parameterType="CareComboServiceItemsBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_care_combo_service_items_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="comboId != null">combo_id,</if>
            <if test="careProjectId != null">care_project_id,</if>
            <if test="cycle != null">cycle,</if>
            <if test="frequency != null">frequency,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="comboId != null">#{comboId},</if>
            <if test="careProjectId != null">#{careProjectId},</if>
            <if test="cycle != null">#{cycle},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCareComboServiceItemsBaseInfo" parameterType="CareComboServiceItemsBaseInfo">
        update t_care_combo_service_items_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="comboId != null">combo_id = #{comboId},</if>
            <if test="careProjectId != null">care_project_id = #{careProjectId},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCareComboServiceItemsBaseInfoById" parameterType="Long">
        delete from t_care_combo_service_items_base_info where id = #{id}
    </delete>

    <delete id="deleteCareComboServiceItemsBaseInfoByIds" parameterType="String">
        update t_care_combo_service_items_base_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
