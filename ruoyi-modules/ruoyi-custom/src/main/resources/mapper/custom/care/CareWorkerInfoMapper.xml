<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.care.mapper.CareWorkerInfoMapper">

    <resultMap type="CareWorkerInfo" id="CareWorkerInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="password" column="password"/>
        <result property="sex" column="sex"/>
        <result property="position" column="position"/>
        <result property="address" column="address"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectCareWorkerInfoVo">
        select id, name, user_id, phone, password, sex, position, address, state, create_time, create_by, update_time,
        update_by, del_flag, remark from t_care_worker_info
    </sql>

    <select id="selectCareWorkerInfoList" parameterType="CareWorkerInfo" resultMap="CareWorkerInfoResult">
        <include refid="selectCareWorkerInfoVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="password != null  and password != ''">and password = #{password}</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="position != null  and position != ''">and position = #{position}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
        </where>
    </select>

    <select id="selectCareWorkerInfoById" parameterType="Long" resultMap="CareWorkerInfoResult">
        <include refid="selectCareWorkerInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCareWorkerInfo" parameterType="CareWorkerInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_care_worker_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="userId != null">user_id,</if>
            <if test="phone != null">phone,</if>
            <if test="password != null">password,</if>
            <if test="sex != null">sex,</if>
            <if test="position != null">position,</if>
            <if test="address != null">address,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="userId != null">#{userId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="password != null">#{password},</if>
            <if test="sex != null">#{sex},</if>
            <if test="position != null">#{position},</if>
            <if test="address != null">#{address},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCareWorkerInfo" parameterType="CareWorkerInfo">
        update t_care_worker_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="password != null">password = #{password},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="position != null">position = #{position},</if>
            <if test="address != null">address = #{address},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCareWorkerInfoById" parameterType="Long">
        delete from t_care_worker_info where id = #{id}
    </delete>

    <delete id="deleteCareWorkerInfoByIds" parameterType="String">
        update t_care_worker_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getCareWorkerList" resultType="cn.hutool.json.JSONObject" parameterType="String">
        select id as value,name as label from t_care_worker_info
        <where>
            del_flag = '0'
            <if test="name != null and name != '' ">
                and name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="getCareByUserId" parameterType="Long" resultMap="CareWorkerInfoResult">
        <include refid="selectCareWorkerInfoVo"/>
        where user_id = #{userId}
    </select>
</mapper>
