<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.care.mapper.CareRecordInfoMapper">

    <resultMap type="CareRecordInfo" id="CareRecordInfoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="serviceItemsId" column="service_items_id"/>
        <result property="careWorkerId" column="care_worker_id"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="careImg" column="care_img"/>
        <result property="careTaskId" column="care_task_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="userName" column="userName"/>
        <result property="careName" column="careName"/>
        <result property="careWorkerName" column="careWorkerName"/>
        <result property="remark" column="remark"/>
        <result property="liveId" column="live_id"/>
    </resultMap>

    <sql id="selectCareRecordInfoVo">
        SELECT
        a.id,
        u.name as userName,
        t.care_name as careName,
        c.name as careWorkerName,
        a.user_id,
        a.service_items_id,
        a.care_worker_id,
        a.begin_time,
        a.end_time,
        a.care_img,
        a.care_task_id,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.live_id,
        a.remark
        FROM
        t_care_record_info as a left join
        t_elderly_people_info as u on a.user_id = u.id left join
        t_care_project_base_info as t on a.service_items_id = t.id left join
        t_care_worker_info as c on a.care_worker_id = c.id
    </sql>

    <select id="selectCareRecordInfoList" parameterType="CareRecordInfo" resultMap="CareRecordInfoResult">
        <include refid="selectCareRecordInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="careName != null  and careName != ''">and t.care_name like concat ('%',#{careName},'%')</if>
            <if test="userName != null  and userName != ''">and u.name like concat ('%',#{userName},'%')</if>
            <if test="userId != null  and userId != ''">and a.user_id = #{userId}</if>
            <if test="serviceItemsId != null ">and a.service_items_id = #{serviceItemsId}</if>
            <if test="careWorkerId != null ">and a.care_worker_id = #{careWorkerId}</if>
            <if test="beginTime != null ">and a.begin_time = #{beginTime}</if>
            <if test="endTime != null ">and a.end_time = #{endTime}</if>
            <if test="careImg != null  and careImg != ''">and a.care_img = #{careImg}</if>
            <if test="careTaskId != null ">and a.care_task_id = #{careTaskId}</if>
        </where>
        order by a.end_time desc
    </select>

    <select id="selectCareRecordInfoById" parameterType="Long" resultMap="CareRecordInfoResult">
        <include refid="selectCareRecordInfoVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertCareRecordInfo" parameterType="CareRecordInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_care_record_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="serviceItemsId != null">service_items_id,</if>
            <if test="careWorkerId != null">care_worker_id,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="careImg != null">care_img,</if>
            <if test="careTaskId != null">care_task_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="liveId != null">live_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="serviceItemsId != null">#{serviceItemsId},</if>
            <if test="careWorkerId != null">#{careWorkerId},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="careImg != null">#{careImg},</if>
            <if test="careTaskId != null">#{careTaskId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="liveId != null">#{liveId},</if>
        </trim>
    </insert>

    <update id="updateCareRecordInfo" parameterType="CareRecordInfo">
        update t_care_record_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="serviceItemsId != null">service_items_id = #{serviceItemsId},</if>
            <if test="careWorkerId != null">care_worker_id = #{careWorkerId},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="careImg != null">care_img = #{careImg},</if>
            <if test="careTaskId != null">care_task_id = #{careTaskId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="liveId != null">live_id = #{liveId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCareRecordInfoById" parameterType="Long">
        delete from t_care_record_info where id = #{id}
    </delete>

    <delete id="deleteCareRecordInfoByIds" parameterType="String">
        update t_care_record_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="recent7DaysItemStatistics" resultType="cn.hutool.json.JSONObject">
        SELECT
            DATE_FORMAT(d.date, '%m-%d') AS date,
            p.care_name AS serviceName,
            COUNT(r.id) AS count
        FROM
            (
                -- 生成最近 7 天的日期
                SELECT CURDATE() - INTERVAL (a.a + (10 * b.a)) DAY AS date
                FROM
                    (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a,
                    (SELECT 0 AS a UNION ALL SELECT 1) AS b
            ) d
                CROSS JOIN
            t_care_project_base_info p -- 确保显示所有护理项目
                LEFT JOIN
            t_care_record_info r ON DATE(r.begin_time) = d.date AND r.service_items_id = p.id
        WHERE
            d.date BETWEEN DATE_SUB(CURDATE(), INTERVAL 6 DAY) AND CURDATE()
        GROUP BY
            d.date, p.care_name
        ORDER BY
            d.date ASC, p.care_name;

    </select>

    <select id="halfYearItemStatistics" resultType="cn.hutool.json.JSONObject">
        SELECT
            DATE_FORMAT(d.date, '%m月') AS month,
            p.care_name AS serviceName,
            COUNT(r.id) AS count
        FROM
            (
                -- 生成最近 6 个月的每个月的第一天
                SELECT LAST_DAY(CURDATE() - INTERVAL (a.a + (10 * b.a)) MONTH) + INTERVAL 1 DAY AS date
                FROM
                    (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a,
                    (SELECT 0 AS a UNION ALL SELECT 1) AS b
            ) d
                CROSS JOIN
            t_care_project_base_info p -- 确保显示所有护理项目
                LEFT JOIN
            t_care_record_info r ON DATE_FORMAT(r.begin_time, '%Y-%m') = DATE_FORMAT(d.date, '%Y-%m')
                AND r.service_items_id = p.id
        WHERE
            d.date BETWEEN DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 5 MONTH), '%Y-%m-01') AND LAST_DAY(CURDATE())
        GROUP BY
            DATE_FORMAT(d.date, '%Y-%m'), p.care_name
        ORDER BY
            DATE_FORMAT(d.date, '%Y-%m') ASC, p.care_name;

    </select>

    <select id="recent7DaysNurseItemStatistics" resultType="cn.hutool.json.JSONObject">
        SELECT
            w.name AS workerName,
            COUNT(r.id) AS count
        FROM
            t_care_worker_info w
                LEFT JOIN
            t_care_record_info r ON r.care_worker_id = w.id
                AND DATE(r.begin_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL 6 DAY) AND CURDATE()
        GROUP BY
            w.id
        ORDER BY
            count DESC;
    </select>

    <select id="halfYearNurseItemStatistics" resultType="cn.hutool.json.JSONObject">
        SELECT
            w.name AS workerName,
            COUNT(r.id) AS count
        FROM
            t_care_worker_info w
                LEFT JOIN
            t_care_record_info r ON r.care_worker_id = w.id
                AND DATE_FORMAT(r.begin_time, '%Y-%m') BETWEEN DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 5 MONTH), '%Y-%m') AND DATE_FORMAT(LAST_DAY(CURDATE()), '%Y-%m')
        GROUP BY
            w.id
        ORDER BY
            count DESC;
    </select>

    <select id="recent7DaysRoomItemStatistics" resultType="cn.hutool.json.JSONObject">
        WITH main AS (SELECT t3.name,
                             COUNT(DISTINCT t.id) AS count
                      FROM t_care_record_info t
                               LEFT JOIN t_live_bed_records t1 ON t.live_id = t1.live_id
                               LEFT JOIN t_room_type_index_info t2 ON t1.room_id = t2.room_id
                               LEFT JOIN t_room_type_base_info t3 ON t2.type_id = t3.id
                      WHERE DATE(t.begin_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL 6 DAY) AND CURDATE()
                      GROUP BY t3.id)

        SELECT t.name, COALESCE(main.count, 0) AS count
        FROM t_room_type_base_info t
                 LEFT JOIN main ON t.name = main.name
        ORDER BY count DESC;
    </select>

    <select id="halfYearRoomItemStatistics" resultType="cn.hutool.json.JSONObject">
        WITH main AS (SELECT t3.name,
                             COUNT(DISTINCT t.id) AS count
                      FROM t_care_record_info t
                               LEFT JOIN t_live_bed_records t1 ON t.live_id = t1.live_id
                               LEFT JOIN t_room_type_index_info t2 ON t1.room_id = t2.room_id
                               LEFT JOIN t_room_type_base_info t3 ON t2.type_id = t3.id
                      WHERE DATE_FORMAT(t.begin_time, '%Y-%m') BETWEEN DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 5 MONTH), '%Y-%m') AND DATE_FORMAT(LAST_DAY(CURDATE()), '%Y-%m')
                      GROUP BY t3.id)

        SELECT t.name, COALESCE(main.count, 0) AS count
        FROM t_room_type_base_info t
                 LEFT JOIN main ON t.name = main.name
        ORDER BY count DESC;
    </select>

    <select id="ownerRecord" resultType="com.ruoyi.custom.admin.care.domain.CareRecordInfo">

    </select>

</mapper>
