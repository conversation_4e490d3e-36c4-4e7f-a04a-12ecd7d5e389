<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.care.mapper.ComboBaseInfoMapper">

    <resultMap type="ComboBaseInfo" id="ComboBaseInfoResult">
        <result property="id" column="id"/>
        <result property="careLevel" column="care_level"/>
        <result property="comboName" column="combo_name"/>
        <result property="introduce" column="introduce"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>

        <result property="version" column="version"/>
        <result property="monthAmount" column="month_amount"/>
    </resultMap>

    <sql id="selectComboBaseInfoVo">
        select t.id,
               t.care_level,
               t.combo_name,
               t.introduce,
               t.state,
               t.create_time,
               t.create_by,
               t.update_time,
               t.update_by,
               t.del_flag,
               t.remark,

               f.version,
               f.month_amount
        from t_combo_base_info t
                 left join t_fee_combo_info f on t.id = f.combo_id and f.state = '0'
    </sql>

    <select id="selectComboBaseInfoList" parameterType="ComboBaseInfo" resultMap="ComboBaseInfoResult">
        <include refid="selectComboBaseInfoVo"/>
        <where>
            t.del_flag = '0'
            <if test="careLevel != null  and careLevel != ''">and t.care_level = #{careLevel}</if>
            <if test="comboName != null  and comboName != ''">and t.combo_name like concat('%', #{comboName}, '%')</if>
            <if test="introduce != null  and introduce != ''">and t.introduce = #{introduce}</if>
            <if test="state != null  and state != ''">and t.state = #{state}</if>
            <if test="flag != null  and flag != ''">and f.version is not null</if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectComboBaseInfoById" parameterType="Long" resultMap="ComboBaseInfoResult">
        <include refid="selectComboBaseInfoVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertComboBaseInfo" parameterType="ComboBaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_combo_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="careLevel != null">care_level,</if>
            <if test="comboName != null">combo_name,</if>
            <if test="introduce != null">introduce,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="careLevel != null">#{careLevel},</if>
            <if test="comboName != null">#{comboName},</if>
            <if test="introduce != null">#{introduce},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateComboBaseInfo" parameterType="ComboBaseInfo">
        update t_combo_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="careLevel != null">care_level = #{careLevel},</if>
            <if test="comboName != null">combo_name = #{comboName},</if>
            <if test="introduce != null">introduce = #{introduce},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteComboBaseInfoById" parameterType="Long">
        delete from t_combo_base_info where id = #{id}
    </delete>

    <delete id="deleteComboBaseInfoByIds" parameterType="String">
        update t_combo_base_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
