<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.care.mapper.CareTaskRecordsInfoMapper">

    <resultMap type="CareTaskRecordsInfo" id="CareTaskRecordsInfoResult">
        <result property="id" column="id"/>
        <result property="liveId" column="live_id"/>
        <result property="userId" column="user_id"/>
        <result property="careServiceId" column="care_service_id"/>
        <result property="cycle" column="cycle"/>
        <result property="frequency" column="frequency"/>
        <result property="actualNumber" column="actual_number"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="bedId" column="bed_id"/>

        <result property="careWorkerId" column="care_worker_id"/>
        <result property="userName" column="userName"/>
        <result property="bedName" column="bedName"/>
        <result property="careName" column="careName"/>
        <result property="status" column="status"/>

    </resultMap>

    <sql id="selectCareTaskRecordsInfoVo">
        SELECT t.id,
               t.live_id,
               t.user_id,
               t.care_service_id,
               t.cycle,
               t.frequency,
               t.actual_number,
               t.bed_id,
               t.create_time,
               t.create_by,
               t.update_time,
               t.update_by,
               t.del_flag,
               t.remark,

               b.care_worker_id,

               p.name                                                                         AS userName,
               CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', bedinfo.bed_name) AS bedName,
               c.care_name                                                                    AS careName,
               CASE
                   WHEN DATE(t.last_task_execution_time) = CURDATE() THEN '已执行'
                   ELSE '未执行' END                                                          AS status

        FROM t_care_task_records_info t
                 LEFT JOIN t_bed_care_info b ON t.bed_id = b.bed_id
                 LEFT JOIN t_care_project_base_info c ON t.care_service_id = c.id
                 LEFT JOIN t_elderly_people_info p ON t.user_id = p.id
                 LEFT JOIN t_live_bed_records bed
                           ON bed.live_id = t.live_id AND (bed.live_state = 0 OR bed.live_state = 2)
                 LEFT JOIN t_bed_base_info bedinfo ON bed.bed_id = bedinfo.id
                 LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
                 LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
                 LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
    </sql>

    <select id="selectCareTaskRecordsInfoList" parameterType="CareTaskRecordsInfo"
            resultMap="CareTaskRecordsInfoResult">
        <include refid="selectCareTaskRecordsInfoVo"/>
        <where>
            t.del_flag = '0'
            <if test="liveId != null  and liveId != ''">and t.live_id = #{liveId}</if>
            <if test="userId != null  and userId != ''">and t.user_id = #{userId}</if>
            <if test="careServiceId != null ">and t.care_service_id = #{careServiceId}</if>
            <if test="cycle != null  and cycle != ''">and t.cycle = #{cycle}</if>
            <if test="frequency != null ">and t.frequency = #{frequency}</if>
            <if test="actualNumber != null ">and t.actual_number = #{actualNumber}</if>

            <if test="userName != null  and userName != ''">
                and p.name like concat('%', #{userName}, '%')
            </if>
        </where>
    </select>

    <select id="getCareTaskRecordsList" parameterType="CareTaskRecordsInfo" resultMap="CareTaskRecordsInfoResult">
        <include refid="selectCareTaskRecordsInfoVo"/>
        <where>
            t.del_flag = '0' and date_Format(t.create_time,'%Y-%d-%m') != date_Format( now(),'%Y-%d-%m')
            <if test="liveId != null  and liveId != ''">and t.live_id = #{liveId}</if>
            <if test="userId != null  and userId != ''">and t.user_id = #{userId}</if>
            <if test="careServiceId != null ">and t.care_service_id = #{careServiceId}</if>
            <if test="cycle != null  and cycle != ''">and t.cycle = #{cycle}</if>
            <if test="frequency != null ">and t.frequency = #{frequency}</if>
            <if test="actualNumber != null ">and t.actual_number = #{actualNumber}</if>
        </where>
    </select>

    <select id="selectCareTaskRecordsInfoById" parameterType="Long" resultMap="CareTaskRecordsInfoResult">
        <include refid="selectCareTaskRecordsInfoVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertCareTaskRecordsInfo" parameterType="CareTaskRecordsInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_care_task_records_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="liveId != null">live_id,</if>
            <if test="bedId != null">bed_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="careServiceId != null">care_service_id,</if>
            <if test="cycle != null">cycle,</if>
            <if test="frequency != null">frequency,</if>
            <if test="actualNumber != null">actual_number,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="liveId != null">#{liveId},</if>
            <if test="bedId != null">#{bedId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="careServiceId != null">#{careServiceId},</if>
            <if test="cycle != null">#{cycle},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="actualNumber != null">#{actualNumber},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCareTaskRecordsInfo" parameterType="CareTaskRecordsInfo">
        update t_care_task_records_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="bedId != null">bed_id = #{bedId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="careServiceId != null">care_service_id = #{careServiceId},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="actualNumber != null">actual_number = #{actualNumber},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAllTaskExecutionTime">
        UPDATE t_care_task_records_info
        SET last_task_execution_time = NOW()
    </update>

    <delete id="deleteCareTaskRecordsInfoById" parameterType="Long">
        DELETE
        FROM t_care_task_records_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteCareTaskRecordsInfoByIds" parameterType="String">
        update t_care_task_records_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
