<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.service.mapper.UserValueAddedServiceMapper">

    <resultMap type="UserValueAddedService" id="UserValueAddedServiceResult">
        <result property="id" column="id"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="serviceTypeId" column="service_type_id"/>
        <result property="serviceProjectId" column="service_project_id"/>
        <result property="consumable" column="consumable"/>
        <result property="chargingMethod" column="charging_method"/>
        <result property="serviceStartDate" column="service_start_date"/>
        <result property="serviceEndDate" column="service_end_date"/>
        <result property="serviceFrequency" column="service_frequency"/>
        <result property="serviceTimes" column="service_times"/>
        <result property="fee" column="fee"/>

        <result property="frequencyWithTimes" column="frequency_with_times"/>
        <result property="serviceTerm" column="service_term"/>
        <result property="serviceTypeName" column="service_type_name"/>
        <result property="serviceProjectName" column="service_project_name"/>
        <result property="elderlyName" column="elderly_name"/>
    </resultMap>

    <sql id="selectUserValueAddedServiceVo">
        SELECT t.id,
               t.elderly_id,
               t.service_type_id,
               t.service_project_id,
               t.consumable,
               t.charging_method,
               t.service_start_date,
               t.service_end_date,
               t.service_frequency,
               t.service_times,
               t.fee,

               -- 拼接服务周期：yyyy/MM/dd-yyyy/MM/dd
               CONCAT(
                       DATE_FORMAT(t.service_start_date, '%Y/%m/%d'),
                       '-',
                       DATE_FORMAT(t.service_end_date, '%Y/%m/%d')
               )                  AS service_term,

               -- 拼接服务频次与次数：每天/5次、每月/6次
               CASE t.service_frequency
                   WHEN '1' THEN CONCAT('每天/', t.service_times, '次')
                   WHEN '2' THEN CONCAT('每月/', t.service_times, '次')
                   ELSE '未知频率'
                   END            AS frequency_with_times,

               t2.type_name       AS service_type_name,
               t3.care_name AS service_project_name,
               t4.name   AS elderly_name
        FROM t_user_value_added_service t
                 LEFT JOIN t_service_type t2 ON t.service_type_id = t2.id
                 LEFT JOIN t_care_project_base_info t3 ON t.service_project_id = t3.id
                 LEFT JOIN t_elderly_people_info t4 ON t.elderly_id = t4.id
    </sql>

    <select id="selectUserValueAddedServiceList" parameterType="UserValueAddedService"
            resultMap="UserValueAddedServiceResult">
        <include refid="selectUserValueAddedServiceVo"/>
        <where>
            <if test="elderlyId != null ">
                and t.elderly_id = #{elderlyId}
            </if>
            <if test="serviceTypeId != null ">
                and t.service_type_id = #{serviceTypeId}
            </if>
            <if test="serviceProjectId != null ">
                and t.service_project_id = #{serviceProjectId}
            </if>
            <if test="consumable != null  and consumable != ''">
                and t.consumable = #{consumable}
            </if>
            <if test="chargingMethod != null  and chargingMethod != ''">
                and t.charging_method = #{chargingMethod}
            </if>
            <if test="serviceFrequency != null  and serviceFrequency != ''">
                and t.service_frequency = #{serviceFrequency}
            </if>
            <if test="fee != null ">
                and t.fee = #{fee}
            </if>
        </where>
    </select>

    <select id="selectUserValueAddedServiceById" parameterType="Long"
            resultMap="UserValueAddedServiceResult">
        <include refid="selectUserValueAddedServiceVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertUserValueAddedService" parameterType="UserValueAddedService" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_user_value_added_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null">elderly_id,
            </if>
            <if test="serviceTypeId != null">service_type_id,
            </if>
            <if test="serviceProjectId != null">service_project_id,
            </if>
            <if test="consumable != null">consumable,
            </if>
            <if test="chargingMethod != null">charging_method,
            </if>
            <if test="serviceStartDate != null">service_start_date,
            </if>
            <if test="serviceEndDate != null">service_end_date,
            </if>
            <if test="serviceFrequency != null">service_frequency,
            </if>
            <if test="serviceTimes != null">service_times,
            </if>
            <if test="fee != null">fee,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null">#{elderlyId},
            </if>
            <if test="serviceTypeId != null">#{serviceTypeId},
            </if>
            <if test="serviceProjectId != null">#{serviceProjectId},
            </if>
            <if test="consumable != null">#{consumable},
            </if>
            <if test="chargingMethod != null">#{chargingMethod},
            </if>
            <if test="serviceStartDate != null">#{serviceStartDate},
            </if>
            <if test="serviceEndDate != null">#{serviceEndDate},
            </if>
            <if test="serviceFrequency != null">#{serviceFrequency},
            </if>
            <if test="serviceTimes != null">#{serviceTimes},
            </if>
            <if test="fee != null">#{fee},
            </if>
        </trim>
    </insert>

    <update id="updateUserValueAddedService" parameterType="UserValueAddedService">
        update t_user_value_added_service
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderlyId != null">elderly_id =
                #{elderlyId},
            </if>
            <if test="serviceTypeId != null">service_type_id =
                #{serviceTypeId},
            </if>
            <if test="serviceProjectId != null">service_project_id =
                #{serviceProjectId},
            </if>
            <if test="consumable != null">consumable =
                #{consumable},
            </if>
            <if test="chargingMethod != null">charging_method =
                #{chargingMethod},
            </if>
            <if test="serviceStartDate != null">service_start_date =
                #{serviceStartDate},
            </if>
            <if test="serviceEndDate != null">service_end_date =
                #{serviceEndDate},
            </if>
            <if test="serviceFrequency != null">service_frequency =
                #{serviceFrequency},
            </if>
            <if test="serviceTimes != null">service_times =
                #{serviceTimes},
            </if>
            <if test="fee != null">fee =
                #{fee},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserValueAddedServiceById" parameterType="Long">
        DELETE
        FROM t_user_value_added_service
        WHERE id = #{id}
    </delete>

    <delete id="deleteUserValueAddedServiceByIds" parameterType="String">
        delete from t_user_value_added_service where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertUserValueAddedServiceBill">
        insert into t_user_value_added_service_bill
        (user_value_added_service_id, cycle, fee, is_confirm)
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userValueAddedServiceId}, #{item.cycle}, #{item.fee}, #{item.is_confirm})
        </foreach>
    </insert>

    <insert id="batchInsertUserValueAddedServiceBill">
        INSERT INTO t_user_value_added_service_bill
        (
        user_value_added_service_id,
        cycle,
        fee,
        is_confirm
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userValueAddedServiceId},
            #{item.cycle},
            #{item.fee},
            #{item.isConfirm}
            )
        </foreach>
    </insert>

    <select id="selectUserValueAddedServiceBill"
            resultType="com.ruoyi.custom.admin.service.domain.UserValueAddedServiceBill">
        SELECT
        t1.id,
        t.id AS userValueAddedServiceId,
        t3.care_name AS serviceProjectName, # 服务项目名称
        t1.cycle AS cycle, # 周期

        CASE t.service_frequency
        WHEN '1' THEN CONCAT('每天/', t.service_times, '次')
        WHEN '2' THEN CONCAT('每月/', t.service_times, '次')
        ELSE '未知频率'
        END AS frequencyWithTimes, # 拼接服务频次与次数：每天/5次、每月/6次

        t.charging_method AS chargingMethod, # 计费方式：一次性、月
        t1.fee, # 账单费用
        t.fee AS totalFee # 总费用

        FROM t_user_value_added_service t
        LEFT JOIN t_user_value_added_service_bill t1 ON t.id = t1.user_value_added_service_id
        LEFT JOIN t_service_type t2 ON t.service_type_id = t2.id
        LEFT JOIN t_care_project_base_info t3 ON t.service_project_id = t3.id
        <where>
            <if test="elderlyId != null and elderlyId != ''">
                AND t.elderly_id = #{elderlyId}
            </if>
            <if test="isConfirm != null and isConfirm != ''">
                AND t1.is_confirm = #{isConfirm}
            </if>
        </where>
        ORDER BY t.service_project_id, t1.cycle
    </select>

    <update id="batchUpdateUserValueAddedServiceBill">
        UPDATE t_user_value_added_service_bill
        SET is_confirm = #{list[0].isConfirm} <!-- 假设所有记录都使用相同的 isConfirm 值 -->
        WHERE id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

</mapper>
