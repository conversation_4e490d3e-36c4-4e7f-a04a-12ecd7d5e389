<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.service.mapper.ServiceProjectMapper">

    <resultMap type="ServiceProject" id="ServiceProjectResult">
        <result property="id" column="id"/>
        <result property="serviceType" column="service_type"/>
        <result property="serviceTypeStr" column="service_type_str"/>
        <result property="serviceProject" column="service_project"/>
        <result property="serviceProjectStr" column="service_project_str"/>
        <result property="consumable" column="consumable"/>
        <result property="price" column="price"/>
        <result property="projectImg" column="project_img"/>
        <result property="projectRemark" column="project_remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="sales" column="sales"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectServiceProjectVo">
        SELECT a.id,
               a.service_type,
               b.type_name AS service_type_str,
               a.service_project,
               c.care_name AS service_project_str,
               a.consumable,
               a.price,
               a.sales,
               a.project_img,
               a.project_remark,
               a.create_time,
               a.create_by,
               a.update_time,
               a.update_by,
               a.del_flag,
               a.remark,
               a.status
        FROM t_service_project AS a
                 LEFT JOIN t_service_type AS b ON a.service_type = b.id
                LEFT JOIN t_care_project_base_info AS c ON a.service_project = c.id
    </sql>

    <select id="selectServiceProjectList" parameterType="ServiceProject"
            resultMap="ServiceProjectResult">
        <include refid="selectServiceProjectVo"/>
        <where>
            a.del_flag = '0' and b.del_flag='0'
            <if test="serviceType != null  and serviceType != ''">and a.service_type = #{serviceType}</if>
            <if test="serviceProject != null  and serviceProject != ''">and a.service_project like concat('%',
                #{serviceProject}, '%')
            </if>
            <if test="price != null  and price != ''">and a.price = #{price}</if>
            <if test="projectImg != null  and projectImg != ''">and a.project_img = #{projectImg}</if>
            <if test="projectRemark != null  and projectRemark != ''">and a.project_remark = #{projectRemark}</if>
            <if test="status != null  and status != ''">and a.status = #{status}</if>
        </where>
        order by a.create_time DESC
    </select>

    <select id="selectServiceProjectById" parameterType="Long" resultMap="ServiceProjectResult">
        <include refid="selectServiceProjectVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertServiceProject" parameterType="ServiceProject"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_service_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceType != null">service_type,</if>
            <if test="serviceProject != null">service_project,</if>
            <if test="consumable != null">consumable,</if>
            <if test="price != null">price,</if>
            <if test="projectImg != null">project_img,</if>
            <if test="projectRemark != null">project_remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="sales != null">sales,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceType != null">#{serviceType},</if>
            <if test="serviceProject != null">#{serviceProject},</if>
            <if test="consumable != null">#{consumable},</if>
            <if test="price != null">#{price},</if>
            <if test="projectImg != null">#{projectImg},</if>
            <if test="projectRemark != null">#{projectRemark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="sales != null">#{sales},</if>
        </trim>
    </insert>

    <update id="updateServiceProject" parameterType="ServiceProject">
        update t_service_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="serviceProject != null">service_project = #{serviceProject},</if>
            <if test="consumable != null">consumable = #{consumable},</if>
            <if test="price != null">price = #{price},</if>
            <if test="projectImg != null">project_img = #{projectImg},</if>
            <if test="projectRemark != null">project_remark = #{projectRemark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sales != null">sales = #{sales},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceProjectById" parameterType="Long">
        DELETE
        FROM t_service_project
        WHERE id = #{id}
    </delete>

    <delete id="deleteServiceProjectByIds" parameterType="String">
        update t_service_project set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getProjectList" resultType="cn.hutool.json.JSONObject">
        SELECT id AS value, service_project AS label
        FROM t_service_project
        WHERE del_flag = '0'
    </select>

</mapper>
