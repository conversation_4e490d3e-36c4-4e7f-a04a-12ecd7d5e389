<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.service.mapper.ServiceTypeMapper">

    <resultMap type="ServiceType" id="ServiceTypeResult">
        <result property="id" column="id"/>
        <result property="typeName" column="type_name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectServiceTypeVo">
        SELECT id,
               type_name,
               create_time,
               create_by,
               update_time,
               update_by,
               del_flag,
               remark
        FROM t_service_type
    </sql>

    <select id="selectServiceTypeList" parameterType="ServiceType" resultMap="ServiceTypeResult">
        <include refid="selectServiceTypeVo"/>
        <where>
            del_flag = 0
            <if test="typeName != null  and typeName != ''">
                and type_name like concat('%', #{typeName}, '%')
            </if>
        </where>
    </select>

    <select id="selectServiceTypeById" parameterType="Long"
            resultMap="ServiceTypeResult">
        <include refid="selectServiceTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertServiceType" parameterType="ServiceType" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_service_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeName != null">type_name,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
            <if test="remark != null">remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeName != null">#{typeName},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
            <if test="remark != null">#{remark},
            </if>
        </trim>
    </insert>

    <update id="updateServiceType" parameterType="ServiceType">
        update t_service_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName != null">type_name =
                #{typeName},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
        </trim>
        where id = #{id}
    </update>

<!--    <delete id="deleteServiceTypeById" parameterType="Long">-->
<!--        DELETE-->
<!--        FROM t_service_type-->
<!--        WHERE id = #{id}-->
<!--    </delete>-->

    <update id="deleteServiceTypeByIds" parameterType="String">
        update t_service_type set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
