<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.mealManagement.mapper.MealOrderInfoMapper">

    <resultMap type="MealOrderInfo" id="MealOrderInfoResult">
        <result property="id" column="id"/>
        <result property="numberOfDiners" column="number_of_diners"/>
        <result property="mealType" column="meal_type"/>
        <result property="mealDate" column="meal_date"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

    <sql id="selectMealOrderInfoVo">
        SELECT id, number_of_diners, meal_type, meal_date, remarks
        FROM t_meal_order_info
    </sql>

    <select id="selectMealOrderInfoList" parameterType="MealOrderInfo" resultMap="MealOrderInfoResult">
        <include refid="selectMealOrderInfoVo"/>
        <where>
            <if test="numberOfDiners != null ">
                and number_of_diners = #{numberOfDiners}
            </if>
            <if test="mealType != null  and mealType != ''">
                and meal_type = #{mealType}
            </if>
            <if test="mealDate != null ">
                and meal_date = #{mealDate}
            </if>
            <if test="remarks != null  and remarks != ''">
                and remarks = #{remarks}
            </if>
            <if test="params.startMealDate != null and params.startMealDate != '' and params.endMealDate != null and params.endMealDate != ''">
                and meal_date between #{params.startMealDate} and #{params.endMealDate}
            </if>
        </where>
    </select>

    <select id="selectMealOrderInfoById" parameterType="Long"
            resultMap="MealOrderInfoResult">
        <include refid="selectMealOrderInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertMealOrderInfo" parameterType="MealOrderInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_meal_order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numberOfDiners != null">number_of_diners,
            </if>
            <if test="mealType != null and mealType != ''">meal_type,
            </if>
            <if test="mealDate != null">meal_date,
            </if>
            <if test="remarks != null">remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numberOfDiners != null">#{numberOfDiners},
            </if>
            <if test="mealType != null and mealType != ''">#{mealType},
            </if>
            <if test="mealDate != null">#{mealDate},
            </if>
            <if test="remarks != null">#{remarks},
            </if>
        </trim>
    </insert>

    <update id="updateMealOrderInfo" parameterType="MealOrderInfo">
        update t_meal_order_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="numberOfDiners != null">number_of_diners =
                #{numberOfDiners},
            </if>
            <if test="mealType != null and mealType != ''">meal_type =
                #{mealType},
            </if>
            <if test="mealDate != null">meal_date =
                #{mealDate},
            </if>
            <if test="remarks != null">remarks =
                #{remarks},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMealOrderInfoById" parameterType="Long">
        DELETE
        FROM t_meal_order_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteMealOrderInfoByIds" parameterType="String">
        delete from t_meal_order_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
