<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.mealManagement.mapper.RecipeMenuTypeBaseInfoMapper">

    <resultMap type="RecipeMenuTypeBaseInfo" id="RecipeMenuTypeBaseInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectRecipeMenuTypeBaseInfoVo">
        select id, name, create_time, create_by, update_time, update_by, del_flag, remark from
        t_recipe_menu_type_base_info
    </sql>

    <select id="selectRecipeMenuTypeBaseInfoList" parameterType="RecipeMenuTypeBaseInfo"
            resultMap="RecipeMenuTypeBaseInfoResult">
        <include refid="selectRecipeMenuTypeBaseInfoVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectRecipeMenuTypeBaseInfoById" parameterType="Long" resultMap="RecipeMenuTypeBaseInfoResult">
        <include refid="selectRecipeMenuTypeBaseInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertRecipeMenuTypeBaseInfo" parameterType="RecipeMenuTypeBaseInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_recipe_menu_type_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateRecipeMenuTypeBaseInfo" parameterType="RecipeMenuTypeBaseInfo">
        update t_recipe_menu_type_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRecipeMenuTypeBaseInfoById" parameterType="Long">
        delete from t_recipe_menu_type_base_info where id = #{id}
    </delete>

    <delete id="deleteRecipeMenuTypeBaseInfoByIds" parameterType="String">
        update t_recipe_menu_type_base_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getRecipeMenuTypeList" resultType="cn.hutool.json.JSONObject">
        select id as value ,name as label from t_recipe_menu_type_base_info where del_flag = '0'
    </select>
</mapper>
