<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.mealManagement.mapper.RecipeDetailsInfoMapper">

    <resultMap type="RecipeDetailsInfo" id="RecipeDetailsInfoResult">
        <result property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
        <result property="data" column="data"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectRecipeDetailsInfoVo">
        select id, type_id, begin_date, end_date, data, create_time, create_by, update_time, update_by, del_flag, remark
        from t_recipe_details_info
    </sql>

    <select id="selectRecipeDetailsInfoList" parameterType="RecipeDetailsInfo" resultMap="RecipeDetailsInfoResult">
        <include refid="selectRecipeDetailsInfoVo"/>
        <where>
            del_flag = '0'
            <if test="typeId != null ">and type_id = #{typeId}</if>
            <if test="beginDate != null ">and begin_date = #{beginDate}</if>
            <if test="endDate != null ">and end_date = #{endDate}</if>
            <if test="data != null  and data != ''">and data = #{data}</if>
        </where>
    </select>

    <select id="selectRecipeDetailsInfoById" parameterType="Long" resultMap="RecipeDetailsInfoResult">
        <include refid="selectRecipeDetailsInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertRecipeDetailsInfo" parameterType="RecipeDetailsInfo">
        insert into t_recipe_details_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="typeId != null">type_id,</if>
            <if test="beginDate != null">begin_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="data != null">data,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="beginDate != null">#{beginDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="data != null">#{data},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateRecipeDetailsInfo" parameterType="RecipeDetailsInfo">
        update t_recipe_details_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="beginDate != null">begin_date = #{beginDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="data != null">data = #{data},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRecipeDetailsInfoById" parameterType="Long">
        delete from t_recipe_details_info where id = #{id}
    </delete>

    <delete id="deleteRecipeDetailsInfoByIds" parameterType="String">
        update t_recipe_details_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
