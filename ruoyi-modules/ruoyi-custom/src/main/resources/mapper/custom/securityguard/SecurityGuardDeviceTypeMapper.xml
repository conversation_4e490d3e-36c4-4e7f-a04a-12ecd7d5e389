<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.securityguard.mapper.SecurityGuardDeviceTypeMapper">

    <resultMap type="SecurityGuardDeviceType" id="SecurityGuardDeviceTypeResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectSecurityGuardDeviceTypeVo">
        SELECT id, name, del_flag
        FROM t_security_guard_device_type
    </sql>

    <select id="selectSecurityGuardDeviceTypeList" parameterType="SecurityGuardDeviceType"
            resultMap="SecurityGuardDeviceTypeResult">
        <include refid="selectSecurityGuardDeviceTypeVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>

    <select id="selectSecurityGuardDeviceTypeById" parameterType="Long"
            resultMap="SecurityGuardDeviceTypeResult">
        <include refid="selectSecurityGuardDeviceTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertSecurityGuardDeviceType" parameterType="SecurityGuardDeviceType" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_security_guard_device_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
        </trim>
    </insert>

    <update id="updateSecurityGuardDeviceType" parameterType="SecurityGuardDeviceType">
        update t_security_guard_device_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityGuardDeviceTypeById" parameterType="Long">
        DELETE
        FROM t_security_guard_device_type
        WHERE id = #{id}
    </delete>

<!--    <delete id="deleteSecurityGuardDeviceTypeByIds" parameterType="String">-->
<!--        delete from t_security_guard_device_type where id in-->
<!--        <foreach item="id" collection="array" open="(" separator="," close=")">-->
<!--            #{id}-->
<!--        </foreach>-->
<!--    </delete>-->
    <update id="deleteSecurityGuardDeviceTypeByIds">
        update t_security_guard_device_type set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
