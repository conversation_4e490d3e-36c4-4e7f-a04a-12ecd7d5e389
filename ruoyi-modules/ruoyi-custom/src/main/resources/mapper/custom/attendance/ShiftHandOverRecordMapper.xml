<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.attendance.mapper.ShiftHandOverRecordMapper">

    <resultMap type="ShiftHandOverRecord" id="ShiftHandOverRecordResult">
        <result property="id" column="id"/>
        <result property="handoverId" column="handover_id"/>
        <result property="handoverName" column="handover_name"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="handoverTime" column="handover_time"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

    <sql id="selectShiftHandOverRecordVo">
        SELECT id, handover_id, handover_name, receiver_id, receiver_name, handover_time, remarks
        FROM t_shift_hand_over_record
    </sql>

    <select id="selectShiftHandOverRecordList" parameterType="ShiftHandOverRecord"
            resultMap="ShiftHandOverRecordResult">
        <include refid="selectShiftHandOverRecordVo"/>
        <where>
            <if test="handoverId != null ">
                and handover_id = #{handoverId}
            </if>
            <if test="handoverName != null  and handoverName != ''">
                and handover_name like concat('%', #{handoverName}, '%')
            </if>
            <if test="receiverId != null ">
                and receiver_id = #{receiverId}
            </if>
            <if test="receiverName != null  and receiverName != ''">
                and receiver_name like concat('%', #{receiverName}, '%')
            </if>
            <if test="handoverTime != null ">
                and handover_time = #{handoverTime}
            </if>
            <if test="remarks != null  and remarks != ''">
                and remarks = #{remarks}
            </if>
            <if test="params.handOrRecevId != null">
                and (handover_id = #{handoverId} or receiver_id = #{receiverId})
            </if>
        </where>
        ORDER BY id desc
    </select>

    <select id="selectShiftHandOverRecordById" parameterType="Long"
            resultMap="ShiftHandOverRecordResult">
        <include refid="selectShiftHandOverRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertShiftHandOverRecord" parameterType="ShiftHandOverRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_shift_hand_over_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="handoverId != null">handover_id,
            </if>
            <if test="handoverName != null and handoverName != ''">handover_name,
            </if>
            <if test="receiverId != null">receiver_id,
            </if>
            <if test="receiverName != null and receiverName != ''">receiver_name,
            </if>
            <if test="handoverTime != null">handover_time,
            </if>
            <if test="remarks != null">remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="handoverId != null">#{handoverId},
            </if>
            <if test="handoverName != null and handoverName != ''">#{handoverName},
            </if>
            <if test="receiverId != null">#{receiverId},
            </if>
            <if test="receiverName != null and receiverName != ''">#{receiverName},
            </if>
            <if test="handoverTime != null">#{handoverTime},
            </if>
            <if test="remarks != null">#{remarks},
            </if>
        </trim>
    </insert>

    <update id="updateShiftHandOverRecord" parameterType="ShiftHandOverRecord">
        update t_shift_hand_over_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="handoverId != null">handover_id =
                #{handoverId},
            </if>
            <if test="handoverName != null and handoverName != ''">handover_name =
                #{handoverName},
            </if>
            <if test="receiverId != null">receiver_id =
                #{receiverId},
            </if>
            <if test="receiverName != null and receiverName != ''">receiver_name =
                #{receiverName},
            </if>
            <if test="handoverTime != null">handover_time =
                #{handoverTime},
            </if>
            <if test="remarks != null">remarks =
                #{remarks},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShiftHandOverRecordById" parameterType="Long">
        DELETE
        FROM t_shift_hand_over_record
        WHERE id = #{id}
    </delete>

    <delete id="deleteShiftHandOverRecordByIds" parameterType="String">
        delete from t_shift_hand_over_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
