<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.attendance.mapper.CalendarManagementMapper">

    <resultMap type="CalendarManagement" id="CalendarManagementResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="legalHolidayAutoRest" column="legal_holiday_auto_rest"/>
        <result property="restDayMode" column="rest_day_mode"/>
        <result property="restDays" column="rest_days"/>
    </resultMap>

    <sql id="selectCalendarManagementVo">
        SELECT id, name, legal_holiday_auto_rest, rest_day_mode, rest_days
        FROM t_calendar_management
    </sql>

    <select id="selectCalendarManagementList" parameterType="CalendarManagement" resultMap="CalendarManagementResult">
        <include refid="selectCalendarManagementVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="legalHolidayAutoRest != null ">and legal_holiday_auto_rest = #{legalHolidayAutoRest}</if>
            <if test="restDayMode != null ">and rest_day_mode = #{restDayMode}</if>
            <if test="restDays != null  and restDays != ''">and rest_days = #{restDays}</if>
        </where>
        order by id desc
    </select>

    <select id="selectCalendarManagementById" parameterType="Integer" resultMap="CalendarManagementResult">
        <include refid="selectCalendarManagementVo"/>
        where id = #{id}
    </select>

    <insert id="insertCalendarManagement" parameterType="CalendarManagement" useGeneratedKeys="true" keyProperty="id">
        insert into t_calendar_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="legalHolidayAutoRest != null">legal_holiday_auto_rest,</if>
            <if test="restDayMode != null">rest_day_mode,</if>
            <if test="restDays != null">rest_days,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="legalHolidayAutoRest != null">#{legalHolidayAutoRest},</if>
            <if test="restDayMode != null">#{restDayMode},</if>
            <if test="restDays != null">#{restDays},</if>
        </trim>
    </insert>

    <update id="updateCalendarManagement" parameterType="CalendarManagement">
        update t_calendar_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="legalHolidayAutoRest != null">legal_holiday_auto_rest = #{legalHolidayAutoRest},</if>
            <if test="restDayMode != null">rest_day_mode = #{restDayMode},</if>
            <if test="restDays != null">rest_days = #{restDays},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCalendarManagementById" parameterType="Integer">
        DELETE
        FROM t_calendar_management
        WHERE id = #{id}
    </delete>

    <delete id="deleteCalendarManagementByIds" parameterType="String">
        delete from t_calendar_management where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
