<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.fee.mapper.FeeComboInfoMapper">

    <resultMap type="FeeComboInfo" id="FeeComboInfoResult">
        <result property="id" column="id"/>
        <result property="comboId" column="combo_id"/>
        <result property="version" column="version"/>
        <result property="monthDetails" column="month_details"/>
        <result property="monthAmount" column="month_amount"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectFeeComboInfoVo">
        select id,
               combo_id,
               version,
               month_details,
               month_amount,
               state,
               create_time,
               create_by,
               update_time,
               update_by,
               del_flag,
               remark
        from t_fee_combo_info
    </sql>

    <select id="selectFeeComboInfoList" parameterType="FeeComboInfo" resultMap="FeeComboInfoResult">
        <include refid="selectFeeComboInfoVo"/>
        <where>
            <if test="comboId != null  and comboId != ''">and combo_id = #{comboId}</if>
            <if test="version != null ">and version = #{version}</if>
            <if test="monthDetails != null  and monthDetails != ''">and month_details = #{monthDetails}</if>
            <if test="monthAmount != null ">and month_amount = #{monthAmount}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
        </where>
    </select>

    <select id="selectFeeComboInfoById" parameterType="String" resultMap="FeeComboInfoResult">
        <include refid="selectFeeComboInfoVo"/>
        where id = #{id}
    </select>


    <select id="selectComboList" parameterType="String" resultType="FeeComboDTO">
        SELECT
        c.id combo_id,
        c.care_level,
        c.combo_name,
        f.month_details,
        f.month_amount
        FROM
        t_combo_base_info c
        LEFT JOIN t_fee_combo_info f ON c.id = f.combo_id
        where c.del_flag = '0' and (f.state is null or f.state=0)
        <if test="comboName != null and comboName != '' ">
            and c.combo_name like concat('%',#{comboName},'%')
        </if>
    </select>

    <select id="selectComboById" parameterType="String" resultType="FeeComboDTO">
        SELECT c.id combo_id,
               c.care_level,
               c.combo_name,
               f.month_details,
               f.month_amount
        FROM t_combo_base_info c
                 LEFT JOIN t_fee_combo_info f ON c.id = f.combo_id
            AND f.state = 0
        where c.id = #{id}
    </select>

    <insert id="insertFeeComboInfo" parameterType="FeeComboInfo">
        insert into t_fee_combo_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="comboId != null">combo_id,</if>
            <if test="version != null">version,</if>
            <if test="monthDetails != null">month_details,</if>
            <if test="monthAmount != null">month_amount,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="comboId != null">#{comboId},</if>
            <if test="version != null">#{version},</if>
            <if test="monthDetails != null">#{monthDetails},</if>
            <if test="monthAmount != null">#{monthAmount},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateFeeComboInfo" parameterType="FeeComboInfo">
        update t_fee_combo_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="comboId != null">combo_id = #{comboId},</if>
            <if test="version != null">version = #{version},</if>
            <if test="monthDetails != null">month_details = #{monthDetails},</if>
            <if test="monthAmount != null">month_amount = #{monthAmount},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFeeComboInfoById" parameterType="String">
        delete
        from t_fee_combo_info
        where id = #{id}
    </delete>

    <delete id="deleteFeeComboInfoByIds" parameterType="String">
        delete from t_fee_combo_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
