package com.ruoyi.custom.admin.marketing.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.domain.PaymentSettlementRecordSummary;
import com.ruoyi.custom.admin.marketing.req.FeeStatisticsReq;
import com.ruoyi.custom.admin.marketing.resp.FeeStatisticsResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentManagementResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentRemindResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentSettlementManagementResp;
import com.ruoyi.custom.admin.marketing.service.IPaymentRecordService;
import io.swagger.annotations.*;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 缴费确认单Controller
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequestMapping("/paymentRecord")
@Api(value = "缴费确认单", tags = "缴费确认单")
public class PaymentRecordController extends BaseController {
    @Autowired
    private IPaymentRecordService paymentRecordService;

    /**
     * 查询缴费确认单列表
     */
    // @RequiresPermissions("custom:record:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询缴费确认单列表")
    public TableDataInfo list(PaymentRecord paymentRecord) {
        startPage();
        List<PaymentRecord> list = paymentRecordService.selectPaymentRecordList(paymentRecord);
        return getDataTable(list);
    }

    /**
     * 获取缴费确认单详细信息
     */
    // @RequiresPermissions("custom:record:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取缴费确认单详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(paymentRecordService.selectPaymentRecordById(id));
    }

    /**
     * 获取缴费金额明细列表
     */
    @GetMapping("/paymentDetail")
    @ApiOperation(value = "获取缴费金额明细列表")
    @ApiImplicitParam(name = "recordId", value = "缴费记录ID", required = true, paramType = "query")
    public AjaxResult getPaymentDetail(String recordId) {
        return AjaxResult.success(paymentRecordService.selectPaymentDetailByRecordId(recordId));
    }

    /**
     * 根据合同id，生成账单确认信息
     */
    @GetMapping("/payment/info")
    @ApiOperation(value = "根据合同id，生成账单缴费信息")
    public AjaxResult generatePaymentInfo(String contractNumber) {
        return AjaxResult.success(paymentRecordService.generatePaymentInfo(contractNumber));
    }

    /**
     * 暂存缴费单
     */
    @PostMapping("/draft")
    @ApiOperation(value = "暂存缴费单", notes = "支持通过tempGenerationId基于镜像数据创建暂存单")
    public AjaxResult draftPayment(@RequestBody PaymentRecord paymentRecord) {
        paymentRecord.setFeeType("1"); // 缴费单
        return AjaxResult.success("操作成功", paymentRecordService.draftPayment(paymentRecord));
    }

    /**
     * 确认缴费单
     */
    @PutMapping("/confirm/{id}")
    @ApiOperation(value = "确认缴费单")
    public AjaxResult confirmPayment(
            @PathVariable("id") String id,
            @RequestBody ConfirmPaymentRequest request) {
        return toAjax(paymentRecordService.confirmPayment(id, request.getBillNumber(), request.getPaidDetails(), request.getRemark()));
    }

    /**
     * 修改暂存缴费单
     */
    @PutMapping("/draft/{id}")
    @ApiOperation(value = "修改暂存缴费单")
    public AjaxResult updateDraftPayment(@PathVariable("id") String id, @RequestBody PaymentRecord paymentRecord) {
        paymentRecord.setId(id);
        return AjaxResult.success("操作成功",paymentRecordService.updateDraftPayment(paymentRecord));
    }

    /**
     * 删除暂存缴费单
     */
    @DeleteMapping("/draft/{id}")
    @ApiOperation(value = "删除暂存缴费单")
    public AjaxResult deleteDraftPayment(@PathVariable("id") String id) {
        return toAjax(paymentRecordService.deleteDraftPayment(id));
    }

    /**
     * 缴费单保存（兼容旧接口，直接确认）
     */
    @PostMapping("/payment")
    @ApiOperation(value = "缴费单保存")
    public AjaxResult paymentConfirm(@RequestBody PaymentRecord paymentRecord) {
        paymentRecord.setFeeType("1"); // 缴费单
        return toAjax(paymentRecordService.payment(paymentRecord));
    }

    /**
     * 根据合同id，生成账单结算信息
     */
    @GetMapping("/settlement/info")
    @ApiOperation(value = "根据合同id，生成账单结算信息")
    public AjaxResult generatePaymentSettlementInfo(String contractNumber) {
        return AjaxResult.success(paymentRecordService.generatePaymentSettlementInfo(contractNumber));
    }

    /**
     * 暂存结算单
     */
    @PostMapping("/settlement/draft")
    @ApiOperation(value = "暂存结算单", notes = "支持通过tempGenerationId基于镜像数据创建暂存结算单")
    public AjaxResult draftSettlement(@RequestBody PaymentRecord paymentRecord) {
        paymentRecord.setFeeType("2"); // 结算单
        return AjaxResult.success("操作成功", paymentRecordService.draftSettlement(paymentRecord));
    }

    /**
     * 确认结算单
     */
    @PutMapping("/settlement/confirm/{id}")
    @ApiOperation(value = "确认结算单")
    public AjaxResult confirmSettlement(@PathVariable("id") String id) {
        return toAjax(paymentRecordService.confirmSettlement(id));
    }

    /**
     * 修改暂存结算单
     */
    @PutMapping("/settlement/draft/{id}")
    @ApiOperation(value = "修改暂存结算单")
    public AjaxResult updateDraftSettlement(@PathVariable("id") String id, @RequestBody PaymentRecord paymentRecord) {
        paymentRecord.setId(id);
        return AjaxResult.success("操作成功", paymentRecordService.updateDraftSettlement(paymentRecord));
    }

    /**
     * 删除暂存结算单
     */
    @DeleteMapping("/settlement/draft/{id}")
    @ApiOperation(value = "删除暂存结算单")
    public AjaxResult deleteDraftSettlement(@PathVariable("id") String id) {
        return toAjax(paymentRecordService.deleteDraftSettlement(id));
    }

    /**
     * 结算单保存（兼容旧接口，直接确认）
     */
    @PostMapping("/settlement")
    @ApiOperation(value = "结算单保存")
    public AjaxResult paymentSettlementConfirm(@RequestBody PaymentRecord paymentRecord) {
        paymentRecord.setFeeType("2"); // 结算单
        return toAjax(paymentRecordService.settlement(paymentRecord));
    }

    /**
     * 根据合同编号结算单
     */
    @GetMapping("/settlement")
    @ApiOperation(value = "根据合同编号结算单")
    public AjaxResult settlement(String contractNumber) {
        PaymentRecord params = new PaymentRecord();
        params.setContractNumber(contractNumber);
        params.setFeeType("2"); // 结算单
        List<PaymentRecord> records = paymentRecordService.selectPaymentRecordList(params);
        if (records.size() < 1) {
            return AjaxResult.error("暂无数据");
        }

        return AjaxResult.success(records.get(0));
    }

    /**
     * 缴费提醒列表
     */
    @GetMapping("/paymentRemindList")
    @ApiOperation(value = "缴费提醒列表")
    public TableDataInfo paymentRemindList(PaymentRecord paymentRecord) {
        startPage();
        List<PaymentRemindResp> list = paymentRecordService.paymentRemindList(paymentRecord);
        return getDataTable(list);
    }

    /**
     * 费用统计
     */
    @GetMapping("/feeStatistics")
    @ApiOperation(value = "费用统计")
    public TableDataInfo feeStatistics(FeeStatisticsReq feeStatisticsReq) {
        startPage();
        List<FeeStatisticsResp> list = paymentRecordService.feeStatistics(feeStatisticsReq);
        return getDataTable(list);
    }

    /**
     * 导出费用统计
     */
    // @RequiresPermissions("custom:alarm:export")
    @Log(title = "费用统计", businessType = BusinessType.EXPORT)
    @PostMapping("/feeStatisticsExport")
    public void export(HttpServletResponse response, FeeStatisticsReq feeStatisticsReq) {
        List<FeeStatisticsResp> list = paymentRecordService.feeStatistics(feeStatisticsReq);
        ExcelUtil<FeeStatisticsResp> util = new ExcelUtil<FeeStatisticsResp>(FeeStatisticsResp.class);
        util.exportExcel(response, list, "费用统计数据");
    }

    /**
     * 缴费管理列表
     */
    @GetMapping("/managementList")
    @ApiOperation(value = "缴费管理列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contractNumber", value = "合同编号", paramType = "query"),
            @ApiImplicitParam(name = "elderlyName", value = "长者姓名", paramType = "query"),
            @ApiImplicitParam(name = "elderlyPhone", value = "联系电话", paramType = "query"),
            @ApiImplicitParam(name = "params.beginContractEndDate", value = "合同结束开始日期", paramType = "query"),
            @ApiImplicitParam(name = "params.endContractEndDate", value = "合同结束结束日期", paramType = "query"),
            @ApiImplicitParam(name = "params.type", value = "类型，字典：custom_payment_management_type", paramType = "query")
    })
    public TableDataInfo managementList(PaymentRecord paymentRecord) {
        startPage();
        List<PaymentManagementResp> list = paymentRecordService.selectPaymentManagementList(paymentRecord);
        return getDataTable(list);
    }

    /**
     * 确认缴费请求对象
     */
    @Data
    @ApiModel(value = "ConfirmPaymentRequest", description = "确认缴费请求")
    public static class ConfirmPaymentRequest {
        @ApiModelProperty(value = "票据号")
        private String billNumber;

        @ApiModelProperty(value = "实缴详情")
        private List<PaymentRecord.PaidDetail> paidDetails;

        @ApiModelProperty(value = "备注")
        private String remark;
    }

    /**
     * 离园管理列表
     */
    @GetMapping("/settlementManagementList")
    @ApiOperation(value = "离园管理列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contractNumber", value = "合同编号", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "长者姓名", paramType = "query"),
            @ApiImplicitParam(name = "params.elderlyPhone", value = "联系电话", paramType = "query"),
            @ApiImplicitParam(name = "params.paymentStatus", value = "状态，字典：custom_payment_record_status；0：暂存，1：已确认", paramType = "query"),
            @ApiImplicitParam(name = "params.beginDischargeDate", value = "离园开始日期", paramType = "query"),
            @ApiImplicitParam(name = "params.endDischargeDate", value = "离园结束日期", paramType = "query")
    })
    public TableDataInfo settlementManagementList(PaymentSettlementRecordSummary paymentSettlementRecordSummary) {
        startPage();
        List<PaymentSettlementManagementResp> list = paymentRecordService.selectPaymentSettlementManagementList(paymentSettlementRecordSummary);
        return getDataTable(list);
    }
}

