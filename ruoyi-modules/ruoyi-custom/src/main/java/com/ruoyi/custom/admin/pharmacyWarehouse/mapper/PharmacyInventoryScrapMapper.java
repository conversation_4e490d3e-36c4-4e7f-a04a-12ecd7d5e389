package com.ruoyi.custom.admin.pharmacyWarehouse.mapper;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryScrap;

import java.util.List;

/**
 * 药品报废明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface PharmacyInventoryScrapMapper {
    /**
     * 查询药品报废明细
     *
     * @param id 药品报废明细主键
     * @return 药品报废明细
     */
    public PharmacyInventoryScrap selectPharmacyInventoryScrapById(Long id);

    /**
     * 查询药品报废明细列表
     *
     * @param pharmacyInventoryScrap 药品报废明细
     * @return 药品报废明细集合
     */
    public List<PharmacyInventoryScrap> selectPharmacyInventoryScrapList(PharmacyInventoryScrap pharmacyInventoryScrap);

    /**
     * 新增药品报废明细
     *
     * @param pharmacyInventoryScrap 药品报废明细
     * @return 结果
     */
    public int insertPharmacyInventoryScrap(PharmacyInventoryScrap pharmacyInventoryScrap);

    /**
     * 修改药品报废明细
     *
     * @param pharmacyInventoryScrap 药品报废明细
     * @return 结果
     */
    public int updatePharmacyInventoryScrap(PharmacyInventoryScrap pharmacyInventoryScrap);

    /**
     * 删除药品报废明细
     *
     * @param id 药品报废明细主键
     * @return 结果
     */
    public int deletePharmacyInventoryScrapById(Long id);

    /**
     * 批量删除药品报废明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryScrapByIds(Long[] ids);

    /**
     * 查询药品报废明细列表
     *
     * @param pharmacyInventoryScrap 药品报废明细
     * @return 药品报废明细集合
     */
    List<PharmacyInventoryScrap> selectPharmacyInventoryScrapList2(PharmacyInventoryScrap pharmacyInventoryScrap);

    int insertBatch(List<PharmacyInventoryScrap> pharmacyInventoryScraps);
}

