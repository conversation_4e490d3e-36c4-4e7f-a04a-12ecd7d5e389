package com.ruoyi.custom.admin.pharmacyWarehouse.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryCheck;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PharmacyInventoryCheckReqVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.resp.PharmacyInventoryCheckRespVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 药品盘点明细Controller
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@RestController
@RequestMapping("/pharmacy/inventory/check")
@Api(tags = "药品盘点明细")
public class PharmacyInventoryCheckController extends BaseController {
    @Autowired
    private IPharmacyInventoryCheckService pharmacyInventoryCheckService;

    /**
     * 查询药品盘点明细列表
     */
    // @RequiresPermissions("custom:check:list")
    @GetMapping("/list")
    @ApiOperation("列表")
    public TableDataInfo list(PharmacyInventoryCheck pharmacyInventoryCheck) {
        startPage();
        List<PharmacyInventoryCheck> list = pharmacyInventoryCheckService.selectPharmacyInventoryCheckList2(pharmacyInventoryCheck);
        return getDataTable(list);
    }

    /**
     * 详情列表
     */
    // @RequiresPermissions("custom:in:list")
    @GetMapping("/subList")
    @ApiOperation(value = "获取详情列表")
    public TableDataInfo<PharmacyInventoryCheckRespVo> subList(@ApiParam(value = "编号", required = true) @RequestParam String serialNumber) {
        startPage();
        PharmacyInventoryCheck base = new PharmacyInventoryCheck();
        base.setSerialNumber(serialNumber);
        List<PharmacyInventoryCheck> list = pharmacyInventoryCheckService.selectPharmacyInventoryCheckList(base);
        if (CollUtil.isEmpty(list)) {
            return getDataTable(list);
        }
        List<PharmacyInventoryCheckRespVo.Detail> detailList = BeanUtil.copyToList(list, PharmacyInventoryCheckRespVo.Detail.class);
        PharmacyInventoryCheckRespVo respVo = new PharmacyInventoryCheckRespVo();
        BeanUtil.copyProperties(list.get(0), respVo, "detailList");
        respVo.setDetailList(detailList);
        return getDataTable(list);
    }

    /**
     * 添加/编辑
     */
    // @RequiresPermissions("custom:check:add")
    @Log(title = "添加/编辑", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "添加/编辑")
    public AjaxResult save(@RequestBody PharmacyInventoryCheckReqVo pharmacyInventoryCheckReqVo) {
        if (pharmacyInventoryCheckReqVo == null) {
            return AjaxResult.error("参数不能为空");
        }
        if (CollUtil.isEmpty(pharmacyInventoryCheckReqVo.getDetailList())) {
            return AjaxResult.error("明细不能为空");
        }
        if (pharmacyInventoryCheckReqVo.getStatus() != null && !"1".equals(pharmacyInventoryCheckReqVo.getStatus())) {
            return AjaxResult.error("当前盘点任务处于进行中或已完成状态，无法进行修改。");
        }
        return toAjax(pharmacyInventoryCheckService.save(pharmacyInventoryCheckReqVo));
    }

    /**
     * 盘点保存
     */
    @PostMapping("/save")
    @ApiOperation(value = "盘点保存")
    public AjaxResult checksave(@RequestBody PharmacyInventoryCheckReqVo pharmacyInventoryCheckReqVo) {
        if (pharmacyInventoryCheckReqVo == null) {
            return AjaxResult.error("参数不能为空");
        }
        if (CollUtil.isEmpty(pharmacyInventoryCheckReqVo.getDetailList())) {
            return AjaxResult.error("明细不能为空");
        }
        return toAjax(pharmacyInventoryCheckService.checksave(pharmacyInventoryCheckReqVo));
    }

    /**
     * 盘点完成
     */
    @PostMapping("/finish")
    public AjaxResult checkFinish(@RequestBody PharmacyInventoryCheckReqVo pharmacyInventoryCheckReqVo) {
        if (pharmacyInventoryCheckReqVo == null) {
            return AjaxResult.error("参数不能为空");
        }
        if (CollUtil.isEmpty(pharmacyInventoryCheckReqVo.getDetailList())) {
            return AjaxResult.error("明细不能为空");
        }
        return toAjax(pharmacyInventoryCheckService.checkFinish(pharmacyInventoryCheckReqVo));
    }

    // /**
    //  * 导出药品盘点明细列表
    //  */
    // // @RequiresPermissions("custom:check:export")
    // @Log(title = "药品盘点明细", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, PharmacyInventoryCheck pharmacyInventoryCheck) {
    //     List<PharmacyInventoryCheck> list = pharmacyInventoryCheckService.selectPharmacyInventoryCheckList(pharmacyInventoryCheck);
    //     ExcelUtil<PharmacyInventoryCheck> util = new ExcelUtil<PharmacyInventoryCheck>(PharmacyInventoryCheck. class);
    //     util.exportExcel(response, list, "药品盘点明细数据");
    // }
    //
    // /**
    //  * 获取药品盘点明细详细信息
    //  */
    // // @RequiresPermissions("custom:check:query")
    // @GetMapping(value = "/{id}")
    // public AjaxResult getInfo(@PathVariable("id") Long id) {
    //     return AjaxResult.success(pharmacyInventoryCheckService.selectPharmacyInventoryCheckById(id));
    // }

    // /**
    //  * 修改药品盘点明细
    //  */
    // // @RequiresPermissions("custom:check:edit")
    // @Log(title = "药品盘点明细", businessType = BusinessType.UPDATE)
    // @PutMapping
    // public AjaxResult edit(@RequestBody PharmacyInventoryCheck pharmacyInventoryCheck) {
    //     return toAjax(pharmacyInventoryCheckService.updatePharmacyInventoryCheck(pharmacyInventoryCheck));
    // }
    //
    // /**
    //  * 删除药品盘点明细
    //  */
    // // @RequiresPermissions("custom:check:remove")
    // @Log(title = "药品盘点明细", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{ids}")
    // public AjaxResult remove(@PathVariable Long[] ids) {
    //     return toAjax(pharmacyInventoryCheckService.deletePharmacyInventoryCheckByIds(ids));
    // }
}

