package com.ruoyi.custom.admin.pharmacyWarehouse.mapper;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventory;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyManagement;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 药品库存Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface PharmacyInventoryMapper {
    /**
     * 查询药品库存
     *
     * @param id 药品库存主键
     * @return 药品库存
     */
    public PharmacyInventory selectPharmacyInventoryById(Long id);

    /**
     * 查询药品库存列表
     *
     * @param pharmacyInventory 药品库存
     * @return 药品库存集合
     */
    public List<PharmacyInventory> selectPharmacyInventoryList(PharmacyInventory pharmacyInventory);

    /**
     * 新增药品库存
     *
     * @param pharmacyInventory 药品库存
     * @return 结果
     */
    public int insertPharmacyInventory(PharmacyInventory pharmacyInventory);

    /**
     * 修改药品库存
     *
     * @param pharmacyInventory 药品库存
     * @return 结果
     */
    public int updatePharmacyInventory(PharmacyInventory pharmacyInventory);

    /**
     * 删除药品库存
     *
     * @param id 药品库存主键
     * @return 结果
     */
    public int deletePharmacyInventoryById(Long id);

    /**
     * 批量删除药品库存
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryByIds(Long[] ids);

    /**
     * 查询药品库存列表
     *
     * @param pharmacyInventory 药品库存
     * @return 药品库存集合
     */
    List<PharmacyInventory> selectPharmacyInventoryList2(PharmacyInventory pharmacyInventory);

    void updateQuantity(@Param("id") Long id, @Param("quantity") Integer quantity);

    /**
     * 根据id和仓库id和药品id和批次查询药品库存（根据联合唯一索引查询库存信息）
     * @param warehouseId
     * @param pharmacyId
     * @param batch
     * @return
     */
    PharmacyInventory selectPharmacyInventoryByUnionId(@Param("warehouseId") Long warehouseId, @Param("pharmacyId") Long pharmacyId, @Param("batch") Integer batch);

    /**
     * 查询库存中存在的仓库列表
     *
     * @return
     */
    List<PharmacyInventory> getWarehouseListInInventory();

    /**
     * 根据仓库id获取库存中存在的药品列表
     * @param warehouseId
     * @return
     */
    List<PharmacyManagement> getPharmacyListInInventory(@Param("warehouseId") Long warehouseId);

    /**
     * 根据仓库id和药品id获取库存中存在的批次列表
     * @param warehouseId
     * @param pharmacyId
     * @return
     */
    List<Integer> getBatchList(@Param("warehouseId") Long warehouseId,@Param("pharmacyId") Long pharmacyId);

    /**
     * 根据库存id获取当前库存量
     * @param inventoryId
     * @return
     */
    int getCurrQuantityById(Long inventoryId);
}

