package com.ruoyi.custom.admin.pharmacyWarehouse.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "处方明细表")
public class PrescriptionDetail {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "编号")
    private String serialNumber;

    @ApiModelProperty(value = "老人id，对应：t_elderly_people_info")
    private Long elderlyId;

    @ApiModelProperty(value = "老人姓名")
    private String elderlyName;

    @ApiModelProperty(value = "开药医生id，对应：sys_user")
    private Long doctorId;

    @ApiModelProperty(value = "开药医生")
    private String doctorName;

    @ApiModelProperty(value = "开药时间")
    private Date prescriptionTime;

    @ApiModelProperty(value = "库存id，对应：t_pharmacy_inventory")
    private Long inventoryId;

    @ApiModelProperty(value = "药品id，对应：t_pharmacy_management")
    private Long medicineId;

    @ApiModelProperty(value = "药品名称")
    private String medicineName;

    @ApiModelProperty(value = "批次")
    private String batchNumber;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "剂量用法")
    private String dosageUsage;

    @ApiModelProperty(value = "预计疗程（天）")
    private Integer estimatedCourseDays;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "床位名称")
    @TableField(exist = false)
    private String bedName;
}

