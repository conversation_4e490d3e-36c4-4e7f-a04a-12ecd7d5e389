package com.ruoyi.custom.admin.pharmacyWarehouse.domain;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@Data
@ApiModel(value = "药品仓库")
public class PharmacyWarehouse {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "位置")
    private String location;

    @ApiModelProperty(value = "管理员id，对应sys_user")
    private Long managerId;

    @ApiModelProperty(value = "管理员")
    private String manager;
}

