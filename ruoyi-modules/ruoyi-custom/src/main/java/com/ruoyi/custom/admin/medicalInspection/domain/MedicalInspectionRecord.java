package com.ruoyi.custom.admin.medicalInspection.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "医护巡查记录表")
public class MedicalInspectionRecord {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "巡查id,对应“医护巡查信息表”")
    private Long inspectionId;

    @ApiModelProperty(value = "巡查人员id，对应sys_user")
    private Long inspectorId;

    @ApiModelProperty(value = "巡查人员")
    private String inspectorName;

    @ApiModelProperty(value = "居住环境")
    private String livingEnvironment;

    @ApiModelProperty(value = "身体状况")
    private String physicalCondition;

    @ApiModelProperty(value = "其他情况")
    private String otherConditions;

    @ApiModelProperty(value = "图片url,多个逗号隔开")
    private String imageUrls;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;
}

