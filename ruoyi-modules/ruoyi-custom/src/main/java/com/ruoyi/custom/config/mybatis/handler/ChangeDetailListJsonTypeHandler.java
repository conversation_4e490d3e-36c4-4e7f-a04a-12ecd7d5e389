package com.ruoyi.custom.config.mybatis.handler;

import com.alibaba.fastjson.JSON;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecord.Detail;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.VARCHAR) // 或 JdbcType.JSON，如果你数据库驱动支持
public class ChangeDetailListJsonTypeHandler extends BaseTypeHandler<List<Detail>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Detail> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<Detail> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<Detail> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<Detail> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<Detail> parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        return JSON.parseArray(json, Detail.class);
    }
}

