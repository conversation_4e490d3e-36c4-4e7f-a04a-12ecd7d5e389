package com.ruoyi.custom.admin.marketing.service;

import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecordImage;

import java.util.List;

/**
 * 缴费变更单镜像Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface IPaymentChangeRecordImageService {

    /**
     * 查询缴费变更单镜像
     * 
     * @param id 缴费变更单镜像主键
     * @return 缴费变更单镜像
     */
    public PaymentChangeRecordImage selectPaymentChangeRecordImageById(String id);

    /**
     * 查询缴费变更单镜像列表
     * 
     * @param paymentChangeRecordImage 缴费变更单镜像
     * @return 缴费变更单镜像集合
     */
    public List<PaymentChangeRecordImage> selectPaymentChangeRecordImageList(PaymentChangeRecordImage paymentChangeRecordImage);

    /**
     * 新增缴费变更单镜像
     * 
     * @param paymentChangeRecordImage 缴费变更单镜像
     * @return 结果
     */
    public int insertPaymentChangeRecordImage(PaymentChangeRecordImage paymentChangeRecordImage);

    /**
     * 修改缴费变更单镜像
     * 
     * @param paymentChangeRecordImage 缴费变更单镜像
     * @return 结果
     */
    public int updatePaymentChangeRecordImage(PaymentChangeRecordImage paymentChangeRecordImage);

    /**
     * 批量删除缴费变更单镜像
     * 
     * @param ids 需要删除的缴费变更单镜像主键集合
     * @return 结果
     */
    public int deletePaymentChangeRecordImageByIds(String[] ids);

    /**
     * 删除缴费变更单镜像信息
     *
     * @param id 缴费变更单镜像主键
     * @return 结果
     */
    public int deletePaymentChangeRecordImageById(String id);

    /**
     * 根据临时生成ID查询缴费变更单镜像
     *
     * @param tempGenerationId 临时生成ID
     * @return 缴费变更单镜像
     */
    public PaymentChangeRecordImage selectPaymentChangeRecordImageByTempGenerationId(String tempGenerationId);

    /**
     * 根据原始记录ID查询缴费变更单镜像
     *
     * @param originalId 原始记录ID
     * @return 缴费变更单镜像
     */
    public PaymentChangeRecordImage selectPaymentChangeRecordImageByOriginalId(String originalId);
}
