package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventory;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryIn;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryInMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryInService;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 药品入库明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class PharmacyInventoryInServiceImpl implements IPharmacyInventoryInService {
    @Autowired
    private PharmacyInventoryInMapper pharmacyInventoryInMapper;

    @Autowired
    private IPharmacyInventoryService pharmacyInventoryService;

    /**
     * 查询药品入库明细
     *
     * @param id 药品入库明细主键
     * @return 药品入库明细
     */
    @Override
    public PharmacyInventoryIn selectPharmacyInventoryInById(Long id) {
        return pharmacyInventoryInMapper.selectPharmacyInventoryInById(id);
    }

    /**
     * 查询药品入库明细列表
     *
     * @param pharmacyInventoryIn 药品入库明细
     * @return 药品入库明细
     */
    @Override
    public List<PharmacyInventoryIn> selectPharmacyInventoryInList(PharmacyInventoryIn pharmacyInventoryIn) {
        return pharmacyInventoryInMapper.selectPharmacyInventoryInList(pharmacyInventoryIn);
    }

    /**
     * 入库
     *
     * @param pharmacyInventoryIns 入库
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertPharmacyInventoryIn(List<PharmacyInventoryIn> pharmacyInventoryIns) {
        Date now = DateUtils.getNowDate();
        // 操作库存
        pharmacyInventoryIns.forEach(item -> {
            item.setCreateTime(now);
            if (item.getInventoryId() == null) { // 库存id为空说明库存没有，需新增
                PharmacyInventory pharmacyInventory = BeanUtil.copyProperties(item, PharmacyInventory.class);
                pharmacyInventoryService.insertPharmacyInventory(pharmacyInventory);
                item.setInventoryId(pharmacyInventory.getId());
            } else { // 库存id不为空说明库存有，需更新
                pharmacyInventoryService.updateQuantity(item.getInventoryId(), item.getQuantity());
            }
        });

        // 批量插入入库流水记录
        return pharmacyInventoryInMapper.insertBatch(pharmacyInventoryIns);
    }

    /**
     * 修改药品入库明细
     *
     * @param pharmacyInventoryIn 药品入库明细
     * @return 结果
     */
    @Override
    public int updatePharmacyInventoryIn(PharmacyInventoryIn pharmacyInventoryIn) {
        return pharmacyInventoryInMapper.updatePharmacyInventoryIn(pharmacyInventoryIn);
    }

    /**
     * 批量删除药品入库明细
     *
     * @param ids 需要删除的药品入库明细主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryInByIds(Long[] ids) {
        return pharmacyInventoryInMapper.deletePharmacyInventoryInByIds(ids);
    }

    /**
     * 删除药品入库明细信息
     *
     * @param id 药品入库明细主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryInById(Long id) {
        return pharmacyInventoryInMapper.deletePharmacyInventoryInById(id);
    }

    @Override
    public List<PharmacyInventoryIn> selectPharmacyInventoryInList2(PharmacyInventoryIn pharmacyInventoryIn) {
        return pharmacyInventoryInMapper.selectPharmacyInventoryInList2(pharmacyInventoryIn);
    }
}

