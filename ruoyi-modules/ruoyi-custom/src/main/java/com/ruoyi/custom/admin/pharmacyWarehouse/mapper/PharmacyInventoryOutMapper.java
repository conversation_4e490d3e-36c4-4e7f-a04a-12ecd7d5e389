package com.ruoyi.custom.admin.pharmacyWarehouse.mapper;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryOut;

import java.util.List;

/**
 * 药品出库明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface PharmacyInventoryOutMapper {
    /**
     * 查询药品出库明细
     *
     * @param id 药品出库明细主键
     * @return 药品出库明细
     */
    public PharmacyInventoryOut selectPharmacyInventoryOutById(Long id);

    /**
     * 查询药品出库明细列表
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 药品出库明细集合
     */
    public List<PharmacyInventoryOut> selectPharmacyInventoryOutList(PharmacyInventoryOut pharmacyInventoryOut);

    /**
     * 新增药品出库明细
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 结果
     */
    public int insertPharmacyInventoryOut(PharmacyInventoryOut pharmacyInventoryOut);

    /**
     * 修改药品出库明细
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 结果
     */
    public int updatePharmacyInventoryOut(PharmacyInventoryOut pharmacyInventoryOut);

    /**
     * 删除药品出库明细
     *
     * @param id 药品出库明细主键
     * @return 结果
     */
    public int deletePharmacyInventoryOutById(Long id);

    /**
     * 批量删除药品出库明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryOutByIds(Long[] ids);

    /**
     * 根据条件查询出库记录
     * @param pharmacyInventoryOut
     * @return
     */
    List<PharmacyInventoryOut> selectPharmacyInventoryOutList2(PharmacyInventoryOut pharmacyInventoryOut);

    /**
     * 批量新增
     * @param pharmacyInventoryOut
     * @return
     */
    int insertBatch(List<PharmacyInventoryOut> pharmacyInventoryOut);
}

