package com.ruoyi.custom.admin.storiedBuilding.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.storiedBuilding.domain.vo.BedRecordsInfoVo;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 床位基础信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@RestController
@RequestMapping("/bedBaseInfo")
@Api(value = "床位基础信息Controller", tags = {"床位基础信息"})
public class BedBaseInfoController extends BaseController {
    @Autowired
    private IBedBaseInfoService bedBaseInfoService;

    @Autowired
    private IRoomTypeBaseInfoService roomTypeBaseInfoService;

    /**
     * 查询床位基础信息列表
     */
    //@RequiresPermissions("storiedBuilding:bedBaseInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询床位基础信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "bedName", value = "床位名称", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "bedNum", value = "床位编号", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "bedState", value = "床位状态", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore BedBaseInfo bedBaseInfo) {
        startPage();
        List<BedBaseInfo> list = bedBaseInfoService.selectBedBaseInfoList(bedBaseInfo);
        return getDataTable(list);
    }

    /**
     * 房态图数据
     */
    @GetMapping("/getBedStateData")
    @ApiOperation(value = "房态图数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "楼栋&楼层&房间id", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "typeId", value = "房间类型id", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class)
    })
    public TableDataInfo getBedStateData(@ApiIgnore Long id, @ApiIgnore Long typeId) {
        startPage();
        List<JSONObject> list = bedBaseInfoService.getBedStateData(id, typeId);
        return getDataTable(list);
    }

    /**
     * 获取总床位数量、空闲床位数量、入住床位数量
     */
    @GetMapping("/getBedCountSummary")
    @ApiOperation(value = "获取总床位数量、空闲床位数量、入住床位数量")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "楼栋&楼层&房间id", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "typeId", value = "房间类型id", required = false, dataTypeClass = String.class)
    })
    public AjaxResult getBedCountSummary(@ApiIgnore Long id, @ApiIgnore Long typeId) {
        Map params = MapUtil.builder()
                .put("id", id)
                .put("typeId", typeId)
                .build();

        JSONObject data = bedBaseInfoService.getBedCountSummary(params);
        return AjaxResult.success(data);
    }

    /**
     * 房态图床位历史记录
     */
    @GetMapping("/getBedRecordsList")
    @ApiOperation(value = "房态图床位历史记录")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "bedId", value = "床位id", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "name", value = "老人姓名", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class)
    })
    public TableDataInfo<BedRecordsInfoVo> getBedRecordsList(String bedId, String name) {
        startPage();
        List<BedRecordsInfoVo> list = bedBaseInfoService.getBedRecordsList(bedId, name);
        return getDataTable(list);
    }

    /**
     * 根据房间获取所有房间内的床位信息
     */
    @GetMapping("/getRoomBedList")
    @ApiOperation(value = "根据房间获取所有房间内的床位信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "roomId", value = "房间id", required = false, dataTypeClass = String.class),
    })
    public AjaxResult getRoomBedList(Long roomId) {
        List<JSONObject> roomBedList = bedBaseInfoService.getRoomBedList(roomId);
        JSONObject typeBase = roomTypeBaseInfoService.getRoomTypeBase(roomId);
        return AjaxResult.success().put("list", roomBedList).put("data", typeBase);
    }

    /**
     * 导出床位基础信息列表
     */
    //@RequiresPermissions("storiedBuilding:bedBaseInfo:export")
    @ApiOperation(value = "导出床位基础信息列表")
    @Log(platform = "1", title = "床位基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BedBaseInfo bedBaseInfo) {
        List<BedBaseInfo> list = bedBaseInfoService.selectBedBaseInfoList(bedBaseInfo);
        ExcelUtil<BedBaseInfo> util = new ExcelUtil<BedBaseInfo>(BedBaseInfo.class);
        util.exportExcel(response, list, "床位基础信息数据");
    }

    /**
     * 获取床位基础信息详细信息
     */
    //@RequiresPermissions("storiedBuilding:bedBaseInfo:query")
    @ApiOperation(value = "获取床位基础信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bedBaseInfoService.selectBedBaseInfoById(id));
    }

    /**
     * 新增床位基础信息
     */
    //@RequiresPermissions("storiedBuilding:bedBaseInfo:add")
    @Log(platform = "1", title = "床位基础信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增床位基础信息")
    @PostMapping
    public AjaxResult add(@RequestBody BedBaseInfo bedBaseInfo) {
        return toAjax(bedBaseInfoService.insertBedBaseInfo(bedBaseInfo));
    }

    /**
     * 修改床位基础信息
     */
    //@RequiresPermissions("storiedBuilding:bedBaseInfo:edit")
    @Log(platform = "1", title = "床位基础信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改床位基础信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BedBaseInfo bedBaseInfo) {
        return toAjax(bedBaseInfoService.updateBedBaseInfo(bedBaseInfo));
    }

    /**
     * 删除床位基础信息
     */
    //@RequiresPermissions("storiedBuilding:bedBaseInfo:remove")
    @Log(platform = "1", title = "床位基础信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除床位基础信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bedBaseInfoService.deleteBedBaseInfoByIds(ids));
    }
}
