package com.ruoyi.custom.admin.serviceWorkOrder.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.care.domain.*;
import com.ruoyi.custom.admin.care.mapper.CareTaskRecordsInfoMapper;
import com.ruoyi.custom.admin.care.service.*;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.goodsOrder.domain.OrderBaseInfo;
import com.ruoyi.custom.admin.goodsOrder.mapper.OrderBaseInfoMapper;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveInfoSaveReqVO;
import com.ruoyi.custom.admin.serviceWorkOrder.domain.OrderServiceWork;
import com.ruoyi.custom.admin.serviceWorkOrder.mapper.ServiceWorkOrderMapper;
import com.ruoyi.custom.admin.serviceWorkOrder.param.ServiceWorkOrderParam;
import com.ruoyi.custom.admin.serviceWorkOrder.service.ServiceWorkOrderService;
import com.ruoyi.custom.admin.serviceWorkOrder.vo.ServiceWorkOrderRequestVo;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedCareInfoService;
import com.ruoyi.custom.utils.OrderUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class ServiceWorkOrderServiceImpl implements ServiceWorkOrderService {

    @Autowired
    private ServiceWorkOrderMapper serviceWorkOrderMapper;
    @Autowired
    private OrderBaseInfoMapper orderBaseInfoMapper;
    @Autowired
    private ICareComboServiceItemsBaseInfoService careComboServiceItemsBaseInfoService;
    @Autowired
    private ICareTaskRecordsInfoService careTaskRecordsInfoService;
    @Autowired
    private CareTaskRecordsInfoMapper careTaskRecordsInfoMapper;
    @Autowired
    private ICareWorkerInfoService careWorkerInfoService;
    @Autowired
    private ICareRecordInfoService careRecordInfoService;
    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;
    @Autowired
    private ICareProjectBaseInfoService careProjectBaseInfoService;
    @Autowired
    private IBedCareInfoService bedCareInfoService;

    /**
     * 指派服务人员  并 生成工单
     *
     * @param serviceWorkOrderParam
     * @return
     */
    @Override
    public int assignWorker(ServiceWorkOrderParam serviceWorkOrderParam) {
        Long workerId = serviceWorkOrderParam.getWorkerId();
        String id = serviceWorkOrderParam.getId();
        OrderServiceWork orderServiceWork = serviceWorkOrderMapper.selectById(id);
        orderServiceWork.setWorkerId(workerId);
        if (orderServiceWork.getStatus() != 1) {
            throw new SecurityException("当前工单状态异常！");
        }
        orderServiceWork.setStatus(2);
        return serviceWorkOrderMapper.updateById(orderServiceWork);
    }

    @Override
    public List<OrderServiceWork> getWorkOrderList(OrderServiceWork orderServiceWork) {
        QueryWrapper<OrderServiceWork> query = Wrappers.query();
        if (StrUtil.isNotEmpty(orderServiceWork.getId())) {
            query.like("id", orderServiceWork.getId());
        }
        return serviceWorkOrderMapper.selectList(query);
    }


    @Override
    public int saveList(LiveInfoSaveReqVO reqVO, String liveId, String careIndex) {
        String bedId = reqVO.getBedId();
        String comboId = reqVO.getComboId();
        CareComboServiceItemsBaseInfo careComboServiceItemsBaseInfo = new CareComboServiceItemsBaseInfo();
        careComboServiceItemsBaseInfo.setComboId(Long.parseLong(comboId));
        List<CareComboServiceItemsBaseInfo> careComboServiceItemsBaseInfos = careComboServiceItemsBaseInfoService.selectCareComboServiceItemsBaseInfoList(careComboServiceItemsBaseInfo);
        for (CareComboServiceItemsBaseInfo info : careComboServiceItemsBaseInfos) {
            OrderServiceWork orderServiceWork = new OrderServiceWork();
            orderServiceWork.setOrderId(null);
            orderServiceWork.setServiceId(info.getCareProjectId());
            // 根据服务id查询服务名称
            orderServiceWork.setServiceName(careProjectBaseInfoService.selectCareProjectBaseInfoById(info.getCareProjectId()).getCareName());
            orderServiceWork.setPlaceOrderWay(2);// 自动
            // 查询老人基本信息
            ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(reqVO.getUserId());
            orderServiceWork.setElderlyPeopleId(reqVO.getUserId());
            orderServiceWork.setName(elderlyPeopleInfo.getName());
            orderServiceWork.setPhone(elderlyPeopleInfo.getPhone());
            orderServiceWork.setLiveId(liveId);
            orderServiceWork.setWorkerId(StrUtil.isNotBlank(careIndex) ? Long.valueOf(careIndex) : null);
            orderServiceWork.setBedId(Long.parseLong(bedId));
            orderServiceWork.setCycle(info.getCycle());
            orderServiceWork.setFrequency(info.getFrequency());
            orderServiceWork.setStatus(StrUtil.isNotBlank(careIndex) ? 2 : 1);
            CareTaskRecordsInfo careTaskRecordsInfo = new CareTaskRecordsInfo();
            careTaskRecordsInfo.setLiveId(liveId);
            careTaskRecordsInfo.setUserId(reqVO.getUserId());
            careTaskRecordsInfo.setCareServiceId(info.getCareProjectId());
            careTaskRecordsInfo.setCycle(info.getCycle());
            careTaskRecordsInfo.setFrequency(info.getFrequency());
            careTaskRecordsInfo.setActualNumber(info.getFrequency());
            careTaskRecordsInfo.setBedId(Long.parseLong(bedId));
            // 2天/此次、5词一天
            if ("0".equals(info.getCycle())) {
                careTaskRecordsInfo.setFrequencyName(info.getCycle() + "/" + info.getFrequency() + "次/天");
            } else if ("1".equals(info.getCycle())) {
                careTaskRecordsInfo.setFrequencyName(info.getCycle() + "/" + info.getFrequency() + "天/次");
            }
            careTaskRecordsInfoService.insertCareTaskRecordsInfo(careTaskRecordsInfo);
            if ("0".equals(orderServiceWork.getCycle())) {
                aTSave(orderServiceWork);
            } else {
                aCSave(orderServiceWork);
            }
        }
        return 0;
    }

    @Override
    public List<JSONObject> appCareTaskList(OrderServiceWork orderServiceWork) {
        return serviceWorkOrderMapper.appCareTaskList(orderServiceWork);
    }

    public int aTSave(OrderServiceWork orderServiceWork) {
        Long userId = SecurityUtils.getUserId();// 用户id

        // 12小时除以频次，均匀安排执行时间
        String frequency = String.valueOf(orderServiceWork.getFrequency());
        int k = Integer.parseInt(frequency);
        int timeNum = (int) Math.floor(12 / k);
        try {
            for (int i = 0; i < k; i++) {
                String date = getDate(1);
                int num = 7 + timeNum * i;// 7为7点开始
                String time = num + ":00";
                String dateTime = date + " " + time;
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date parse = sf.parse(dateTime);
                orderServiceWork.setId(OrderUtils.getCommonOrderCode(OrderUtils.serviceWorkOrder));
                orderServiceWork.setReserveTime(parse);
                orderServiceWork.setCreateBy(String.valueOf(userId));
                orderServiceWork.setCreateTime(new Date());
                orderServiceWork.setUpdateTime(new Date());
                orderServiceWork.setUpdateBy(String.valueOf(userId));
                orderServiceWork.setDelFlag("0");// 显示
                serviceWorkOrderMapper.insertOrderServiceWork(orderServiceWork);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 1;
    }

    public int aCSave(OrderServiceWork orderServiceWork) {
        Long userId = SecurityUtils.getUserId();// 用户id

        String frequency = String.valueOf(orderServiceWork.getFrequency());
        int num = Integer.parseInt(frequency);
        String date = getDate(num) + " 08:00";
        try {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date parse = sf.parse(date);
            orderServiceWork.setId(OrderUtils.getCommonOrderCode(OrderUtils.serviceWorkOrder));
            orderServiceWork.setReserveTime(parse);
            orderServiceWork.setCreateBy(String.valueOf(userId));
            orderServiceWork.setCreateTime(new Date());
            orderServiceWork.setUpdateTime(new Date());
            orderServiceWork.setUpdateBy(String.valueOf(userId));
            orderServiceWork.setDelFlag("0");// 显示
            serviceWorkOrderMapper.insertOrderServiceWork(orderServiceWork);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 1;
    }

    public String getDate(int num) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, num);
        String yesterday = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        return yesterday;
    }

    /**
     * 定时任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateCare() {
        // 更新所有计划任务执行时间
        careTaskRecordsInfoService.updateAllTaskExecutionTime();

        // 若“预约时间”为昨日以前，则更新工单状态为“已过期”
        serviceWorkOrderMapper.updateWorkOrderState();

        // 生成今日任务
        CareTaskRecordsInfo careTaskRecordsInfo = new CareTaskRecordsInfo();
        careTaskRecordsInfo.setCycle("0");
        List<CareTaskRecordsInfo> careTaskRecordsInfos = careTaskRecordsInfoMapper.getCareTaskRecordsList(careTaskRecordsInfo);
        for (CareTaskRecordsInfo recordsInfo : careTaskRecordsInfos) {
            OrderServiceWork orderServiceWork = new OrderServiceWork();
            orderServiceWork.setOrderId(null); // 从入住生成的工单没有订单号
            orderServiceWork.setPlaceOrderWay(1); // 自动生成
            orderServiceWork.setElderlyPeopleId(recordsInfo.getUserId());
            // 查询老人基本信息
            ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(recordsInfo.getUserId());
            orderServiceWork.setPhone(elderlyPeopleInfo.getPhone());
            orderServiceWork.setName(elderlyPeopleInfo.getName());
            orderServiceWork.setWorkerId(recordsInfo.getCareWorkerId());
            orderServiceWork.setBedId(recordsInfo.getBedId());
            orderServiceWork.setLiveId(recordsInfo.getLiveId());
            orderServiceWork.setStatus(2);
            orderServiceWork.setServiceId(recordsInfo.getCareServiceId());
            // 查询服务信息
            CareProjectBaseInfo serviceProject = careProjectBaseInfoService.selectCareProjectBaseInfoById(recordsInfo.getCareServiceId());
            orderServiceWork.setServiceName(serviceProject.getCareName());
            // orderServiceWork.set
            orderServiceWork.setFrequency(recordsInfo.getFrequency());
            orderServiceWork.setCycle(recordsInfo.getCycle());

            aTSave(orderServiceWork);
        }
    }

    /**
     * 服务人员开始服务
     *
     * @param requestVo
     * @return
     */
    @Override
    public int startService(ServiceWorkOrderRequestVo requestVo) {
        String id = requestVo.getId();
        OrderServiceWork orderServiceWork = serviceWorkOrderMapper.selectById(id);
        orderServiceWork.setStartTime(new Date());
        orderServiceWork.setStartImg(requestVo.getStartImg());
        orderServiceWork.setStatus(3);
        return serviceWorkOrderMapper.updateById(orderServiceWork);
    }

    /**
     * 服务人员服务签退
     *
     * @param requestVo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int endService(ServiceWorkOrderRequestVo requestVo) {
        // init
        String id = requestVo.getId();
        Date date = new Date();
        OrderServiceWork orderServiceWork = serviceWorkOrderMapper.selectById(id);

        String orderId = orderServiceWork.getOrderId();
        // 走入住流程生成的工单没有订单
        if (StrUtil.isNotBlank(orderId)) {
            // 更新订单基础表
            OrderBaseInfo orderBaseInfo = orderBaseInfoMapper.selectById(orderId);
            if (null == orderServiceWork || null == orderBaseInfo) {
                throw new ServiceException("当前订单服务信息异常！");
            }
            orderBaseInfo.setFinishTime(new Date());
            orderBaseInfo.setStatus(4);// 订单改为已完成状态
            orderBaseInfo.setUpdateTime(date);
            orderBaseInfoMapper.updateById(orderBaseInfo);
        }

        // 校验护工是否存在
        CareWorkerInfo careWorkerInfo = careWorkerInfoService.selectCareWorkerInfoById(orderServiceWork.getWorkerId());
        if (null == careWorkerInfo) {
            throw new SecurityException("当前工单有异常！");
        }

        // 更新护工工单
        orderServiceWork.setEndTime(new Date());
        orderServiceWork.setEndImg(requestVo.getEndImg());
        Date startTime = orderServiceWork.getStartTime();
        Date newDate = new Date();
        long time = (newDate.getTime() - startTime.getTime()) / 1000 / 60;
        orderServiceWork.setServiceTime(new BigDecimal(time));
        orderServiceWork.setStatus(4);
        serviceWorkOrderMapper.updateById(orderServiceWork);

        // 生成护理记录
        CareRecordInfo careRecordInfo = new CareRecordInfo();
        careRecordInfo.setUserId(orderServiceWork.getElderlyPeopleId());
        careRecordInfo.setServiceItemsId(orderServiceWork.getServiceId());
        careRecordInfo.setCareWorkerId(orderServiceWork.getWorkerId());
        careRecordInfo.setBeginTime(orderServiceWork.getStartTime());
        careRecordInfo.setEndTime(date);
        careRecordInfo.setCareImg(requestVo.getEndImg());
        careRecordInfo.setCareTaskId(orderServiceWork.getId());
        careRecordInfo.setLiveId(orderServiceWork.getLiveId());
        careRecordInfoService.insertCareRecordInfo(careRecordInfo);

        return 1;
    }

    /**
     * 服务人员现场照片保存
     *
     * @param requestVo
     * @return
     */
    @Override
    public int servingUpload(ServiceWorkOrderRequestVo requestVo) {
        String id = requestVo.getId();
        OrderServiceWork orderServiceWork = serviceWorkOrderMapper.selectById(id);
        if (null == orderServiceWork) {
            throw new ServiceException("当前订单服务信息异常");
        }
        if (StringUtils.isEmpty(orderServiceWork.getLiveImg())) {
            orderServiceWork.setLiveImg(requestVo.getLiveImg());
        } else {
            orderServiceWork.setLiveImg(orderServiceWork.getLiveImg() + "|" + requestVo.getLiveImg());
        }

        return serviceWorkOrderMapper.updateById(orderServiceWork);
    }

    /**
     * 服务人员移动待完成服务列表
     *
     * @param orderServiceWork
     * @return
     */
    @Override
    public List<OrderServiceWork> getNotCompletedServiceWorkOrderListByWork(OrderServiceWork orderServiceWork) {
        Long workId = orderServiceWork.getWorkerId();
        QueryWrapper<OrderServiceWork> query = Wrappers.query();
        if (null == workId) {
            throw new SecurityException("当前登录用户信息异常！");
        }
        query.eq("worker_id", workId);
        if (!StringUtils.isEmpty(orderServiceWork.getName())) {
            query.like("name", orderServiceWork.getName());
        }
        query.in("status", 2, 3);// 未完成状态
        query.orderByDesc("status");
        query.orderByAsc("reserve_time");
        return serviceWorkOrderMapper.selectList(query);
    }

    /**
     * 服务人员移动已完成服务列表
     *
     * @param homeOrderServiceWork
     * @return
     */
    // @Override
    // public List<HomeOrderServiceWork> getCompletedServiceWorkOrderListByWork(HomeOrderServiceWork homeOrderServiceWork) {
    //     Long workId = homeOrderServiceWork.getWorkerId();
    //     QueryWrapper<HomeOrderServiceWork> query = Wrappers.query();
    //     if (null == workId) {
    //         throw new SecurityException("当前登录用户信息异常！");
    //     }
    //     query.eq("worker_id", workId);
    //     if (!StringUtils.isEmpty(homeOrderServiceWork.getName())) {
    //         query.like("name", homeOrderServiceWork.getName());
    //     }
    //     query.eq("status", 4);// 已完成状态
    //     query.orderByDesc("end_time");
    //     return serviceWorkOrderMapper.selectList(query);
    // }

    // @Override
    // public AppServiceWorkOrderInfoRequestVo getServiceWorkOrderInfoById(String id) {
    //     HomeOrderServiceWork orderServiceWork = serviceWorkOrderMapper.selectById(id);
    //     AppServiceWorkOrderInfoRequestVo appServiceWorkOrderInfoRequestVo = new AppServiceWorkOrderInfoRequestVo();
    //     BeanUtil.copyProperties(orderServiceWork, appServiceWorkOrderInfoRequestVo);
    //     HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(orderServiceWork.getOrderId());
    //     if (null == homeOrderBaseInfo || null == orderServiceWork) {
    //         throw new SecurityException("当前工单信息异常！");
    //     }
    //     appServiceWorkOrderInfoRequestVo.setPayType(homeOrderBaseInfo.getPayWay());
    //     if (orderServiceWork.getServiceType() == 0) {
    //         appServiceWorkOrderInfoRequestVo.setPayAmount(homeOrderBaseInfo.getTotalFee());
    //     } else {// 如果是套餐的话支付金额为0
    //         appServiceWorkOrderInfoRequestVo.setPayAmount(new BigDecimal(0));
    //     }
    //     QueryWrapper<HomeOrderCommentInfo> query = Wrappers.query();
    //     query.eq("order_id", homeOrderBaseInfo.getId());
    //     HomeOrderCommentInfo homeOrderCommentInfo = homeOrderCommentInfoMapper.selectOne(query);
    //     if (null != homeOrderCommentInfo) {
    //         appServiceWorkOrderInfoRequestVo.setCommentContent(homeOrderCommentInfo.getCommentContent());
    //     }
    //     return appServiceWorkOrderInfoRequestVo;
    // }
}
