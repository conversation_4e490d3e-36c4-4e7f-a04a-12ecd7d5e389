package com.ruoyi.custom.admin.pharmacyWarehouse.resp;

import com.ruoyi.custom.admin.pharmacyWarehouse.req.PharmacyInventoryInReqVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "药品入库 respVo")
public class PharmacyInventoryInRespVo {
    @ApiModelProperty(value = "编号，格式：YPPD-20230202-0001")
    private String serialNumber;

    @ApiModelProperty(value = "仓库id，对应t_pharmacy_warehouse")
    private Long warehouseId;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "经办人id，对应sys_user")
    private Long operatorId;

    @ApiModelProperty(value = "经办人")
    private String operatorName;

    @ApiModelProperty(value = "来源，字典：custom_pharmacy_inventory_in_source")
    private String source;

    @ApiModelProperty(value = "入库时间")
    private Date inTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "明细")
    private List<PharmacyInventoryInRespVo.Detail> detailList;

    @ApiModel(value = "明细")
    @Data
    public static class Detail {
        @ApiModelProperty(value = "id")
        private Long id;

        @ApiModelProperty(value = "库存id，对应t_pharmacy_drug")
        private Long inventoryId;

        @ApiModelProperty(value = "药品id，对应t_pharmacy_drug")
        private Long drugId;

        @ApiModelProperty(value = "药品名称")
        private String drugName;

        @ApiModelProperty(value = "批次")
        private Integer batch;

        @ApiModelProperty(value = "规格")
        private String specifications;

        @ApiModelProperty(value = "计量单位")
        private String unit;

        @ApiModelProperty(value = "入库数量")
        private Integer quantity;

        @ApiModelProperty(value = "市场价")
        private BigDecimal marketPrice;

        @ApiModelProperty(value = "成本价")
        private BigDecimal costPrice;

        @ApiModelProperty(value = "生产时间")
        private Date productionTime;

        @ApiModelProperty(value = "保质期")
        private Integer shelfLife;
    }
}

