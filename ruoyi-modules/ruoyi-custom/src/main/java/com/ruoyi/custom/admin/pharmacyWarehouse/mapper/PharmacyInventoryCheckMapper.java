package com.ruoyi.custom.admin.pharmacyWarehouse.mapper;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryCheck;

import java.util.List;


/**
 * 药品盘点明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface PharmacyInventoryCheckMapper {
    /**
     * 查询药品盘点明细
     *
     * @param id 药品盘点明细主键
     * @return 药品盘点明细
     */
    public PharmacyInventoryCheck selectPharmacyInventoryCheckById(Long id);

    /**
     * 查询药品盘点明细列表
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 药品盘点明细集合
     */
    public List<PharmacyInventoryCheck> selectPharmacyInventoryCheckList(PharmacyInventoryCheck pharmacyInventoryCheck);

    /**
     * 新增药品盘点明细
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 结果
     */
    public int insertPharmacyInventoryCheck(PharmacyInventoryCheck pharmacyInventoryCheck);

    /**
     * 修改药品盘点明细
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 结果
     */
    public int updatePharmacyInventoryCheck(PharmacyInventoryCheck pharmacyInventoryCheck);

    /**
     * 删除药品盘点明细
     *
     * @param id 药品盘点明细主键
     * @return 结果
     */
    public int deletePharmacyInventoryCheckById(Long id);

    /**
     * 批量删除药品盘点明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryCheckByIds(Long[] ids);

    /**
     * 查询药品盘点明细列表
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 药品盘点明细集合
     */
    List<PharmacyInventoryCheck> selectPharmacyInventoryCheckList2(PharmacyInventoryCheck pharmacyInventoryCheck);

    /**
     * 批量新增药品盘点明细
     *
     * @param items
     * @return
     */
    int insertBatch(List<PharmacyInventoryCheck> items);

    void deleteBySerialNumber(String serialNumber);

    // /**
    //  * 批量删除药品盘点明细
    //  *
    //  * @param array
    //  */
    // void deleteBatch(Long[] ids);
}
