package com.ruoyi.custom.admin.pharmacyWarehouse.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "处方 reqVo")
public class PrescriptionDetailReqVo {
    @ApiModelProperty(value = "编号，格式：YPPD-20230202-0001")
    private String serialNumber;

    @ApiModelProperty(value = "老人id，对应：t_elderly_people_info")
    private Long elderlyId;

    @ApiModelProperty(value = "老人姓名")
    private String elderlyName;

    @ApiModelProperty(value = "开药医生id，对应：sys_user")
    private Long doctorId;

    @ApiModelProperty(value = "开药医生")
    private String doctorName;

    @ApiModelProperty(value = "开药时间")
    private Date prescriptionTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "明细")
    private List<Detail> detailList;

    @ApiModel(value = "明细")
    @Data
    public static class Detail {
        @ApiModelProperty(value = "id")
        private Long id;

        @ApiModelProperty(value = "库存id，对应：t_pharmacy_inventory")
        private Long inventoryId;

        @ApiModelProperty(value = "药品id，对应：t_pharmacy_management")
        private Long medicineId;

        @ApiModelProperty(value = "药品名称")
        private String medicineName;

        @ApiModelProperty(value = "批次")
        private String batchNumber;

        @ApiModelProperty(value = "规格")
        private String specification;

        @ApiModelProperty(value = "剂量用法")
        private String dosageUsage;

        @ApiModelProperty(value = "预计疗程（天）")
        private Integer estimatedCourseDays;

        @ApiModelProperty(value = "备注")
        private String remark;
    }
}

