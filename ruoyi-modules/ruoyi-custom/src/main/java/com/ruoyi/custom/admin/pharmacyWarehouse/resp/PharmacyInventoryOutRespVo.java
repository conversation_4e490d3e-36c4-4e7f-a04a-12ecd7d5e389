package com.ruoyi.custom.admin.pharmacyWarehouse.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "药品出库 respVo")
public class PharmacyInventoryOutRespVo {
    @ApiModelProperty(value = "编号，格式：YPPD-20230202-0001")
    private String serialNumber;

    @ApiModelProperty(value = "仓库id，对应t_pharmacy_warehouse")
    private Long warehouseId;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "经办人id，对应sys_user")
    private Long operatorId;

    @ApiModelProperty(value = "经办人")
    private String operatorName;

    @ApiModelProperty(value = "去向，字典：custom_pharmacy_inventory_out_destination")
    private String destination;

    @ApiModelProperty(value = "出库时间")
    private Date outTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "明细")
    private List<Detail> detailList;

    @ApiModel(value = "明细")
    @Data
    public static class Detail {
        @ApiModelProperty(value = "id")
        private Long id;

        @ApiModelProperty(value = "库存id，对应t_pharmacy_drug")
        private Long inventoryId;

        @ApiModelProperty(value = "药品id，对应t_pharmacy_drug")
        private Long drugId;

        @ApiModelProperty(value = "药品名称")
        private String drugName;

        @ApiModelProperty(value = "批次")
        private Integer batch;

        @ApiModelProperty(value = "出库数量")
        private Integer quantity;

        @ApiModelProperty(value = "剩余库存")
        private Integer reamin;

    }
}

