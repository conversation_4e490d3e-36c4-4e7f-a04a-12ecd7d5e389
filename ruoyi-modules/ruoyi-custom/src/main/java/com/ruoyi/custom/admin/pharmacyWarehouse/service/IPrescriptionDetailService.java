package com.ruoyi.custom.admin.pharmacyWarehouse.service;


import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PrescriptionDetail;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PrescriptionDetailReqVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.resp.PharmacyInventoryInRespVo;

import java.util.List;


/**
 * 处方明细Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface IPrescriptionDetailService {
    /**
     * 查询处方明细
     *
     * @param id 处方明细主键
     * @return 处方明细
     */
    public PrescriptionDetail selectPrescriptionDetailById(Long id);

    /**
     * 查询处方明细列表
     *
     * @param prescriptionDetail 处方明细
     * @return 处方明细集合
     */
    public List<PrescriptionDetail> selectPrescriptionDetailList(PrescriptionDetail prescriptionDetail);

    /**
     * 新增处方明细
     *
     * @param prescriptionDetail 处方明细
     * @return 结果
     */
    public int insertPrescriptionDetail(PrescriptionDetail prescriptionDetail);

    /**
     * 修改处方明细
     *
     * @param prescriptionDetail 处方明细
     * @return 结果
     */
    public int updatePrescriptionDetail(PrescriptionDetail prescriptionDetail);

    /**
     * 批量删除处方明细
     *
     * @param ids 需要删除的处方明细主键集合
     * @return 结果
     */
    public int deletePrescriptionDetailByIds(Long[] ids);

    /**
     * 删除处方明细信息
     *
     * @param id 处方明细主键
     * @return 结果
     */
    public int deletePrescriptionDetailById(Long id);

    /**
     * 根据处方id查询处方明细
     * @param prescriptionDetail
     * @return
     */
    List<PrescriptionDetail> selectPrescriptionDetailList2(PrescriptionDetail prescriptionDetail);

    /**
     * 添加/编辑
     * @param prescriptionDetailReqVo
     * @return
     */
    int save(PrescriptionDetailReqVo prescriptionDetailReqVo);

    /**
     * 根据处方编号删除
     * @param serialNumber
     * @return
     */
    int deletePrescriptionDetailBySerialNumber(String serialNumber);

    /**
     * 带子列表
     *
     * @param prescriptionDetail
     * @return
     */
    List<PharmacyInventoryInRespVo> selectPrescriptionDetailList3(PrescriptionDetail prescriptionDetail);
}

