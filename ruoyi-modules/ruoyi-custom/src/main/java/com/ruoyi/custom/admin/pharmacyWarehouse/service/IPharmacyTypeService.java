package com.ruoyi.custom.admin.pharmacyWarehouse.service;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyType;

import java.util.List;


/**
 * 药品类型Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IPharmacyTypeService {
    /**
     * 查询药品类型
     *
     * @param id 药品类型主键
     * @return 药品类型
     */
    public PharmacyType selectPharmacyTypeById(Long id);

    /**
     * 查询药品类型列表
     *
     * @param pharmacyType 药品类型
     * @return 药品类型集合
     */
    public List<PharmacyType> selectPharmacyTypeList(PharmacyType pharmacyType);

    /**
     * 新增药品类型
     *
     * @param pharmacyType 药品类型
     * @return 结果
     */
    public int insertPharmacyType(PharmacyType pharmacyType);

    /**
     * 修改药品类型
     *
     * @param pharmacyType 药品类型
     * @return 结果
     */
    public int updatePharmacyType(PharmacyType pharmacyType);

    /**
     * 批量删除药品类型
     *
     * @param ids 需要删除的药品类型主键集合
     * @return 结果
     */
    public int deletePharmacyTypeByIds(Long[] ids);

    /**
     * 删除药品类型信息
     *
     * @param id 药品类型主键
     * @return 结果
     */
    public int deletePharmacyTypeById(Long id);
}

