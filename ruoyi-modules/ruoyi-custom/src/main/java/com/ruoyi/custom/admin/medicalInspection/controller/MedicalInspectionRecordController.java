package com.ruoyi.custom.admin.medicalInspection.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.medicalInspection.domain.MedicalInspectionRecord;
import com.ruoyi.custom.admin.medicalInspection.service.IMedicalInspectionRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 医护巡查记录Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/medicalInspection/record")
@Api(tags = "医护巡查记录")
public class MedicalInspectionRecordController extends BaseController {
    @Autowired
    private IMedicalInspectionRecordService medicalInspectionRecordService;

    /**
     * 查询医护巡查记录列表
     */
    // @RequiresPermissions("custom:record:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询医护巡查记录列表")
    public TableDataInfo list(MedicalInspectionRecord medicalInspectionRecord) {
        startPage();
        List<MedicalInspectionRecord> list = medicalInspectionRecordService.selectMedicalInspectionRecordList(medicalInspectionRecord);
        return getDataTable(list);
    }

    /**
     * 获取医护巡查记录详细信息
     */
    // @RequiresPermissions("custom:record:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取医护巡查记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(medicalInspectionRecordService.selectMedicalInspectionRecordById(id));
    }

    /**
     * 新增医护巡查记录
     */
    // @RequiresPermissions("custom:record:add")
    @Log(title = "医护巡查记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增医护巡查记录")
    public AjaxResult add(@RequestBody MedicalInspectionRecord medicalInspectionRecord) {
        return toAjax(medicalInspectionRecordService.insertMedicalInspectionRecord(medicalInspectionRecord));
    }

    /**
     * 修改医护巡查记录
     */
    // @RequiresPermissions("custom:record:edit")
    @Log(title = "医护巡查记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改医护巡查记录")
    public AjaxResult edit(@RequestBody MedicalInspectionRecord medicalInspectionRecord) {
        return toAjax(medicalInspectionRecordService.updateMedicalInspectionRecord(medicalInspectionRecord));
    }

    /**
     * 删除医护巡查记录
     */
    // @RequiresPermissions("custom:record:remove")
    @Log(title = "医护巡查记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除医护巡查记录")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(medicalInspectionRecordService.deleteMedicalInspectionRecordByIds(ids));
    }

    // /**
    //  * 导出医护巡查记录列表
    //  */
    // // @RequiresPermissions("custom:record:export")
    // @Log(title = "医护巡查记录", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, MedicalInspectionRecord medicalInspectionRecord) {
    //     List<MedicalInspectionRecord> list = medicalInspectionRecordService.selectMedicalInspectionRecordList(medicalInspectionRecord);
    //     ExcelUtil<MedicalInspectionRecord> util = new ExcelUtil<MedicalInspectionRecord>(MedicalInspectionRecord.class);
    //     util.exportExcel(response, list, "医护巡查记录数据");
    // }
}

