package com.ruoyi.custom.admin.pharmacyWarehouse.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "药品盘点 reqVo")
public class PharmacyInventoryCheckReqVo {
    @ApiModelProperty(value = "编号，格式：YPPD-20230202-0001")
    private String serialNumber;

    @ApiModelProperty(value = "盘点名称")
    private String checkName;

    @ApiModelProperty(value = "仓库id，对应t_pharmacy_warehouse")
    private Long warehouseId;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "计划开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planStartDate;

    @ApiModelProperty(value = "计划结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planEndDate;

    @ApiModelProperty(value = "盘点人id，对应sys_user")
    private Long operatorId;

    @ApiModelProperty(value = "盘点人")
    private String operatorName;

    @ApiModelProperty(value = "状态，字典：custom_pharmacy_inventory_check_status；1：未开始，2：盘点中，3：已结束")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "明细")
    private List<Detail> detailList;

    @ApiModel(value = "明细")
    @Data
    public static class Detail {
        @ApiModelProperty(value = "id")
        private Long id;

        @ApiModelProperty(value = "库存id，对应t_pharmacy_drug")
        private Long inventoryId;

        @ApiModelProperty(value = "药品id，对应t_pharmacy_drug")
        private Long drugId;

        @ApiModelProperty(value = "药品名称")
        private String drugName;

        @ApiModelProperty(value = "批次")
        private Integer batch;

        @ApiModelProperty(value = "盘点数量（盘点前数量）")
        private Integer checkQuantity;

        @ApiModelProperty(value = "实际数量（盘点后数量）")
        private Integer realQuantity;
    }
}

