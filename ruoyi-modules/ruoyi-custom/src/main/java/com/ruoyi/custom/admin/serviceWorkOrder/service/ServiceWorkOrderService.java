package com.ruoyi.custom.admin.serviceWorkOrder.service;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveInfoSaveReqVO;
import com.ruoyi.custom.admin.serviceWorkOrder.domain.OrderServiceWork;
import com.ruoyi.custom.admin.serviceWorkOrder.param.ServiceWorkOrderParam;
import com.ruoyi.custom.admin.serviceWorkOrder.vo.ServiceWorkOrderRequestVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_service_work_order服务层
 * @date 2022-07-18
 */
@Service
public interface ServiceWorkOrderService {

    // int save(HomeOrderServiceWork homeOrderServiceWork);

    /**
     * 分配人员
     *
     * @param serviceWorkOrderParam
     * @return
     */
    int assignWorker(ServiceWorkOrderParam serviceWorkOrderParam);

    List<OrderServiceWork> getWorkOrderList(OrderServiceWork orderServiceWork);

    public int saveList(LiveInfoSaveReqVO reqVO, String liveId, String careIndex);

    /**
     * 小程序获取护理提醒列表
     *
     * @param
     * @return
     */
    List<JSONObject> appCareTaskList(OrderServiceWork orderServiceWork);

    /**
     * 按次生成提醒
     *
     * @param orderServiceWork
     * @return
     */
    public int aCSave(OrderServiceWork orderServiceWork);

    /**
     * 按天生成提醒
     *
     * @param orderServiceWork
     * @return
     */
    public int aTSave(OrderServiceWork orderServiceWork);

    /**
     * 服务人员开始服务
     *
     * @param requestVo
     * @return
     */
    int startService(ServiceWorkOrderRequestVo requestVo);

    /**
     * 服务人员服务签退
     *
     * @param requestVo
     * @return
     */
    int endService(ServiceWorkOrderRequestVo requestVo);

    /**
     * 服务人员现场照片保存
     *
     * @param requestVo
     * @return
     */
    int servingUpload(ServiceWorkOrderRequestVo requestVo);

    void generateCare();

    /**
     * 服务人员移动待完成服务列表
     *
     * @param orderServiceWork
     * @return
     */
    List<OrderServiceWork> getNotCompletedServiceWorkOrderListByWork(OrderServiceWork orderServiceWork);

    /**
     * 服务人员移动已完成服务列表
     *
     * @param homeOrderServiceWork
     * @return
     */
    // List<HomeOrderServiceWork> getCompletedServiceWorkOrderListByWork(HomeOrderServiceWork homeOrderServiceWork);

    /**
     * 服务人员移动服务详情
     *
     * @param id
     * @return
     */
    // HomeAppServiceWorkOrderInfoRequestVo getServiceWorkOrderInfoById(String id);
}
