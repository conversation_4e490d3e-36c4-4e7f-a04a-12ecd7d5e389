package com.ruoyi.custom.admin.pharmacyWarehouse.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PrescriptionDetail;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PrescriptionDetailReqVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.resp.PharmacyInventoryInRespVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.resp.PrescriptionDetailRespVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPrescriptionDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 处方明细Controller
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RestController
@RequestMapping("/prescription")
@Api(tags = "处方明细")
public class PrescriptionDetailController extends BaseController {
    @Autowired
    private IPrescriptionDetailService prescriptionDetailService;

    /**
     * 查询处方明细列表
     */
    // @RequiresPermissions("custom:detail:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(PrescriptionDetail prescriptionDetail) {
        startPage();
        List<PrescriptionDetail> list = prescriptionDetailService.selectPrescriptionDetailList2(prescriptionDetail);
        return getDataTable(list);
    }

    /**
     * 详情列表
     */
    // @RequiresPermissions("custom:in:list")
    @GetMapping("/subList")
    @ApiOperation(value = "获取详情列表")
    public TableDataInfo<PrescriptionDetailRespVo> subList(@ApiParam(value = "编号", required = true) @RequestParam String serialNumber) {
        startPage();
        PrescriptionDetail base = new PrescriptionDetail();
        base.setSerialNumber(serialNumber);
        List<PrescriptionDetail> list = prescriptionDetailService.selectPrescriptionDetailList(base);
        if (CollUtil.isEmpty(list)) {
            return getDataTable(list);
        }
        List<PrescriptionDetailRespVo.Detail> detailList = BeanUtil.copyToList(list, PrescriptionDetailRespVo.Detail.class);
        PrescriptionDetailRespVo respVo = new PrescriptionDetailRespVo();
        BeanUtil.copyProperties(list.get(0), respVo, "detailList");
        respVo.setDetailList(detailList);
        return getDataTable(list);
    }

    /**
     * 添加/编辑
     */
    // @RequiresPermissions("custom:check:add")
    @Log(title = "添加/编辑", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "添加/编辑")
    public AjaxResult save(@RequestBody PrescriptionDetailReqVo prescriptionDetailReqVo) {
        if (prescriptionDetailReqVo == null) {
            return AjaxResult.error("参数不能为空");
        }
        if (CollUtil.isEmpty(prescriptionDetailReqVo.getDetailList())) {
            return AjaxResult.error("明细不能为空");
        }
        return toAjax(prescriptionDetailService.save(prescriptionDetailReqVo));
    }

    /**
     * 删除处方
     */
    // @RequiresPermissions("custom:detail:remove")
    @Log(title = "处方明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{serialNumber}")
    @ApiOperation(value = "删除处方")
    public AjaxResult remove(@PathVariable("serialNumber") String serialNumber) {
        return toAjax(prescriptionDetailService.deletePrescriptionDetailBySerialNumber(serialNumber));
    }

    // /**
    //  * 导出处方明细列表
    //  */
    // // @RequiresPermissions("custom:detail:export")
    // @Log(title = "处方明细", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, PrescriptionDetail prescriptionDetail) {
    //     List<PrescriptionDetail> list = prescriptionDetailService.selectPrescriptionDetailList(prescriptionDetail);
    //     ExcelUtil<PrescriptionDetail> util = new ExcelUtil<PrescriptionDetail>(PrescriptionDetail. class);
    //     util.exportExcel(response, list, "处方明细数据");
    // }

    // /**
    //  * 删除处方明细
    //  */
    // // @RequiresPermissions("custom:detail:remove")
    // @Log(title = "处方明细", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{ids}")
    // public AjaxResult remove(@PathVariable Long[] ids) {
    //     return toAjax(prescriptionDetailService.deletePrescriptionDetailByIds(ids));
    // }
}

