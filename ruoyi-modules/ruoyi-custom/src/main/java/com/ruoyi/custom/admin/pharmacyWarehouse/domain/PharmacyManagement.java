package com.ruoyi.custom.admin.pharmacyWarehouse.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "药品管理")
public class PharmacyManagement {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "药品名称")
    private String name;

    @ApiModelProperty(value = "药品类型id，对应t_pharmacy_type")
    private Long typeId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "药品类型")
    @TableField(exist = false)
    private String typeName;
}

