package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;


import java.util.List;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyWarehouse;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyWarehouseMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 药品仓库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class PharmacyWarehouseServiceImpl implements IPharmacyWarehouseService {
    @Autowired
    private PharmacyWarehouseMapper pharmacyWarehouseMapper;

    /**
     * 查询药品仓库
     *
     * @param id 药品仓库主键
     * @return 药品仓库
     */
    @Override
    public PharmacyWarehouse selectPharmacyWarehouseById(Long id) {
        return pharmacyWarehouseMapper.selectPharmacyWarehouseById(id);
    }

    /**
     * 查询药品仓库列表
     *
     * @param pharmacyWarehouse 药品仓库
     * @return 药品仓库
     */
    @Override
    public List<PharmacyWarehouse> selectPharmacyWarehouseList(PharmacyWarehouse pharmacyWarehouse) {
        return pharmacyWarehouseMapper.selectPharmacyWarehouseList(pharmacyWarehouse);
    }

    /**
     * 新增药品仓库
     *
     * @param pharmacyWarehouse 药品仓库
     * @return 结果
     */
    @Override
    public int insertPharmacyWarehouse(PharmacyWarehouse pharmacyWarehouse) {
        return pharmacyWarehouseMapper.insertPharmacyWarehouse(pharmacyWarehouse);
    }

    /**
     * 修改药品仓库
     *
     * @param pharmacyWarehouse 药品仓库
     * @return 结果
     */
    @Override
    public int updatePharmacyWarehouse(PharmacyWarehouse pharmacyWarehouse) {
        return pharmacyWarehouseMapper.updatePharmacyWarehouse(pharmacyWarehouse);
    }

    /**
     * 批量删除药品仓库
     *
     * @param ids 需要删除的药品仓库主键
     * @return 结果
     */
    @Override
    public int deletePharmacyWarehouseByIds(Long[] ids) {
        return pharmacyWarehouseMapper.deletePharmacyWarehouseByIds(ids);
    }

    /**
     * 删除药品仓库信息
     *
     * @param id 药品仓库主键
     * @return 结果
     */
    @Override
    public int deletePharmacyWarehouseById(Long id) {
        return pharmacyWarehouseMapper.deletePharmacyWarehouseById(id);
    }
}

