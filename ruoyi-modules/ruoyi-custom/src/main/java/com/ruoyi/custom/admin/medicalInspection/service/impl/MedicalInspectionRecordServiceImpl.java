package com.ruoyi.custom.admin.medicalInspection.service.impl;


import java.util.List;

import com.ruoyi.custom.admin.medicalInspection.domain.MedicalInspectionRecord;
import com.ruoyi.custom.admin.medicalInspection.mapper.MedicalInspectionRecordMapper;
import com.ruoyi.custom.admin.medicalInspection.service.IMedicalInspectionRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 医护巡查记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class MedicalInspectionRecordServiceImpl implements IMedicalInspectionRecordService {
    @Autowired
    private MedicalInspectionRecordMapper medicalInspectionRecordMapper;

    /**
     * 查询医护巡查记录
     *
     * @param id 医护巡查记录主键
     * @return 医护巡查记录
     */
    @Override
    public MedicalInspectionRecord selectMedicalInspectionRecordById(Long id) {
        return medicalInspectionRecordMapper.selectMedicalInspectionRecordById(id);
    }

    /**
     * 查询医护巡查记录列表
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 医护巡查记录
     */
    @Override
    public List<MedicalInspectionRecord> selectMedicalInspectionRecordList(MedicalInspectionRecord medicalInspectionRecord) {
        return medicalInspectionRecordMapper.selectMedicalInspectionRecordList(medicalInspectionRecord);
    }

    /**
     * 新增医护巡查记录
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 结果
     */
    @Override
    public int insertMedicalInspectionRecord(MedicalInspectionRecord medicalInspectionRecord) {
        return medicalInspectionRecordMapper.insertMedicalInspectionRecord(medicalInspectionRecord);
    }

    /**
     * 修改医护巡查记录
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 结果
     */
    @Override
    public int updateMedicalInspectionRecord(MedicalInspectionRecord medicalInspectionRecord) {
        return medicalInspectionRecordMapper.updateMedicalInspectionRecord(medicalInspectionRecord);
    }

    /**
     * 批量删除医护巡查记录
     *
     * @param ids 需要删除的医护巡查记录主键
     * @return 结果
     */
    @Override
    public int deleteMedicalInspectionRecordByIds(Long[] ids) {
        return medicalInspectionRecordMapper.deleteMedicalInspectionRecordByIds(ids);
    }

    /**
     * 删除医护巡查记录信息
     *
     * @param id 医护巡查记录主键
     * @return 结果
     */
    @Override
    public int deleteMedicalInspectionRecordById(Long id) {
        return medicalInspectionRecordMapper.deleteMedicalInspectionRecordById(id);
    }
}

