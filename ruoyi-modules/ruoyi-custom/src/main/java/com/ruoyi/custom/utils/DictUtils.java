package com.ruoyi.custom.utils;


import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class DictUtils {

    private static DictUtils dictUtils;
    @Autowired
    protected RemoteSystemService remoteSystemService;

    public static String selectDictLabel(String dictType, String dictValue) {
        R<String> stringR = dictUtils.remoteSystemService.selectDictLabel(dictType, dictValue);
        String data = stringR.getData();
        return data;
    }

    @PostConstruct // 完成对service的注入
    public void init() {
        dictUtils = this;
        dictUtils.remoteSystemService = this.remoteSystemService;
    }


}
