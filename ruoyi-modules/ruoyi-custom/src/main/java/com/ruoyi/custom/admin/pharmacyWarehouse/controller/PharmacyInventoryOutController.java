package com.ruoyi.custom.admin.pharmacyWarehouse.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryOut;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PharmacyInventoryOutReqVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.resp.PharmacyInventoryOutRespVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryOutService;
import com.ruoyi.custom.utils.OrderUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 药品出库明细Controller
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@RestController
@RequestMapping("/pharmacy/inventory/out")
@Api(tags = "药品出库明细")
public class PharmacyInventoryOutController extends BaseController {
    @Autowired
    private IPharmacyInventoryOutService pharmacyInventoryOutService;

    /**
     * 查询药品出库明细列表
     */
    // @RequiresPermissions("custom:out:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(PharmacyInventoryOut pharmacyInventoryOut) {
        startPage();
        List<PharmacyInventoryOut> list = pharmacyInventoryOutService.selectPharmacyInventoryOutList2(pharmacyInventoryOut);
        return getDataTable(list);
    }

    /**
     * 详情列表
     */
    @GetMapping("/subList")
    @ApiOperation(value = "获取详情列表")
    public TableDataInfo<PharmacyInventoryOutRespVo> subList(@ApiParam(value = "编号", required = true) @RequestParam String serialNumber) {
        startPage();
        PharmacyInventoryOut base = new PharmacyInventoryOut();
        base.setSerialNumber(serialNumber);
        List<PharmacyInventoryOut> list = pharmacyInventoryOutService.selectPharmacyInventoryOutList(base);
        if (CollUtil.isEmpty(list)) {
            return getDataTable(list);
        }
        List<PharmacyInventoryOutRespVo.Detail> detailList = BeanUtil.copyToList(list, PharmacyInventoryOutRespVo.Detail.class);
        PharmacyInventoryOutRespVo respVo = new PharmacyInventoryOutRespVo();
        BeanUtil.copyProperties(list.get(0), respVo, "detailList");
        respVo.setDetailList(detailList);
        return getDataTable(list);
    }

    /**
     * 出库
     */
    // @RequiresPermissions("custom:out:add")
    @Log(title = "出库", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "出库")
    public AjaxResult add(@RequestBody PharmacyInventoryOutReqVo pharmacyInventoryOutReqVo) {
        List<PharmacyInventoryOut> items = BeanUtil.copyToList(pharmacyInventoryOutReqVo.getDetailList(), PharmacyInventoryOut.class);
        items.forEach(item -> {
            BeanUtil.copyProperties(pharmacyInventoryOutReqVo, item, "detailList");
        });
        return toAjax(pharmacyInventoryOutService.insertPharmacyInventoryOut(items));
    }

    /**
     * 生成出库编号
     */
    @GetMapping("/generateSerialNumber")
    @ApiOperation(value = "生成出库编号")
    public AjaxResult generateSerialNumber() {
        return AjaxResult.success("操作成功", OrderUtils.getOutInventoryCode());
    }

    // /**
    //  * 导出药品出库明细列表
    //  */
    // // @RequiresPermissions("custom:out:export")
    // @Log(title = "药品出库明细", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, PharmacyInventoryOut pharmacyInventoryOut) {
    //     List<PharmacyInventoryOut> list = pharmacyInventoryOutService.selectPharmacyInventoryOutList(pharmacyInventoryOut);
    //     ExcelUtil<PharmacyInventoryOut> util = new ExcelUtil<PharmacyInventoryOut>(PharmacyInventoryOut. class);
    //     util.exportExcel(response, list, "药品出库明细数据");
    // }
    //
    // /**
    //  * 获取药品出库明细详细信息
    //  */
    // // @RequiresPermissions("custom:out:query")
    // @GetMapping(value = "/{id}")
    // public AjaxResult getInfo(@PathVariable("id") Long id) {
    //     return AjaxResult.success(pharmacyInventoryOutService.selectPharmacyInventoryOutById(id));
    // }

    // /**
    //  * 修改药品出库明细
    //  */
    // // @RequiresPermissions("custom:out:edit")
    // @Log(title = "药品出库明细", businessType = BusinessType.UPDATE)
    // @PutMapping
    // public AjaxResult edit(@RequestBody PharmacyInventoryOut pharmacyInventoryOut) {
    //     return toAjax(pharmacyInventoryOutService.updatePharmacyInventoryOut(pharmacyInventoryOut));
    // }
    //
    // /**
    //  * 删除药品出库明细
    //  */
    // // @RequiresPermissions("custom:out:remove")
    // @Log(title = "药品出库明细", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{ids}")
    // public AjaxResult remove(@PathVariable Long[] ids) {
    //     return toAjax(pharmacyInventoryOutService.deletePharmacyInventoryOutByIds(ids));
    // }
}

