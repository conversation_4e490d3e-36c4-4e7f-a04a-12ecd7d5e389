package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PrescriptionDetail;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PrescriptionDetailMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PrescriptionDetailReqVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.resp.PharmacyInventoryInRespVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPrescriptionDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 处方明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Service
public class PrescriptionDetailServiceImpl implements IPrescriptionDetailService {
    @Autowired
    private PrescriptionDetailMapper prescriptionDetailMapper;

    /**
     * 查询处方明细
     *
     * @param id 处方明细主键
     * @return 处方明细
     */
    @Override
    public PrescriptionDetail selectPrescriptionDetailById(Long id) {
        return prescriptionDetailMapper.selectPrescriptionDetailById(id);
    }

    /**
     * 查询处方明细列表
     *
     * @param prescriptionDetail 处方明细
     * @return 处方明细
     */
    @Override
    public List<PrescriptionDetail> selectPrescriptionDetailList(PrescriptionDetail prescriptionDetail) {
        return prescriptionDetailMapper.selectPrescriptionDetailList(prescriptionDetail);
    }

    /**
     * 新增处方明细
     *
     * @param prescriptionDetail 处方明细
     * @return 结果
     */
    @Override
    public int insertPrescriptionDetail(PrescriptionDetail prescriptionDetail) {
        return prescriptionDetailMapper.insertPrescriptionDetail(prescriptionDetail);
    }

    /**
     * 修改处方明细
     *
     * @param prescriptionDetail 处方明细
     * @return 结果
     */
    @Override
    public int updatePrescriptionDetail(PrescriptionDetail prescriptionDetail) {
        return prescriptionDetailMapper.updatePrescriptionDetail(prescriptionDetail);
    }

    /**
     * 批量删除处方明细
     *
     * @param ids 需要删除的处方明细主键
     * @return 结果
     */
    @Override
    public int deletePrescriptionDetailByIds(Long[] ids) {
        return prescriptionDetailMapper.deletePrescriptionDetailByIds(ids);
    }

    /**
     * 删除处方明细信息
     *
     * @param id 处方明细主键
     * @return 结果
     */
    @Override
    public int deletePrescriptionDetailById(Long id) {
        return prescriptionDetailMapper.deletePrescriptionDetailById(id);
    }

    @Override
    public List<PrescriptionDetail> selectPrescriptionDetailList2(PrescriptionDetail prescriptionDetail) {
        return prescriptionDetailMapper.selectPrescriptionDetailList2(prescriptionDetail);
    }

    @Override
    public int save(PrescriptionDetailReqVo prescriptionDetailReqVo) {
        // 查询单号是否存在
        String serialNumber = prescriptionDetailReqVo.getSerialNumber();
        PrescriptionDetail params = new PrescriptionDetail();
        params.setSerialNumber(serialNumber);
        // List<PrescriptionDetail> checks = prescriptionDetailMapper.selectPrescriptionDetailList(params);
        List<PrescriptionDetail> items = BeanUtil.copyToList(prescriptionDetailReqVo.getDetailList(), PrescriptionDetail.class);
        items.forEach(item -> {
            BeanUtil.copyProperties(prescriptionDetailReqVo, item, "detailList");
        });
        // if(CollUtil.isNotEmpty(checks))
        //     prescriptionDetailMapper.deleteBySerialNumber(serialNumber);
        return prescriptionDetailMapper.insertBatch(items);

    }

    @Override
    public int deletePrescriptionDetailBySerialNumber(String serialNumber) {
        return prescriptionDetailMapper.deleteBySerialNumber(serialNumber);
    }

    @Override
    public List<PharmacyInventoryInRespVo> selectPrescriptionDetailList3(PrescriptionDetail prescriptionDetail) {
        return prescriptionDetailMapper.selectPrescriptionDetailList3(prescriptionDetail);
    }
}

