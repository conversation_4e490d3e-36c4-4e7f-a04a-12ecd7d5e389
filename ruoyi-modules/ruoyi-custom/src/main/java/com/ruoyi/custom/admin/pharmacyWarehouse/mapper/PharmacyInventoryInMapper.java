package com.ruoyi.custom.admin.pharmacyWarehouse.mapper;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryIn;

import java.util.List;


/**
 * 药品入库明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface PharmacyInventoryInMapper {
    /**
     * 查询药品入库明细
     *
     * @param id 药品入库明细主键
     * @return 药品入库明细
     */
    public PharmacyInventoryIn selectPharmacyInventoryInById(Long id);

    /**
     * 查询药品入库明细列表
     *
     * @param pharmacyInventoryIn 药品入库明细
     * @return 药品入库明细集合
     */
    public List<PharmacyInventoryIn> selectPharmacyInventoryInList(PharmacyInventoryIn pharmacyInventoryIn);

    /**
     * 新增药品入库明细
     *
     * @param pharmacyInventoryIn 药品入库明细
     * @return 结果
     */
    public int insertPharmacyInventoryIn(PharmacyInventoryIn pharmacyInventoryIn);

    /**
     * 修改药品入库明细
     *
     * @param pharmacyInventoryIn 药品入库明细
     * @return 结果
     */
    public int updatePharmacyInventoryIn(PharmacyInventoryIn pharmacyInventoryIn);

    /**
     * 删除药品入库明细
     *
     * @param id 药品入库明细主键
     * @return 结果
     */
    public int deletePharmacyInventoryInById(Long id);

    /**
     * 批量删除药品入库明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryInByIds(Long[] ids);

    List<PharmacyInventoryIn> selectPharmacyInventoryInList2(PharmacyInventoryIn pharmacyInventoryIn);

    int insertBatch(List<PharmacyInventoryIn> pharmacyInventoryIns);
}

