package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;


import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryOut;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryOutMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryOutService;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 药品出库明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Service
public class PharmacyInventoryOutServiceImpl implements IPharmacyInventoryOutService {
    @Autowired
    private PharmacyInventoryOutMapper pharmacyInventoryOutMapper;

    @Autowired
    private IPharmacyInventoryService pharmacyInventoryService;

    /**
     * 查询药品出库明细
     *
     * @param id 药品出库明细主键
     * @return 药品出库明细
     */
    @Override
    public PharmacyInventoryOut selectPharmacyInventoryOutById(Long id) {
        return pharmacyInventoryOutMapper.selectPharmacyInventoryOutById(id);
    }

    /**
     * 查询药品出库明细列表
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 药品出库明细
     */
    @Override
    public List<PharmacyInventoryOut> selectPharmacyInventoryOutList(PharmacyInventoryOut pharmacyInventoryOut) {
        return pharmacyInventoryOutMapper.selectPharmacyInventoryOutList(pharmacyInventoryOut);
    }

    /**
     * 新增药品出库明细
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertPharmacyInventoryOut(List<PharmacyInventoryOut> pharmacyInventoryOut) {
        Date now = DateUtils.getNowDate();

        // 操作库存
        pharmacyInventoryOut.forEach(item -> {
            item.setCreateTime(now);
            updateQuantityWithLock(item.getInventoryId(), -item.getQuantity());
        });

        // 批量插入出库流水记录
        return pharmacyInventoryOutMapper.insertBatch(pharmacyInventoryOut);
    }

    // 修改更新库存的方法，加入悲观锁
    private void updateQuantityWithLock(Long inventoryId, int quantity) {
        // 查询库存并加锁，之后再更新库存
        int currentQuantity = pharmacyInventoryService.getCurrQuantityById(inventoryId);
        if (currentQuantity + quantity < 0) {
            throw new ServiceException("库存不足");
        }
        pharmacyInventoryService.updateQuantity(inventoryId, quantity);
    }

    /**
     * 修改药品出库明细
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 结果
     */
    @Override
    public int updatePharmacyInventoryOut(PharmacyInventoryOut pharmacyInventoryOut) {
        return pharmacyInventoryOutMapper.updatePharmacyInventoryOut(pharmacyInventoryOut);
    }

    /**
     * 批量删除药品出库明细
     *
     * @param ids 需要删除的药品出库明细主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryOutByIds(Long[] ids) {
        return pharmacyInventoryOutMapper.deletePharmacyInventoryOutByIds(ids);
    }

    /**
     * 删除药品出库明细信息
     *
     * @param id 药品出库明细主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryOutById(Long id) {
        return pharmacyInventoryOutMapper.deletePharmacyInventoryOutById(id);
    }

    @Override
    public List<PharmacyInventoryOut> selectPharmacyInventoryOutList2(PharmacyInventoryOut pharmacyInventoryOut) {
        return pharmacyInventoryOutMapper.selectPharmacyInventoryOutList2(pharmacyInventoryOut);
    }
}

