package com.ruoyi.custom.admin.marketing.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 缴费变更单镜像对象 t_payment_change_record_image
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "PaymentChangeRecordImage", description = "缴费变更单镜像")
public class PaymentChangeRecordImage extends BaseEntity {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 临时生成ID，用于关联 /change/info 与 暂存操作
     */
    @ApiModelProperty(value = "临时生成ID，用于关联 /change/info 与 暂存操作")
    private String tempGenerationId;

    /**
     * 对应主表 t_payment_change_record.id，暂存时回填
     */
    @ApiModelProperty(value = "对应主表 t_payment_change_record.id，暂存时回填")
    private String originalId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 居住id
     */
    @ApiModelProperty(value = "居住id")
    private String liveId;

    /**
     * 老人ID
     */
    @ApiModelProperty(value = "老人ID")
    private String elderlyId;

    /**
     * 老人姓名
     */
    @ApiModelProperty(value = "老人姓名")
    private String elderlyName;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartDate;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    /**
     * 合同周期（月数）
     */
    @ApiModelProperty(value = "合同周期（月数）")
    private Integer contractCycle;

    /**
     * 护理级别
     */
    @ApiModelProperty(value = "护理级别")
    private String careLevel;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String bedName;

    /**
     * 本次账户变动金额
     */
    @ApiModelProperty(value = "本次账户变动金额")
    private BigDecimal accountAddCost;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 缴费状态，字典：custom_payment_record_status；0：暂存，1：已确认
     */
    @ApiModelProperty(value = "缴费状态")
    private String paymentStatus;

    /**
     * 变更详情（List<Detail> JSON）
     */
    @ApiModelProperty(value = "变更详情")
    private List<PaymentChangeRecord.Detail> details;
}
