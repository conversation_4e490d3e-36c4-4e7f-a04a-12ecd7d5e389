package com.ruoyi.custom.admin.marketing.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 缴费变更单镜像对象 t_payment_change_record_image
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "缴费变更单镜像", description = "缴费变更单镜像表")
public class PaymentChangeRecordImage extends BaseEntity {

    @TableId
    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("临时生成ID，用于关联 /change/info 与 暂存操作")
    private String tempGenerationId;

    @ApiModelProperty("对应 t_payment_change_record.id，用于反向追溯")
    private String originalId;

    @ApiModelProperty("合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "居住id")
    private String liveId;

    @ApiModelProperty("老人ID")
    private String elderlyId;

    @ApiModelProperty("老人姓名")
    private String elderlyName;

    @ApiModelProperty("合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartDate;

    @ApiModelProperty("合同结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    @ApiModelProperty("合同周期（月）")
    private Integer contractCycle;

    @ApiModelProperty("护理级别")
    private String careLevel;

    @ApiModelProperty("床位")
    private String bedName;

    @ApiModelProperty(value = "本次账户变动金额")
    private BigDecimal accountAddCost;

    @ApiModelProperty("备注")
    private String remark;

    /**
     * 缴费状态，字典：custom_payment_record_status；0：暂存，1：已确认
     */
    @ApiModelProperty("缴费状态")
    private String paymentStatus;

    @ApiModelProperty("变更详情（JSON）")
    private List<PaymentChangeRecord.Detail> details;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createTime;
}