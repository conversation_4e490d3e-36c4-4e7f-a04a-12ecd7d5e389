package com.ruoyi.custom.utils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName BasePageRequest
 * @Description 分页基础请求参数
 * <AUTHOR>
 * @Date 2022/7/18 13:41
 */
@Data
@ApiModel(description = "参数")
public class BasePageRequest {

    @NotNull(message = "当前页码必填")
    @ApiModelProperty(value = "当前页码", required = true)
    Long pageNum;

    @NotNull(message = "每页条数必填")
    @ApiModelProperty(value = "每页条数", required = true)
    Long pageSize;


}
