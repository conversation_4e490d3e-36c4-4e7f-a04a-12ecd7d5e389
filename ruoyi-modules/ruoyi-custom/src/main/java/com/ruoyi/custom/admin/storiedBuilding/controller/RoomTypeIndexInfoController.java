package com.ruoyi.custom.admin.storiedBuilding.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeIndexInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 房间和类型的关联Controller
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@RestController
@RequestMapping("/RoomTypeIndexInfo")
@Api(value = "房间和类型的关联Controller", tags = "房间和类型的关联")
public class RoomTypeIndexInfoController extends BaseController {
    @Autowired
    private IRoomTypeIndexInfoService RoomTypeIndexInfoService;

    /**
     * 查询房间和类型的关联列表
     */
    //@RequiresPermissions("storiedBuilding:RoomTypeIndexInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询房间和类型的关联列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "typeId", value = "房间类型id", paramType = "query", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(name = "roomId", value = "房间id", paramType = "query", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "query", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore RoomTypeIndexInfo RoomTypeIndexInfo) {
        startPage();
        List<RoomTypeIndexInfo> list = RoomTypeIndexInfoService.selectRoomTypeIndexInfoList(RoomTypeIndexInfo);
        return getDataTable(list);
    }

    /**
     * 导出房间和类型的关联列表
     */
    //@RequiresPermissions("storiedBuilding:RoomTypeIndexInfo:export")
    @Log(platform = "1", title = "房间和类型的关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出房间和类型的关联列表")
    public void export(HttpServletResponse response, RoomTypeIndexInfo RoomTypeIndexInfo) {
        List<RoomTypeIndexInfo> list = RoomTypeIndexInfoService.selectRoomTypeIndexInfoList(RoomTypeIndexInfo);
        ExcelUtil<RoomTypeIndexInfo> util = new ExcelUtil<RoomTypeIndexInfo>(RoomTypeIndexInfo.class);
        util.exportExcel(response, list, "房间和类型的关联数据");
    }

    /**
     * 获取房间和类型的关联详细信息
     */
    //@RequiresPermissions("storiedBuilding:RoomTypeIndexInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取房间和类型的关联详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(RoomTypeIndexInfoService.selectRoomTypeIndexInfoById(id));
    }

    /**
     * 新增房间和类型的关联
     */
    //@RequiresPermissions("storiedBuilding:RoomTypeIndexInfo:add")
    @Log(platform = "1", title = "房间和类型的关联", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "房间和类型的关联")
    public AjaxResult add(@RequestBody RoomTypeIndexInfo RoomTypeIndexInfo) {
        return toAjax(RoomTypeIndexInfoService.insertRoomTypeIndexInfo(RoomTypeIndexInfo));
    }

    /**
     * 修改房间和类型的关联
     */
    //@RequiresPermissions("storiedBuilding:RoomTypeIndexInfo:edit")
    @Log(platform = "1", title = "房间和类型的关联", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "房间和类型的关联")
    public AjaxResult edit(@RequestBody RoomTypeIndexInfo RoomTypeIndexInfo) {
        return toAjax(RoomTypeIndexInfoService.updateRoomTypeIndexInfo(RoomTypeIndexInfo));
    }

    /**
     * 删除房间和类型的关联
     */
    //@RequiresPermissions("storiedBuilding:RoomTypeIndexInfo:remove")
    @Log(platform = "1", title = "房间和类型的关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "房间和类型的关联")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(RoomTypeIndexInfoService.deleteRoomTypeIndexInfoByIds(ids));
    }
}
