package com.ruoyi.custom.admin.marketing.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.admin.marketing.mapper.PaymentChangeRecordImageMapper;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecordImage;
import com.ruoyi.custom.admin.marketing.service.IPaymentChangeRecordImageService;

/**
 * 缴费变更单镜像Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
public class PaymentChangeRecordImageServiceImpl implements IPaymentChangeRecordImageService {
    @Autowired
    private PaymentChangeRecordImageMapper paymentChangeRecordImageMapper;

    /**
     * 查询缴费变更单镜像
     *
     * @param id 缴费变更单镜像主键
     * @return 缴费变更单镜像
     */
    @Override
    public PaymentChangeRecordImage selectPaymentChangeRecordImageById(String id) {
        return paymentChangeRecordImageMapper.selectPaymentChangeRecordImageById(id);
    }

    /**
     * 根据临时生成ID查询缴费变更单镜像
     *
     * @param tempGenerationId 临时生成ID
     * @return 缴费变更单镜像
     */
    @Override
    public PaymentChangeRecordImage selectPaymentChangeRecordImageByTempGenerationId(String tempGenerationId) {
        return paymentChangeRecordImageMapper.selectPaymentChangeRecordImageByTempGenerationId(tempGenerationId);
    }

    /**
     * 查询缴费变更单镜像列表
     *
     * @param paymentChangeRecordImage 缴费变更单镜像
     * @return 缴费变更单镜像
     */
    @Override
    public List<PaymentChangeRecordImage> selectPaymentChangeRecordImageList(PaymentChangeRecordImage paymentChangeRecordImage) {
        return paymentChangeRecordImageMapper.selectPaymentChangeRecordImageList(paymentChangeRecordImage);
    }

    /**
     * 新增缴费变更单镜像
     *
     * @param paymentChangeRecordImage 缴费变更单镜像
     * @return 结果
     */
    @Override
    public int insertPaymentChangeRecordImage(PaymentChangeRecordImage paymentChangeRecordImage) {
        return paymentChangeRecordImageMapper.insertPaymentChangeRecordImage(paymentChangeRecordImage);
    }

    /**
     * 修改缴费变更单镜像
     *
     * @param paymentChangeRecordImage 缴费变更单镜像
     * @return 结果
     */
    @Override
    public int updatePaymentChangeRecordImage(PaymentChangeRecordImage paymentChangeRecordImage) {
        return paymentChangeRecordImageMapper.updatePaymentChangeRecordImage(paymentChangeRecordImage);
    }

    /**
     * 批量删除缴费变更单镜像
     *
     * @param ids 需要删除的缴费变更单镜像主键
     * @return 结果
     */
    @Override
    public int deletePaymentChangeRecordImageByIds(String[] ids) {
        return paymentChangeRecordImageMapper.deletePaymentChangeRecordImageByIds(ids);
    }

    /**
     * 删除缴费变更单镜像信息
     *
     * @param id 缴费变更单镜像主键
     * @return 结果
     */
    @Override
    public int deletePaymentChangeRecordImageById(String id) {
        return paymentChangeRecordImageMapper.deletePaymentChangeRecordImageById(id);
    }
}