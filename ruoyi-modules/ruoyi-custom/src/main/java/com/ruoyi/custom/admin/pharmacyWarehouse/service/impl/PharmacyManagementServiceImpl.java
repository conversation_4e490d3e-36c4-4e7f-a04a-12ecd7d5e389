package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;


import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyManagement;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyManagementMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 药品管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class PharmacyManagementServiceImpl implements IPharmacyManagementService {
    @Autowired
    private PharmacyManagementMapper pharmacyManagementMapper;

    /**
     * 查询药品管理
     *
     * @param id 药品管理主键
     * @return 药品管理
     */
    @Override
    public PharmacyManagement selectPharmacyManagementById(Long id) {
        return pharmacyManagementMapper.selectPharmacyManagementById(id);
    }

    /**
     * 查询药品管理列表
     *
     * @param pharmacyManagement 药品管理
     * @return 药品管理
     */
    @Override
    public List<PharmacyManagement> selectPharmacyManagementList(PharmacyManagement pharmacyManagement) {
        return pharmacyManagementMapper.selectPharmacyManagementList(pharmacyManagement);
    }

    /**
     * 新增药品管理
     *
     * @param pharmacyManagement 药品管理
     * @return 结果
     */
    @Override
    public int insertPharmacyManagement(PharmacyManagement pharmacyManagement) {
        pharmacyManagement.setCreateTime(DateUtils.getNowDate());
        return pharmacyManagementMapper.insertPharmacyManagement(pharmacyManagement);
    }

    /**
     * 修改药品管理
     *
     * @param pharmacyManagement 药品管理
     * @return 结果
     */
    @Override
    public int updatePharmacyManagement(PharmacyManagement pharmacyManagement) {
        return pharmacyManagementMapper.updatePharmacyManagement(pharmacyManagement);
    }

    /**
     * 批量删除药品管理
     *
     * @param ids 需要删除的药品管理主键
     * @return 结果
     */
    @Override
    public int deletePharmacyManagementByIds(Long[] ids) {
        return pharmacyManagementMapper.deletePharmacyManagementByIds(ids);
    }

    /**
     * 删除药品管理信息
     *
     * @param id 药品管理主键
     * @return 结果
     */
    @Override
    public int deletePharmacyManagementById(Long id) {
        return pharmacyManagementMapper.deletePharmacyManagementById(id);
    }
}
