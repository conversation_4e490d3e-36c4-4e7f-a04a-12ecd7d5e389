package com.ruoyi.custom.admin.pharmacyWarehouse.mapper;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyWarehouse;

import java.util.List;

/**
 * 药品仓库Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface PharmacyWarehouseMapper {
    /**
     * 查询药品仓库
     *
     * @param id 药品仓库主键
     * @return 药品仓库
     */
    public PharmacyWarehouse selectPharmacyWarehouseById(Long id);

    /**
     * 查询药品仓库列表
     *
     * @param pharmacyWarehouse 药品仓库
     * @return 药品仓库集合
     */
    public List<PharmacyWarehouse> selectPharmacyWarehouseList(PharmacyWarehouse pharmacyWarehouse);

    /**
     * 新增药品仓库
     *
     * @param pharmacyWarehouse 药品仓库
     * @return 结果
     */
    public int insertPharmacyWarehouse(PharmacyWarehouse pharmacyWarehouse);

    /**
     * 修改药品仓库
     *
     * @param pharmacyWarehouse 药品仓库
     * @return 结果
     */
    public int updatePharmacyWarehouse(PharmacyWarehouse pharmacyWarehouse);

    /**
     * 删除药品仓库
     *
     * @param id 药品仓库主键
     * @return 结果
     */
    public int deletePharmacyWarehouseById(Long id);

    /**
     * 批量删除药品仓库
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePharmacyWarehouseByIds(Long[] ids);
}

