package com.ruoyi.custom.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @version 1.0
 * @java 项目 www.fhadmin.org
 * @since 2020/12/23 6:29 下午
 */
public class ChineseCharacterUtils {

    /**
     * 将单个汉字转成拼音
     *
     * @param chinese 汉字字符
     * @return 拼音
     */
    public static String chineseCharConversionPinyin(char chinese) {
        HanyuPinyinOutputFormat outputFormat = new HanyuPinyinOutputFormat();
        outputFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        String[] res;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            res = PinyinHelper.toHanyuPinyinStringArray(chinese, outputFormat);
            // 对于多音字，只用第一个拼音
            stringBuilder.append(res[0]);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
        return stringBuilder.toString();
    }


    /**
     * 将汉字转成拼音,取首字母或全拼
     *
     * @param chinese 汉字字符串
     * @param isFull  是否全拼
     * @return 拼音
     */
    public static String chineseConversionPinyin(String chinese, boolean isFull) {
        // ^[\u2E80-\u9FFF]+$ 匹配所有东亚区的语言
        // ^[\u4E00-\u9FFF]+$ 匹配简体和繁体
        // ^[\u4E00-\u9FA5]+$ 匹配简体
        String regExp = "^[\u4E00-\u9FFF]+$";
        StringBuilder stringBuilder = new StringBuilder();
        if (chinese == null || "".equals(chinese.trim())) {
            return "";
        }
        String pinyin = "";
        for (int i = 0; i < chinese.length(); i++) {
            char unit = chinese.charAt(i);
            // 是汉字，则转拼音
            if (match(String.valueOf(unit), regExp)) {
                pinyin = chineseCharConversionPinyin(unit);
                if (isFull) {
                    stringBuilder.append(pinyin);
                } else {
                    stringBuilder.append(pinyin.charAt(0));
                }
            } else {
                stringBuilder.append(unit);
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 根据字符和正则表达式进行匹配
     *
     * @param str   源字符串
     * @param regex 正则表达式
     * @return true：匹配成功  false：匹配失败
     */
    private static boolean match(String str, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.find();
    }

    public static void main(String[] args) {
        char sex = '男';
        String chineseCharConversionPinyin = chineseCharConversionPinyin(sex);
        // nan
        System.out.println(chineseCharConversionPinyin);

        String str = "你好,中国";
        String chineseConversionPinyin = chineseConversionPinyin(str, false);
        // nh,zg或者nihao,zhongguo
        System.out.println(chineseConversionPinyin);
    }
}
