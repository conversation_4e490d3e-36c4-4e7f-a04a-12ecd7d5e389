package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryCheck;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryCheckMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PharmacyInventoryCheckReqVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryCheckService;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 药品盘点明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Service
public class PharmacyInventoryCheckServiceImpl implements IPharmacyInventoryCheckService {
    @Autowired
    private PharmacyInventoryCheckMapper pharmacyInventoryCheckMapper;

    @Autowired
    private IPharmacyInventoryService pharmacyInventoryService;

    /**
     * 查询药品盘点明细
     *
     * @param id 药品盘点明细主键
     * @return 药品盘点明细
     */
    @Override
    public PharmacyInventoryCheck selectPharmacyInventoryCheckById(Long id) {
        return pharmacyInventoryCheckMapper.selectPharmacyInventoryCheckById(id);
    }

    /**
     * 查询药品盘点明细列表
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 药品盘点明细
     */
    @Override
    public List<PharmacyInventoryCheck> selectPharmacyInventoryCheckList(PharmacyInventoryCheck pharmacyInventoryCheck) {
        return pharmacyInventoryCheckMapper.selectPharmacyInventoryCheckList(pharmacyInventoryCheck);
    }

    /**
     * 编辑药品盘点明细
     *
     * @param pharmacyInventoryCheckReqVo 药品盘点明细
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(PharmacyInventoryCheckReqVo pharmacyInventoryCheckReqVo) {
        // 查询盘点单号是否存在
        String serialNumber = pharmacyInventoryCheckReqVo.getSerialNumber();
        PharmacyInventoryCheck params = new PharmacyInventoryCheck();
        params.setSerialNumber(serialNumber);
        List<PharmacyInventoryCheck> checks = pharmacyInventoryCheckMapper.selectPharmacyInventoryCheckList(params);
        List<PharmacyInventoryCheck> items = BeanUtil.copyToList(pharmacyInventoryCheckReqVo.getDetailList(), PharmacyInventoryCheck.class);
        items.forEach(item -> {
            BeanUtil.copyProperties(pharmacyInventoryCheckReqVo, item, "detailList");
        });
        if (CollUtil.isNotEmpty(checks)) {
            // 存在，删除老数据
            pharmacyInventoryCheckMapper.deleteBySerialNumber(serialNumber);
        }

        // 新增
        return pharmacyInventoryCheckMapper.insertBatch(items);

    }

    /**
     * 修改药品盘点明细
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 结果
     */
    @Override
    public int updatePharmacyInventoryCheck(PharmacyInventoryCheck pharmacyInventoryCheck) {
        return pharmacyInventoryCheckMapper.updatePharmacyInventoryCheck(pharmacyInventoryCheck);
    }

    /**
     * 批量删除药品盘点明细
     *
     * @param ids 需要删除的药品盘点明细主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryCheckByIds(Long[] ids) {
        return pharmacyInventoryCheckMapper.deletePharmacyInventoryCheckByIds(ids);
    }

    /**
     * 删除药品盘点明细信息
     *
     * @param id 药品盘点明细主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryCheckById(Long id) {
        return pharmacyInventoryCheckMapper.deletePharmacyInventoryCheckById(id);
    }

    @Override
    public List<PharmacyInventoryCheck> selectPharmacyInventoryCheckList2(PharmacyInventoryCheck pharmacyInventoryCheck) {
        return pharmacyInventoryCheckMapper.selectPharmacyInventoryCheckList2(pharmacyInventoryCheck);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int checksave(PharmacyInventoryCheckReqVo pharmacyInventoryCheckReqVo) {
        pharmacyInventoryCheckReqVo.getDetailList().forEach(item -> {
            PharmacyInventoryCheck params = new PharmacyInventoryCheck();
            params.setId(item.getId());
            params.setRealQuantity(item.getRealQuantity());
            params.setProfitLossQuantity(item.getRealQuantity() - item.getCheckQuantity());
            params.setStatus("2");
            // 更新盘点流水
            pharmacyInventoryCheckMapper.updatePharmacyInventoryCheck(params);
        });
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int checkFinish(PharmacyInventoryCheckReqVo pharmacyInventoryCheckReqVo) {
        pharmacyInventoryCheckReqVo.getDetailList().forEach(item -> {
            PharmacyInventoryCheck params = new PharmacyInventoryCheck();
            params.setId(item.getId());
            params.setRealQuantity(item.getRealQuantity());
            params.setProfitLossQuantity(item.getRealQuantity() - item.getCheckQuantity());
            params.setStatus("3");
            // 更新盘点流水
            pharmacyInventoryCheckMapper.updatePharmacyInventoryCheck(params);
            // 更新库存
            pharmacyInventoryService.updateQuantity(item.getInventoryId(), item.getRealQuantity());
        });
        return 1;
    }
}

