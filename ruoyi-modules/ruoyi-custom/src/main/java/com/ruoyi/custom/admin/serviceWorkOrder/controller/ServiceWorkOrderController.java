package com.ruoyi.custom.admin.serviceWorkOrder.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.custom.admin.serviceWorkOrder.domain.OrderServiceWork;
import com.ruoyi.custom.admin.serviceWorkOrder.param.ServiceWorkOrderParam;
import com.ruoyi.custom.admin.serviceWorkOrder.service.ServiceWorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_service_work_order控制器
 * @date 2022-07-18
 */
@Slf4j
@RestController
@RequestMapping("/serviceWorkOrder")
@Api(tags = "服务人员工单模块")
public class ServiceWorkOrderController extends BaseController {

    @Autowired
    private ServiceWorkOrderService serviceWorkOrderService;

    @ApiOperation("获取工单list")
    @PostMapping("/getWorkOrderList")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = true, dataTypeClass = String.class),
    })
    public TableDataInfo<OrderServiceWork> getWorkOrderList(@ApiIgnore OrderServiceWork orderServiceWork) {
        startPage();
        List<OrderServiceWork> list = serviceWorkOrderService.getWorkOrderList(orderServiceWork);
        return getDataTable(list);
    }


    /**
     * 指派服务人员并更新工单
     */
    @PostMapping("/assignWorker")
    @ApiOperation(value = "指派服务人员并更新工单")
    public Object assignWorker(@RequestBody ServiceWorkOrderParam serviceWorkOrderParam) {
        int i = serviceWorkOrderService.assignWorker(serviceWorkOrderParam);
        return toAjax(i);
    }


    /**
     * 定时生成工单任务
     */
    @GetMapping("/generateCare")
    public void generateCare() {
        serviceWorkOrderService.generateCare();
    }
}
