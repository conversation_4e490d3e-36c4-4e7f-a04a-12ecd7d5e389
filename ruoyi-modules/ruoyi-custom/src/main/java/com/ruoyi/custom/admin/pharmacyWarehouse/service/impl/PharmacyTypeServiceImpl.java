package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;


import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyType;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyTypeMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 药品类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class PharmacyTypeServiceImpl implements IPharmacyTypeService {
    @Autowired
    private PharmacyTypeMapper pharmacyTypeMapper;

    /**
     * 查询药品类型
     *
     * @param id 药品类型主键
     * @return 药品类型
     */
    @Override
    public PharmacyType selectPharmacyTypeById(Long id) {
        return pharmacyTypeMapper.selectPharmacyTypeById(id);
    }

    /**
     * 查询药品类型列表
     *
     * @param pharmacyType 药品类型
     * @return 药品类型
     */
    @Override
    public List<PharmacyType> selectPharmacyTypeList(PharmacyType pharmacyType) {
        return pharmacyTypeMapper.selectPharmacyTypeList(pharmacyType);
    }

    /**
     * 新增药品类型
     *
     * @param pharmacyType 药品类型
     * @return 结果
     */
    @Override
    public int insertPharmacyType(PharmacyType pharmacyType) {
        pharmacyType.setCreateTime(DateUtils.getNowDate());
        return pharmacyTypeMapper.insertPharmacyType(pharmacyType);
    }

    /**
     * 修改药品类型
     *
     * @param pharmacyType 药品类型
     * @return 结果
     */
    @Override
    public int updatePharmacyType(PharmacyType pharmacyType) {
        return pharmacyTypeMapper.updatePharmacyType(pharmacyType);
    }

    /**
     * 批量删除药品类型
     *
     * @param ids 需要删除的药品类型主键
     * @return 结果
     */
    @Override
    public int deletePharmacyTypeByIds(Long[] ids) {
        return pharmacyTypeMapper.deletePharmacyTypeByIds(ids);
    }

    /**
     * 删除药品类型信息
     *
     * @param id 药品类型主键
     * @return 结果
     */
    @Override
    public int deletePharmacyTypeById(Long id) {
        return pharmacyTypeMapper.deletePharmacyTypeById(id);
    }
}

