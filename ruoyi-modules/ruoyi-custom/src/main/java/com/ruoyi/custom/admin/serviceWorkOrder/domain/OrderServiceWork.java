package com.ruoyi.custom.admin.serviceWorkOrder.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description t_order_service_work
 * @date 2022-07-20
 */
@Data
@ApiModel("t_order_service_work")
@TableName("t_order_service_work")
public class OrderServiceWork extends BaseEntity {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderId;

    /**
     * 下单方式 1:用户 2：自动
     */
    @Excel(name = "下单方式", readConverterExp = "1=用户,2=自动")
    @ApiModelProperty(value = "下单方式 1:用户 2：自动")
    private Integer placeOrderWay;

    /**
     * 老人id
     */
    @ApiModelProperty(value = "老人id")
    private String elderlyPeopleId;

    /**
     * 老人姓名
     */
    @Excel(name = "老人姓名")
    @ApiModelProperty(value = "老人姓名")
    private String name;

    /**
     * 老人联系方式
     */
    @Excel(name = "老人联系方式")
    @ApiModelProperty(value = "老人联系方式")
    private String phone;

    /**
     * 工作人员id
     */
    @ApiModelProperty(value = "工作人员id")
    private Long workerId;

    /**
     * 床位id
     */
    @ApiModelProperty(value = "床位id")
    private Long bedId;

    /**
     * 居住基础表id
     */
    @ApiModelProperty(value = "居住基础表id")
    private String liveId;

    /**
     * 工单状态(1待指派人员，2未开始，3服务中，4已完成)
     */
    @Excel(name = "工单状态", readConverterExp = "1=待指派人员,2=未开始,3=服务中,4=已完成")
    @ApiModelProperty(value = "工单状态(1待指派人员，2未开始，3服务中，4已完成)")
    private Integer status;

    /**
     * 服务id
     */
    @ApiModelProperty(value = "服务id")
    private Long serviceId;

    /**
     * 服务名称
     */
    @Excel(name = "服务名称")
    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    /**
     * 预约时间&计划完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "预约时间|计划开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预约时间&计划开始时间")
    private Date reserveTime;

    /**
     * 服务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "服务开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "服务开始时间")
    private Date startTime;

    /**
     * 服务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "服务结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "服务结束时间")
    private Date endTime;

    /**
     * 开始照片
     */
    @ApiModelProperty(value = "开始照片")
    private String startImg;

    /**
     * 现场图片
     */
    @ApiModelProperty(value = "现场图片")
    private String liveImg;

    /**
     * 结束照片
     */
    @ApiModelProperty(value = "结束照片")
    private String endImg;

    /**
     * 服务时长
     */
    @Excel(name = "服务时长")
    @ApiModelProperty(value = "服务时长")
    private BigDecimal serviceTime;

    /**
     * 数量
     */
    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private Integer number;

    /**
     * 周期
     */
    @Excel(name = "周期")
    @ApiModelProperty(value = "周期")
    private String cycle;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty(value = "逻辑删除标记（0：显示；1：隐藏）")
    private String delFlag;

    // ------------------ 分界线 ------------------

    /**
     * 频次(应做的次数)
     */
    @Excel(name = "频次(应做的次数)")
    @ApiModelProperty(" 频次(应做的次数)")
    @TableField(exist = false)
    private Long frequency;

    /**
     * 老人性别
     */
    @ApiModelProperty(value = "老人性别")
    @TableField(exist = false)
    private String sex;

    /**
     * 楼栋-楼层-房间-床位
     */
    @ApiModelProperty(value = "床位，格式：楼栋-楼层-房间-床位")
    @TableField(exist = false)
    private String groupName;

}
