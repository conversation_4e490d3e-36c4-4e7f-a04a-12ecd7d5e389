package com.ruoyi.custom.admin.serviceWorkOrder.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description home_service_work_order
 * @date 2022-07-18
 */
@Data
@ApiModel("service_work_order")
public class ServiceWorkOrderRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    private String id;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderId;

    /**
     * 老人id
     */
    @ApiModelProperty("老人id")
    private String elderlyPeopleId;

    /**
     * 工作人员id
     */
    @ApiModelProperty("工作人员id")
    private Long workerId;

    /**
     * 服务人员手机号
     */
    @ApiModelProperty("服务人员手机号")
    private String workerPhone;

    /**
     * 工单状态
     */
    @ApiModelProperty("工单状态")
    private int status;

    /**
     * 预约时间
     */
    @ApiModelProperty("预约时间")
    private Date reserveTime;

    /**
     * 服务id
     */
    @ApiModelProperty("服务id")
    private Long serviceId;

    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    private String serviceName;

    /**
     * 服务开始时间
     */
    @ApiModelProperty("服务开始时间")
    private Date startTime;

    /**
     * 开始照片
     */
    @ApiModelProperty("开始照片")
    private String startImg;

    /**
     * 现场图片
     */
    @ApiModelProperty("现场图片")
    private String liveImg;

    /**
     * 服务结束时间
     */
    @ApiModelProperty("服务结束时间")
    private Date endTime;

    /**
     * 结束照片
     */
    @ApiModelProperty("结束照片")
    private String endImg;

    /**
     * 服务时长
     */
    @ApiModelProperty("服务时长")
    private Integer serviceTime;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    private Date createTime;

    /**
     * 创建人员
     */
    @ApiModelProperty("创建人员")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 修改人员
     */
    @ApiModelProperty("修改人员")
    private String updateBy;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty("逻辑删除标记（0：显示；1：隐藏")
    private String delFlag;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    public ServiceWorkOrderRequestVo() {
    }
}
