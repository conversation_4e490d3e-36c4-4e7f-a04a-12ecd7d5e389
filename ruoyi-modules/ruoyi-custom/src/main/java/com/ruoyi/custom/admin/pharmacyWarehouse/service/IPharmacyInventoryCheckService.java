package com.ruoyi.custom.admin.pharmacyWarehouse.service;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryCheck;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PharmacyInventoryCheckReqVo;

import java.util.List;


/**
 * 药品盘点明细Service接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface IPharmacyInventoryCheckService {
    /**
     * 查询药品盘点明细
     *
     * @param id 药品盘点明细主键
     * @return 药品盘点明细
     */
    public PharmacyInventoryCheck selectPharmacyInventoryCheckById(Long id);

    /**
     * 查询药品盘点明细列表
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 药品盘点明细集合
     */
    public List<PharmacyInventoryCheck> selectPharmacyInventoryCheckList(PharmacyInventoryCheck pharmacyInventoryCheck);

    /**
     * 新增/编辑药品盘点明细
     *
     * @param pharmacyInventoryChecks 药品盘点明细
     * @return 结果
     */
    public int save(PharmacyInventoryCheckReqVo pharmacyInventoryChecks);

    /**
     * 修改药品盘点明细
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 结果
     */
    public int updatePharmacyInventoryCheck(PharmacyInventoryCheck pharmacyInventoryCheck);

    /**
     * 批量删除药品盘点明细
     *
     * @param ids 需要删除的药品盘点明细主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryCheckByIds(Long[] ids);

    /**
     * 删除药品盘点明细信息
     *
     * @param id 药品盘点明细主键
     * @return 结果
     */
    public int deletePharmacyInventoryCheckById(Long id);

    /**
     * 查询药品盘点明细列表
     *
     * @param pharmacyInventoryCheck 药品盘点明细
     * @return 药品盘点明细集合
     */
    List<PharmacyInventoryCheck> selectPharmacyInventoryCheckList2(PharmacyInventoryCheck pharmacyInventoryCheck);

    /**
     * 盘点保存
     *
     * @param pharmacyInventoryCheckReqVo 药品盘点明细
     * @return 结果
     */
    int checksave(PharmacyInventoryCheckReqVo pharmacyInventoryCheckReqVo);

    /**
     * 盘点完成
     *
     * @param pharmacyInventoryCheckReqVo 药品盘点明细
     * @return 结果
     */
    int checkFinish(PharmacyInventoryCheckReqVo pharmacyInventoryCheckReqVo);
}

