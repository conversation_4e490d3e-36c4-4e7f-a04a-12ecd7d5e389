package com.ruoyi.custom.admin.medicalInspection.mapper;

import com.ruoyi.custom.admin.medicalInspection.domain.MedicalInspectionRecord;

import java.util.List;


/**
 * 医护巡查记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface MedicalInspectionRecordMapper {
    /**
     * 查询医护巡查记录
     *
     * @param id 医护巡查记录主键
     * @return 医护巡查记录
     */
    public MedicalInspectionRecord selectMedicalInspectionRecordById(Long id);

    /**
     * 查询医护巡查记录列表
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 医护巡查记录集合
     */
    public List<MedicalInspectionRecord> selectMedicalInspectionRecordList(MedicalInspectionRecord medicalInspectionRecord);

    /**
     * 新增医护巡查记录
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 结果
     */
    public int insertMedicalInspectionRecord(MedicalInspectionRecord medicalInspectionRecord);

    /**
     * 修改医护巡查记录
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 结果
     */
    public int updateMedicalInspectionRecord(MedicalInspectionRecord medicalInspectionRecord);

    /**
     * 删除医护巡查记录
     *
     * @param id 医护巡查记录主键
     * @return 结果
     */
    public int deleteMedicalInspectionRecordById(Long id);

    /**
     * 批量删除医护巡查记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMedicalInspectionRecordByIds(Long[] ids);
}

