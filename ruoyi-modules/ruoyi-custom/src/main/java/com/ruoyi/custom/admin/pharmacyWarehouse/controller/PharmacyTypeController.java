package com.ruoyi.custom.admin.pharmacyWarehouse.controller;


import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyType;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 药品类型Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/pharmacy/type")
@Api(tags = "药品类型")
public class PharmacyTypeController extends BaseController {
    @Autowired
    private IPharmacyTypeService pharmacyTypeService;

    /**
     * 查询药品类型列表
     */
    // @RequiresPermissions("custom:type:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(PharmacyType pharmacyType) {
        startPage();
        List<PharmacyType> list = pharmacyTypeService.selectPharmacyTypeList(pharmacyType);
        return getDataTable(list);
    }

    /**
     * 导出药品类型列表
     */
    // @RequiresPermissions("custom:type:export")
    @Log(title = "药品类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PharmacyType pharmacyType) {
        List<PharmacyType> list = pharmacyTypeService.selectPharmacyTypeList(pharmacyType);
        ExcelUtil<PharmacyType> util = new ExcelUtil<PharmacyType>(PharmacyType.class);
        util.exportExcel(response, list, "药品类型数据");
    }

    /**
     * 获取药品类型详细信息
     */
    // @RequiresPermissions("custom:type:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取详情")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(pharmacyTypeService.selectPharmacyTypeById(id));
    }

    /**
     * 新增药品类型
     */
    // @RequiresPermissions("custom:type:add")
    @Log(title = "药品类型", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增")
    public AjaxResult add(@RequestBody PharmacyType pharmacyType) {
        return toAjax(pharmacyTypeService.insertPharmacyType(pharmacyType));
    }

    /**
     * 修改药品类型
     */
    // @RequiresPermissions("custom:type:edit")
    @Log(title = "药品类型", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改")
    public AjaxResult edit(@RequestBody PharmacyType pharmacyType) {
        return toAjax(pharmacyTypeService.updatePharmacyType(pharmacyType));
    }

    /**
     * 删除药品类型
     */
    // @RequiresPermissions("custom:type:remove")
    @Log(title = "药品类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(pharmacyTypeService.deletePharmacyTypeByIds(ids));
    }
}

