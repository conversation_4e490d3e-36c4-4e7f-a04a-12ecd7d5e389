package com.ruoyi.custom.admin.pharmacyWarehouse.service;


import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryOut;

import java.util.List;


/**
 * 药品出库明细Service接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface IPharmacyInventoryOutService {
    /**
     * 查询药品出库明细
     *
     * @param id 药品出库明细主键
     * @return 药品出库明细
     */
    public PharmacyInventoryOut selectPharmacyInventoryOutById(Long id);

    /**
     * 查询药品出库明细列表
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 药品出库明细集合
     */
    public List<PharmacyInventoryOut> selectPharmacyInventoryOutList(PharmacyInventoryOut pharmacyInventoryOut);

    /**
     * 新增药品出库明细
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 结果
     */
    public int insertPharmacyInventoryOut(List<PharmacyInventoryOut> pharmacyInventoryOut);

    /**
     * 修改药品出库明细
     *
     * @param pharmacyInventoryOut 药品出库明细
     * @return 结果
     */
    public int updatePharmacyInventoryOut(PharmacyInventoryOut pharmacyInventoryOut);

    /**
     * 批量删除药品出库明细
     *
     * @param ids 需要删除的药品出库明细主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryOutByIds(Long[] ids);

    /**
     * 删除药品出库明细信息
     *
     * @param id 药品出库明细主键
     * @return 结果
     */
    public int deletePharmacyInventoryOutById(Long id);

    /**
     * 查询药品出库明细列表2
     * @param pharmacyInventoryOut
     * @return
     */
    List<PharmacyInventoryOut> selectPharmacyInventoryOutList2(PharmacyInventoryOut pharmacyInventoryOut);
}

