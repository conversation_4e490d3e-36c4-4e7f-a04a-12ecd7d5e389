package com.ruoyi.custom.admin.storiedBuilding.controller;

import java.util.*;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.storiedBuilding.domain.BedCareInfo;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedCareInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 护工分配Controller
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@RestController
@RequestMapping("/bedCareInfo")
@Api(value = "护工分配", tags = "护工分配")
public class BedCareInfoController extends BaseController {
    @Autowired
    private IBedCareInfoService bedCareInfoService;

    /**
     * 查询床位和护工关联信息列表
     */
    //@RequiresPermissions("storiedBuilding:bedCareInfo:list")
    @ApiOperation(value = "查询床位和护工关联信息列表")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(dataTypeClass = String.class, name = "careWorkerId", value = "护工id", paramType = "query", required = true),
    })
    public TableDataInfo<BedCareInfo> list(@ApiIgnore BedCareInfo bedCareInfo) {
        startPage();
        List<BedCareInfo> list = bedCareInfoService.selectBedCareInfoList(bedCareInfo);
        return getDataTable(list);
    }

    /**
     * 导出床位和护工关联信息列表
     */
    //@RequiresPermissions("storiedBuilding:bedCareInfo:export")
    @Log(platform = "1", title = "床位和护工关联信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出床位和护工关联信息列表")
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, BedCareInfo bedCareInfo) {
        List<BedCareInfo> list = bedCareInfoService.selectBedCareInfoList(bedCareInfo);
        ExcelUtil<BedCareInfo> util = new ExcelUtil<BedCareInfo>(BedCareInfo.class);
        util.exportExcel(response, list, "床位和护工关联信息数据");
    }

    /**
     * 获取床位和护工关联信息详细信息
     */
    //@RequiresPermissions("storiedBuilding:bedCareInfo:query")
    @ApiOperation(value = "获取床位和护工关联信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bedCareInfoService.selectBedCareInfoById(id));
    }


    /**
     * 保存床位和护工关联信息
     */
    @ApiOperation(value = "保存床位和护工关联信息")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody BedCareInfo bedCareInfo) {
        if (null == bedCareInfo.getId() || bedCareInfo.getId() == 0) {
            return toAjax(bedCareInfoService.insertBedCareInfo(bedCareInfo));
        } else {
            return toAjax(bedCareInfoService.updateBedCareInfo(bedCareInfo));
        }
    }


    /**
     * 新增床位和护工关联信息
     */
    //@RequiresPermissions("storiedBuilding:bedCareInfo:add")
    @Log(platform = "1", title = "床位和护工关联信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增床位和护工关联信息")
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody BedCareInfo bedCareInfo) {
        return toAjax(bedCareInfoService.insertBedCareInfo(bedCareInfo));
    }

    /**
     * 修改床位和护工关联信息
     */
    //@RequiresPermissions("storiedBuilding:bedCareInfo:edit")
    @Log(platform = "1", title = "床位和护工关联信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改床位和护工关联信息")
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody BedCareInfo bedCareInfo) {
        return toAjax(bedCareInfoService.updateBedCareInfo(bedCareInfo));
    }

    /**
     * 删除床位和护工关联信息
     */
    //@RequiresPermissions("storiedBuilding:bedCareInfo:remove")
    @Log(platform = "1", title = "床位和护工关联信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除床位和护工关联信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(bedCareInfoService.deleteBedCareInfoByIds(ids));
    }

    @ApiOperation(value = "护工分配树状保存")
    @PostMapping("/saveBedCareInfo")
    public AjaxResult saveBedCareInfo(@RequestBody JSONObject data) {
        String beds = data.getStr("beds");
        String careWorkerId = data.getStr("careWorkerId");
        if (null == beds || beds.isEmpty() || "[]".equals(beds)) {
            String[] str = new String[0];
            return toAjax(bedCareInfoService.saveBedCareInfo(str, careWorkerId));
        } else {
            String substring = beds.substring(1, beds.length() - 1);
            String[] split = substring.split(",");
            return toAjax(bedCareInfoService.saveBedCareInfo(split, careWorkerId));
        }
    }

    @ApiOperation(value = "解绑功能")
    @GetMapping("/updateCareBed")
    public AjaxResult updateCareBed(Long id) {
        BedCareInfo bedCareInfo = bedCareInfoService.selectBedCareInfoById(id);
        bedCareInfo.setCareState("1");
        bedCareInfo.setDelFlag("1");
        bedCareInfo.setEndDate(new Date());
        return toAjax(bedCareInfoService.updateBedCareInfo(bedCareInfo));
    }

    @ApiOperation(value = "根据护工回显选中床位")
    @GetMapping("/getCareBed")
    public AjaxResult getCareBed(Long id) {
        String groupCareBed = bedCareInfoService.getGroupCareBed(String.valueOf(id));
        if (null == groupCareBed || groupCareBed.isEmpty()) {
            String[] str = new String[0];
            return AjaxResult.success().put("data", str);
        } else {
            String[] split = groupCareBed.split(",");
            return AjaxResult.success().put("data", split);
        }
    }


}
