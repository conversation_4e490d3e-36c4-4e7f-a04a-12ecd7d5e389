package com.ruoyi.custom.admin.medicalInspection.service;

import com.ruoyi.custom.admin.medicalInspection.domain.MedicalInspectionRecord;

import java.util.List;

/**
 * 医护巡查记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IMedicalInspectionRecordService {
    /**
     * 查询医护巡查记录
     *
     * @param id 医护巡查记录主键
     * @return 医护巡查记录
     */
    public MedicalInspectionRecord selectMedicalInspectionRecordById(Long id);

    /**
     * 查询医护巡查记录列表
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 医护巡查记录集合
     */
    public List<MedicalInspectionRecord> selectMedicalInspectionRecordList(MedicalInspectionRecord medicalInspectionRecord);

    /**
     * 新增医护巡查记录
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 结果
     */
    public int insertMedicalInspectionRecord(MedicalInspectionRecord medicalInspectionRecord);

    /**
     * 修改医护巡查记录
     *
     * @param medicalInspectionRecord 医护巡查记录
     * @return 结果
     */
    public int updateMedicalInspectionRecord(MedicalInspectionRecord medicalInspectionRecord);

    /**
     * 批量删除医护巡查记录
     *
     * @param ids 需要删除的医护巡查记录主键集合
     * @return 结果
     */
    public int deleteMedicalInspectionRecordByIds(Long[] ids);

    /**
     * 删除医护巡查记录信息
     *
     * @param id 医护巡查记录主键
     * @return 结果
     */
    public int deleteMedicalInspectionRecordById(Long id);
}

