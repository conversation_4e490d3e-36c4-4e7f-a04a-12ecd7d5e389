package com.ruoyi.custom.admin.medicalInspection.controller;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.medicalInspection.domain.MedicalInspectionInfo;
import com.ruoyi.custom.admin.medicalInspection.service.IMedicalInspectionInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 医护巡查信息Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/medicalInspection/info")
@Api(tags = "医护巡查信息")
public class MedicalInspectionInfoController extends BaseController {
    @Autowired
    private IMedicalInspectionInfoService medicalInspectionInfoService;

    /**
     * 查询医护巡查信息列表
     */
    // @RequiresPermissions("custom:info:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(MedicalInspectionInfo medicalInspectionInfo) {
        startPage();
        List<MedicalInspectionInfo> list = medicalInspectionInfoService.selectMedicalInspectionInfoList(medicalInspectionInfo);
        return getDataTable(list);
    }

    /**
     * 获取医护巡查信息详细信息
     */
    // @RequiresPermissions("custom:info:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取详情")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(medicalInspectionInfoService.selectMedicalInspectionInfoById(id));
    }

    /**
     * 新增医护巡查信息
     */
    // @RequiresPermissions("custom:info:add")
    @Log(title = "医护巡查信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增")
    public AjaxResult add(@RequestBody MedicalInspectionInfo medicalInspectionInfo) {
        if (CollUtil.isEmpty(medicalInspectionInfo.getAssignPersonnels())) {
            return AjaxResult.error("请选择分配人员");
        }
        return toAjax(medicalInspectionInfoService.insertMedicalInspectionInfo(medicalInspectionInfo));
    }

    /**
     * 修改医护巡查信息
     */
    // @RequiresPermissions("custom:info:edit")
    @Log(title = "医护巡查信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改")
    public AjaxResult edit(@RequestBody MedicalInspectionInfo medicalInspectionInfo) {
        if (CollUtil.isEmpty(medicalInspectionInfo.getAssignPersonnels())) {
            return AjaxResult.error("请选择分配人员");
        }
        return toAjax(medicalInspectionInfoService.updateMedicalInspectionInfo(medicalInspectionInfo));
    }

    /**
     * 删除医护巡查信息
     */
    // @RequiresPermissions("custom:info:remove")
    @Log(title = "医护巡查信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(medicalInspectionInfoService.deleteMedicalInspectionInfoByIds(ids));
    }

    // /**
    //  * 导出医护巡查信息列表
    //  */
    // @RequiresPermissions("custom:info:export")
    // @Log(title = "医护巡查信息", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, MedicalInspectionInfo medicalInspectionInfo) {
    //     List<MedicalInspectionInfo> list = medicalInspectionInfoService.selectMedicalInspectionInfoList(medicalInspectionInfo);
    //     ExcelUtil<MedicalInspectionInfo> util = new ExcelUtil<MedicalInspectionInfo>(MedicalInspectionInfo. class);
    //     util.exportExcel(response, list, "医护巡查信息数据");
    // }
}

