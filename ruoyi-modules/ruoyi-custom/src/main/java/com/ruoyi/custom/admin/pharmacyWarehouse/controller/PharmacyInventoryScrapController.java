package com.ruoyi.custom.admin.pharmacyWarehouse.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryScrap;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PharmacyInventoryScrapReqVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.resp.PharmacyInventoryScrapRespVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryScrapService;
import com.ruoyi.custom.utils.OrderUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 药品报废明细Controller
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@RestController
@RequestMapping("/pharmacy/inventory/scrap")
@Api(tags = "药品报废明细")
public class PharmacyInventoryScrapController extends BaseController {
    @Autowired
    private IPharmacyInventoryScrapService pharmacyInventoryScrapService;

    /**
     * 查询药品报废明细列表
     */
    // @RequiresPermissions("custom:scrap:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(PharmacyInventoryScrap pharmacyInventoryScrap) {
        startPage();
        List<PharmacyInventoryScrap> list = pharmacyInventoryScrapService.selectPharmacyInventoryScrapList2(pharmacyInventoryScrap);
        return getDataTable(list);
    }

    /**
     * 详情列表
     */
    @GetMapping("/subList")
    @ApiOperation(value = "获取详情列表")
    public TableDataInfo<PharmacyInventoryScrapRespVo> subList(@ApiParam(value = "编号", required = true) @RequestParam String serialNumber) {
        startPage();
        PharmacyInventoryScrap base = new PharmacyInventoryScrap();
        base.setSerialNumber(serialNumber);
        List<PharmacyInventoryScrap> list = pharmacyInventoryScrapService.selectPharmacyInventoryScrapList(base);
        if (CollUtil.isEmpty(list)) {
            return getDataTable(list);
        }
        List<PharmacyInventoryScrapRespVo.Detail> detailList = BeanUtil.copyToList(list, PharmacyInventoryScrapRespVo.Detail.class);
        PharmacyInventoryScrapRespVo respVo = new PharmacyInventoryScrapRespVo();
        BeanUtil.copyProperties(list.get(0), respVo, "detailList");
        respVo.setDetailList(detailList);
        return getDataTable(list);
    }

    /**
     * 报废
     */
    // @RequiresPermissions("custom:scrap:add")
    @Log(title = "报废", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "报废")
    public AjaxResult add(@RequestBody PharmacyInventoryScrapReqVo pharmacyInventoryScrapReqVo) {
        List<PharmacyInventoryScrap> items = BeanUtil.copyToList(pharmacyInventoryScrapReqVo.getDetailList(), PharmacyInventoryScrap.class);
        items.forEach(item -> {
            BeanUtil.copyProperties(pharmacyInventoryScrapReqVo, item, "detailList");
        });
        return toAjax(pharmacyInventoryScrapService.insertPharmacyInventoryScrap(items));
    }

    /**
     * 生成报废编号
     */
    @GetMapping("/generateSerialNumber")
    @ApiOperation(value = "生成报废编号")
    public AjaxResult generateSerialNumber() {
        return AjaxResult.success("操作成功", OrderUtils.getScrapInventoryCode());
    }

    // /**
    //  * 导出药品报废明细列表
    //  */
    // // @RequiresPermissions("custom:scrap:export")
    // @Log(title = "药品报废明细", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, PharmacyInventoryScrap pharmacyInventoryScrap) {
    //     List<PharmacyInventoryScrap> list = pharmacyInventoryScrapService.selectPharmacyInventoryScrapList(pharmacyInventoryScrap);
    //     ExcelUtil<PharmacyInventoryScrap> util = new ExcelUtil<PharmacyInventoryScrap>(PharmacyInventoryScrap. class);
    //     util.exportExcel(response, list, "药品报废明细数据");
    // }
    //
    // /**
    //  * 获取药品报废明细详细信息
    //  */
    // // @RequiresPermissions("custom:scrap:query")
    // @GetMapping(value = "/{id}")
    // public AjaxResult getInfo(@PathVariable("id") Long id) {
    //     return AjaxResult.success(pharmacyInventoryScrapService.selectPharmacyInventoryScrapById(id));
    // }

    // /**
    //  * 修改药品报废明细
    //  */
    // // @RequiresPermissions("custom:scrap:edit")
    // @Log(title = "药品报废明细", businessType = BusinessType.UPDATE)
    // @PutMapping
    // public AjaxResult edit(@RequestBody PharmacyInventoryScrap pharmacyInventoryScrap) {
    //     return toAjax(pharmacyInventoryScrapService.updatePharmacyInventoryScrap(pharmacyInventoryScrap));
    // }
    //
    // /**
    //  * 删除药品报废明细
    //  */
    // // @RequiresPermissions("custom:scrap:remove")
    // @Log(title = "药品报废明细", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{ids}")
    // public AjaxResult remove(@PathVariable Long[] ids) {
    //     return toAjax(pharmacyInventoryScrapService.deletePharmacyInventoryScrapByIds(ids));
    // }
}

