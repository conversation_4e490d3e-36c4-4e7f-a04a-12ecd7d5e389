package com.ruoyi.custom.config.mybatis.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * MyBatis TypeHandler for converting between Long[] and comma-separated VARCHAR.
 */
@MappedJdbcTypes(JdbcType.VARCHAR) // 对应的 JDBC 类型
@MappedTypes(Long[].class)         // 对应的 Java 类型
public class LongArrayTypeHandler extends BaseTypeHandler<Long[]> {

    private static final String SEPARATOR = ",";

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Long[] parameter, JdbcType jdbcType) throws SQLException {
        // 将 Long[] 转换为逗号分隔的字符串
        // 使用 Stream API 更简洁
        String joinedString = Arrays.stream(parameter)
                .map(String::valueOf)
                .collect(Collectors.joining(SEPARATOR));
        // 或者传统方式：
        // StringBuilder sb = new StringBuilder();
        // for (int j = 0; j < parameter.length; j++) {
        //     sb.append(parameter[j]);
        //     if (j < parameter.length - 1) {
        //         sb.append(SEPARATOR);
        //     }
        // }
        // ps.setString(i, sb.toString());
        ps.setString(i, joinedString);
    }

    @Override
    public Long[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String dbValue = rs.getString(columnName);
        return parseStringToArray(dbValue);
    }

    @Override
    public Long[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String dbValue = rs.getString(columnIndex);
        return parseStringToArray(dbValue);
    }

    @Override
    public Long[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String dbValue = cs.getString(columnIndex);
        return parseStringToArray(dbValue);
    }

    /**
     * Parses the comma-separated string from DB into a Long[].
     *
     * @param dbValue The string value from the database.
     * @return Array of Longs, or null/empty array if the input is null or empty.
     */
    private Long[] parseStringToArray(String dbValue) {
        if (dbValue == null || dbValue.trim().isEmpty()) {
            // 返回 null 或空数组，取决于你的业务需求和字段定义
            return null; // 或者 return new Long[0];
        }
        try {
            // 使用 Stream API
            return Arrays.stream(dbValue.split(SEPARATOR))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .toArray(Long[]::new); // 关键：转换为 Long[]
            // 或者传统方式：
            // String[] parts = dbValue.split(SEPARATOR);
            // Long[] result = new Long[parts.length]; // 可能需要先过滤空字符串再确定大小
            // int index = 0;
            // for (String part : parts) {
            //     String trimmed = part.trim();
            //     if (!trimmed.isEmpty()) {
            //          result[index++] = Long.parseLong(trimmed);
            //     }
            // }
            // // 如果过滤了空字符串，需要调整数组大小
            // return Arrays.copyOf(result, index);

        } catch (NumberFormatException e) {
            System.err.println("Error parsing comma-separated Long array from DB: " + dbValue + " - " + e.getMessage());
            return null; // 或者 return new Long[0]; 或抛出异常
        }
    }
}
