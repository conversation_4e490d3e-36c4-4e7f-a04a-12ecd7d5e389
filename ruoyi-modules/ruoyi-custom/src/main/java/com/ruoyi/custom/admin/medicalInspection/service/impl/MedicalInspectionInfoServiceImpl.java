package com.ruoyi.custom.admin.medicalInspection.service.impl;


import cn.hutool.core.util.StrUtil;
import com.ruoyi.custom.admin.medicalInspection.domain.MedicalInspectionInfo;
import com.ruoyi.custom.admin.medicalInspection.mapper.MedicalInspectionInfoMapper;
import com.ruoyi.custom.admin.medicalInspection.service.IMedicalInspectionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 医护巡查信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class MedicalInspectionInfoServiceImpl implements IMedicalInspectionInfoService {
    @Autowired
    private MedicalInspectionInfoMapper medicalInspectionInfoMapper;

    /**
     * 查询医护巡查信息
     *
     * @param id 医护巡查信息主键
     * @return 医护巡查信息
     */
    @Override
    public MedicalInspectionInfo selectMedicalInspectionInfoById(Long id) {
        return medicalInspectionInfoMapper.selectMedicalInspectionInfoById(id);
    }

    /**
     * 查询医护巡查信息列表
     *
     * @param medicalInspectionInfo 医护巡查信息
     * @return 医护巡查信息
     */
    @Override
    public List<MedicalInspectionInfo> selectMedicalInspectionInfoList(MedicalInspectionInfo medicalInspectionInfo) {
        return medicalInspectionInfoMapper.selectMedicalInspectionInfoList(medicalInspectionInfo);
    }

    /**
     * 新增医护巡查信息
     *
     * @param medicalInspectionInfo 医护巡查信息
     * @return 结果
     */
    @Override
    public int insertMedicalInspectionInfo(MedicalInspectionInfo medicalInspectionInfo) {
        dealAssignPerson(medicalInspectionInfo);
        return medicalInspectionInfoMapper.insertMedicalInspectionInfo(medicalInspectionInfo);
    }

    /**
     * 修改医护巡查信息
     *
     * @param medicalInspectionInfo 医护巡查信息
     * @return 结果
     */
    @Override
    public int updateMedicalInspectionInfo(MedicalInspectionInfo medicalInspectionInfo) {
        dealAssignPerson(medicalInspectionInfo);
        return medicalInspectionInfoMapper.updateMedicalInspectionInfo(medicalInspectionInfo);
    }

    private void dealAssignPerson(MedicalInspectionInfo medicalInspectionInfo) {
        medicalInspectionInfo.getAssignPersonnels().forEach(assignPersonnel -> {
            medicalInspectionInfo.setAssignedPersonnelIds(StrUtil.isBlank(medicalInspectionInfo.getAssignedPersonnelIds()) ?
                    String.valueOf(assignPersonnel.getId()) : medicalInspectionInfo.getAssignedPersonnelIds() + "," + assignPersonnel.getId());
            medicalInspectionInfo.setAssignedPersonnelNames(StrUtil.isBlank(medicalInspectionInfo.getAssignedPersonnelNames()) ?
                    assignPersonnel.getName() : medicalInspectionInfo.getAssignedPersonnelNames() + "," + assignPersonnel.getName());
        });
    }

    /**
     * 批量删除医护巡查信息
     *
     * @param ids 需要删除的医护巡查信息主键
     * @return 结果
     */
    @Override
    public int deleteMedicalInspectionInfoByIds(Long[] ids) {
        return medicalInspectionInfoMapper.deleteMedicalInspectionInfoByIds(ids);
    }

    /**
     * 删除医护巡查信息信息
     *
     * @param id 医护巡查信息主键
     * @return 结果
     */
    @Override
    public int deleteMedicalInspectionInfoById(Long id) {
        return medicalInspectionInfoMapper.deleteMedicalInspectionInfoById(id);
    }
}

