package com.ruoyi.custom.utils;

import org.apache.poi.ss.formula.functions.T;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

public class ReflectionUtils {

    /**
     * 将实体类中的String类型属性为null的置为""
     *
     * @param o
     * @return
     */
    public static Object nullifyStrings(Object o) {
        Field[] declaredFields = o.getClass().getDeclaredFields();
        for (Field f : declaredFields) {
            f.setAccessible(true);
            String name = f.getName();
            if ("serialVersionUID".equals(name)) {
                continue;
            }
            // 获取属性类型
            Class type = f.getType();
            try {
                // 只操作String类型
                if (type.equals(String.class)) {
                    String value = (String) f.get(o);
                    // 如果为空
                    if (value == null || value.trim().isEmpty()) {
                        // 获取属性的set方法
                        Method method = o.getClass().getMethod("set" + name.replaceFirst(name.substring(0, 1), name.substring(0, 1).toUpperCase()), type);
//                        f.set(o, null);
                        // 将值设为空串
                        method.invoke(o, "");
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return o;
    }


    /**
     * 含递归
     * 将实体类中的 String类型或对象 属性为null的置为""或空对象
     *
     * @param o
     * @return
     */
    public static Object nullifyObjectOrStrings(Object o) throws ClassNotFoundException {
        Field[] declaredFields = o.getClass().getDeclaredFields();
        for (Field f : declaredFields) {
            f.setAccessible(true);
            String name = f.getName();
            if ("serialVersionUID".equals(name)) {
                continue;
            }

            // 获取属性类型
            Class type = f.getType();
            try {
                // 获取属性的set方法
                String setterMethod = "set" + name.replaceFirst(name.substring(0, 1), name.substring(0, 1).toUpperCase());
                Method method = o.getClass().getMethod(setterMethod, type);
                // 只操作String类型
                if (type.equals(String.class)) {
                    String value = (String) f.get(o);
                    // 如果为空
                    if (value == null || value.trim().isEmpty()) {
//                        f.set(o, null);
                        // 将值设为空串
                        method.invoke(o, "");
                    }
                } else {
                    Class<?> aClass = Class.forName(f.getGenericType().getTypeName());
                    Object createObj = aClass.newInstance();
                    // 实体赋值
                    method.invoke(o, createObj);
                    nullifyObjectOrStrings(createObj);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return o;
    }


    public static void main(String[] args) {
        System.out.println(getPreliminaryAssessment(3, 2, 3, 1));
        System.out.println(1111);

    }


    /**
     * 老年人评估报告逻辑运算
     *
     * @param rchdValue 日常生活活动级别值
     * @param jsztValue 精神状态级别值
     * @param gzValue   感知觉与沟通级别值
     * @param shcyValue 社会参与级别值
     * @return
     */
    private static String getPreliminaryAssessment(int rchdValue, int jsztValue, int gzValue, int shcyValue) {
        String label = "";
        if (rchdValue < 3) {
            if (rchdValue == 0) {// 日常生活活动的分级为0
                if (jsztValue == 0 && gzValue == 0 && shcyValue < 2) {// 日常生活活动、精神状态、感知觉与沟通分级均为0，社会参与的分级为0或1
                    label = "能力完好";
                } else if (jsztValue <= 2 || gzValue <= 2 || shcyValue == 2) {// 日常生活活动分级为0，但精神状态、感知觉与沟通中至少一项分级为1或2，或社会参与的分级为2:
                    label = "轻度受损";
                } else if (jsztValue == 3 || gzValue == 3 || shcyValue == 3) {//
                    label = "轻度受损";
                }
            } else if (rchdValue == 1) {// 日常生活活动的分级为1
                if (jsztValue < 2 || gzValue < 2 || shcyValue < 2) {// 日常生活活动分级为1，精神状态、感知觉与沟通、社会参与中至少有一项的分级为0或1
                    label = "轻度受损";
                } else if (jsztValue == 2 && gzValue == 2 && shcyValue == 2 || jsztValue == 3 || gzValue == 3 || shcyValue == 3) {// 日常生活活动分级为1，但精神状态、感知觉与沟通、社会参与均为2，或有一项为3
                    label = "中度受损";
                }
            } else if (rchdValue == 2) {// 日常生活活动的分级为2
                if (jsztValue == 2 && gzValue == 2 && shcyValue == 2) {// 日常生活活动、精神状态、感知觉与沟通、社会参与分级均为2:
                    label = "重度失能";
                } else if (jsztValue < 3 || gzValue < 3 || shcyValue < 3) {// 日常生活活动分级为2，且精神状态、感知觉与沟通、社会参与中有1-2项的分级为1或2
                    label = "中度受损";
                } else if (jsztValue == 3 || gzValue == 3 || shcyValue == 3) {// 日常生活活动分级为2，且精神状态、感知觉与沟通、社会参与中至少有一项分级为3
                    label = "重度失能";
                }
            }

        } else {// 日常生活活动的分级为3
            label = "重度失能";
        }
        return label;
    }
}
