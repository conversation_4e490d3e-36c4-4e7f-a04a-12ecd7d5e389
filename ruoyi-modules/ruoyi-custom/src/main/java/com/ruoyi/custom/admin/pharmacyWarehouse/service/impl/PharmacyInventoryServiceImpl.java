package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;


import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventory;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyManagement;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 药品库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class PharmacyInventoryServiceImpl implements IPharmacyInventoryService {
    @Autowired
    private PharmacyInventoryMapper pharmacyInventoryMapper;

    /**
     * 查询药品库存
     *
     * @param id 药品库存主键
     * @return 药品库存
     */
    @Override
    public PharmacyInventory selectPharmacyInventoryById(Long id) {
        return pharmacyInventoryMapper.selectPharmacyInventoryById(id);
    }

    @Override
    public PharmacyInventory selectPharmacyInventoryByUnionId(Long warehouseId, Long pharmacyId, Integer batch) {
        return pharmacyInventoryMapper.selectPharmacyInventoryByUnionId(warehouseId, pharmacyId, batch);
    }

    /**
     * 查询药品库存列表
     *
     * @param pharmacyInventory 药品库存
     * @return 药品库存
     */
    @Override
    public List<PharmacyInventory> selectPharmacyInventoryList(PharmacyInventory pharmacyInventory) {
        return pharmacyInventoryMapper.selectPharmacyInventoryList(pharmacyInventory);
    }

    /**
     * 新增药品库存
     *
     * @param pharmacyInventory 药品库存
     * @return 结果
     */
    @Override
    public int insertPharmacyInventory(PharmacyInventory pharmacyInventory) {
        return pharmacyInventoryMapper.insertPharmacyInventory(pharmacyInventory);
    }

    /**
     * 修改药品库存
     *
     * @param pharmacyInventory 药品库存
     * @return 结果
     */
    @Override
    public int updatePharmacyInventory(PharmacyInventory pharmacyInventory) {
        return pharmacyInventoryMapper.updatePharmacyInventory(pharmacyInventory);
    }

    /**
     * 批量删除药品库存
     *
     * @param ids 需要删除的药品库存主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryByIds(Long[] ids) {
        return pharmacyInventoryMapper.deletePharmacyInventoryByIds(ids);
    }

    /**
     * 删除药品库存信息
     *
     * @param id 药品库存主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryById(Long id) {
        return pharmacyInventoryMapper.deletePharmacyInventoryById(id);
    }

    @Override
    public List<PharmacyInventory> selectPharmacyInventoryList2(PharmacyInventory pharmacyInventory) {
        return pharmacyInventoryMapper.selectPharmacyInventoryList2(pharmacyInventory);
    }

    @Override
    public void updateQuantity(Long id, Integer quantity) {
        pharmacyInventoryMapper.updateQuantity(id, quantity);
    }

    @Override
    public List<PharmacyInventory> getWarehouseListInInventory() {
        return pharmacyInventoryMapper.getWarehouseListInInventory();
    }

    @Override
    public List<PharmacyManagement> getPharmacyListInInventory(Long warehouseId) {
        return pharmacyInventoryMapper.getPharmacyListInInventory(warehouseId);
    }

    @Override
    public List<Integer> getBatchList(Long warehouseId, Long pharmacyId) {
        return pharmacyInventoryMapper.getBatchList(warehouseId, pharmacyId);
    }

    @Override
    public int getCurrQuantityById(Long inventoryId) {
        return pharmacyInventoryMapper.getCurrQuantityById(inventoryId);
    }
}

