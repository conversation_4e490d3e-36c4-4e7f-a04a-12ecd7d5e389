package com.ruoyi.custom.admin.pharmacyWarehouse.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryIn;
import com.ruoyi.custom.admin.pharmacyWarehouse.req.PharmacyInventoryInReqVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.resp.PharmacyInventoryInRespVo;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryInService;
import com.ruoyi.custom.utils.OrderUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 药品入库明细Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/pharmacy/inventory/in")
@Api(tags = "药品入库明细")
public class PharmacyInventoryInController extends BaseController {
    @Autowired
    private IPharmacyInventoryInService pharmacyInventoryInService;

    /**
     * 列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(PharmacyInventoryIn pharmacyInventoryIn) {
        startPage();
        List<PharmacyInventoryIn> list = pharmacyInventoryInService.selectPharmacyInventoryInList2(pharmacyInventoryIn);
        return getDataTable(list);
    }

    /**
     * 详情列表
     */
    // @RequiresPermissions("custom:in:list")
    @GetMapping("/subList")
    @ApiOperation(value = "获取详情列表")
    public TableDataInfo<PharmacyInventoryInRespVo> subList(@ApiParam(value = "编号", required = true) @RequestParam String serialNumber) {
        startPage();
        PharmacyInventoryIn base = new PharmacyInventoryIn();
        base.setSerialNumber(serialNumber);
        List<PharmacyInventoryIn> list = pharmacyInventoryInService.selectPharmacyInventoryInList(base);
        if (CollUtil.isEmpty(list)) {
            return getDataTable(list);
        }
        List<PharmacyInventoryInRespVo.Detail> detailList = BeanUtil.copyToList(list, PharmacyInventoryInRespVo.Detail.class);
        PharmacyInventoryInRespVo respVo = new PharmacyInventoryInRespVo();
        BeanUtil.copyProperties(list.get(0), respVo, "detailList");
        respVo.setDetailList(detailList);
        return getDataTable(list);
    }

    /**
     * 入库
     */
    // @RequiresPermissions("custom:in:add")
    @Log(title = "入库", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "入库")
    public AjaxResult add(@RequestBody PharmacyInventoryInReqVo pharmacyInventoryInReqVo) {
        List<PharmacyInventoryIn> items = BeanUtil.copyToList(pharmacyInventoryInReqVo.getDetailList(), PharmacyInventoryIn.class);
        items.forEach(item -> {
            BeanUtil.copyProperties(pharmacyInventoryInReqVo, item, "detailList");
        });
        return toAjax(pharmacyInventoryInService.insertPharmacyInventoryIn(items));
    }

    /**
     * 生成入库编号
     */
    @GetMapping("/generateSerialNumber")
    @ApiOperation(value = "生成入库编号")
    public AjaxResult generateSerialNumber() {
        return AjaxResult.success("操作成功", OrderUtils.getInInventoryCode());
    }

    // /**
    //  * 导出药品入库明细列表
    //  */
    // // @RequiresPermissions("custom:in:export")
    // @Log(title = "药品入库明细", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, PharmacyInventoryIn pharmacyInventoryIn) {
    //     List<PharmacyInventoryIn> list = pharmacyInventoryInService.selectPharmacyInventoryInList(pharmacyInventoryIn);
    //     ExcelUtil<PharmacyInventoryIn> util = new ExcelUtil<PharmacyInventoryIn>(PharmacyInventoryIn.class);
    //     util.exportExcel(response, list, "药品入库明细数据");
    // }
    //
    // /**
    //  * 获取药品入库明细详细信息
    //  */
    // // @RequiresPermissions("custom:in:query")
    // @GetMapping(value = "/{id}")
    // public AjaxResult getInfo(@PathVariable("id") Long id) {
    //     return AjaxResult.success(pharmacyInventoryInService.selectPharmacyInventoryInById(id));
    // }

    // /**
    //  * 修改药品入库明细
    //  */
    // // @RequiresPermissions("custom:in:edit")
    // @Log(title = "药品入库明细", businessType = BusinessType.UPDATE)
    // @PutMapping
    // public AjaxResult edit(@RequestBody PharmacyInventoryIn pharmacyInventoryIn) {
    //     return toAjax(pharmacyInventoryInService.updatePharmacyInventoryIn(pharmacyInventoryIn));
    // }
    //
    // /**
    //  * 删除药品入库明细
    //  */
    // // @RequiresPermissions("custom:in:remove")
    // @Log(title = "药品入库明细", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{ids}")
    // public AjaxResult remove(@PathVariable Long[] ids) {
    //     return toAjax(pharmacyInventoryInService.deletePharmacyInventoryInByIds(ids));
    // }
}

