package com.ruoyi.custom.utils.excel.handler; // 请确保包名正确

import com.ruoyi.common.core.utils.SpringUtils; // 如果需要，用于后备获取 Bean
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.DefaultExcelHandlerAdapter;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo; // 引入你的实体类
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component // 注册为 Spring Bean
public class UserNameHandler extends DefaultExcelHandlerAdapter {

    private static final Logger log = LoggerFactory.getLogger(UserNameHandler.class);

    // 使用 ThreadLocal 缓存每个线程的用户列表
    private static final ThreadLocal<List<SysUser>> userCache = new ThreadLocal<>();

    // 静态实例，用于潜在的访问需求，但优先使用注入的实例
    private static RemoteUserService staticUserService;

    // 注入服务
    @Autowired(required = false) // required=false 避免在某些测试或非web环境下启动失败
    private RemoteUserService userService;

    // PostConstruct 或类似方法，用于设置静态字段（如果需要）或确保注入成功
    @PostConstruct
    public void init() {
        // 如果 Autowired 注入成功，优先使用实例变量
        if (this.userService != null) {
            staticUserService = this.userService;
            log.info("RemoteUserService 通过 @Autowired 成功注入。");
        } else {
            // 如果 @Autowired 在 ExcelUtil 使用此处理器的上下文中无效，则使用 SpringUtils 作为后备方案
            log.warn("RemoteUserService 未通过 @Autowired 注入。尝试使用 SpringUtils。");
            try {
                staticUserService = SpringUtils.getBean(RemoteUserService.class);
                this.userService = staticUserService; // 确保实例字段也被设置
                if (staticUserService != null) {
                    log.info("RemoteUserService 通过 SpringUtils 成功获取。");
                }
            } catch (Exception e) {
                log.error("通过 SpringUtils 获取 RemoteUserService bean 失败。", e);
            }
        }
        if (staticUserService == null) {
            log.error("关键错误：无法获取 RemoteUserService 实例。UserNameHandler 将无法正常工作。");
            // 可以考虑抛出初始化异常阻止应用启动，如果此服务是必需的
            // throw new IllegalStateException("无法初始化 RemoteUserService");
        }
    }

    /**
     * 从 ThreadLocal 缓存中检索用户列表。
     * 如果当前线程的缓存为空，则从 RemoteUserService 获取。
     *
     * @return SysUser 列表，可能为空，但绝不为 null。
     */
    private List<SysUser> getUsersFromCache() {
        List<SysUser> users = userCache.get();
        if (users == null) {
            log.debug("线程 [{}] 的用户缓存未命中。正在从 RemoteUserService 获取。", Thread.currentThread().getName());
            // 优先使用注入的实例变量，后备使用静态变量
            RemoteUserService serviceToUse = (this.userService != null) ? this.userService : staticUserService;

            if (serviceToUse == null) {
                log.error("RemoteUserService 实例不可用。无法获取用户。");
                users = Collections.emptyList(); // 返回空列表以避免稍后出现 NullPointerException
            } else {
                try {
                    // 仅当缓存为空时才获取数据
                    users = serviceToUse.getAll(); // 调用 Feign 接口
                    // 服务可能返回 null
                    if (users == null) {
                        users = Collections.emptyList();
                        log.warn("RemoteUserService.getAll() 返回了 null。使用空列表。");
                    }
                    // 将获取到的列表（即使为空）存储在缓存中
                    userCache.set(users);
                    log.debug("线程 [{}] 的用户缓存已填充，包含 {} 个用户。", Thread.currentThread().getName(), users.size());
                } catch (Exception e) {
                    // 捕获调用远程服务时可能发生的异常
                    log.error("从 RemoteUserService 获取用户时出错: {}", e.getMessage(), e);
                    users = Collections.emptyList(); // 出错时返回空列表
                    // 根据业务需要，可能需要抛出异常中断操作
                    // throw new RuntimeException("无法加载基础用户数据: " + e.getMessage(), e);
                }
            }
        } else {
            log.trace("线程 [{}] 的用户缓存命中。", Thread.currentThread().getName());
        }
        return users;
    }

    /**
     * 重要：清除当前线程的 ThreadLocal 缓存。
     * 必须在 Excel 导入/导出操作完成后调用此方法，以防止内存泄漏。
     */
    public static void clearCache() {
        userCache.remove();
        log.debug("线程 [{}] 的用户缓存已清除。", Thread.currentThread().getName());
    }


    /**
     * 导入时，解析单元格中的用户昵称。
     * 找到对应的用户 ID 设置到实体的 marketerId 字段。
     * 将原始的昵称（单元格的值）设置到实体的 marketer 字段。
     *
     * @param cellValue 从Excel单元格读取到的值 (用户昵称)
     * @param args      注解中指定的args参数
     * @param entity    当前正在处理的实体对象实例 (预期是 MarketingCustomerInfo)
     * @return 返回 null，表示 handler 已完全处理相关字段，ExcelUtil 无需再进行设置。
     *         如果查找失败或发生错误，则抛出 RuntimeException。
     */
    @Override
    public Object parse(Object cellValue, String[] args, Object entity) {
        // 确保传入的 entity 是我们期望的类型
        if (!(entity instanceof MarketingCustomerInfo)) {
            // 如果 entity 为 null 或类型不匹配，记录错误并可能提前返回或抛异常
            if(entity == null) {
                log.error("UserNameHandler.parse 接收到的实体为 null。");
            } else {
                log.error("UserNameHandler 期望处理 MarketingCustomerInfo 类型，但收到了 {}", entity.getClass().getName());
            }
            // 抛出异常可能更合适，因为它表示调用方式或配置有问题
            throw new IllegalArgumentException("内部错误：数据处理器接收到的实体类型不正确或为 null");
        }
        MarketingCustomerInfo targetEntity = (MarketingCustomerInfo) entity;

        // 处理空单元格值
        if (cellValue == null || StringUtils.isBlank(cellValue.toString())) {
            log.trace("单元格值为空，将 marketerId 和 marketer 设置为 null。");
            targetEntity.setMarketerId(null); // 设置 ID 为 null
            targetEntity.setMarketer(null);   // 设置 Name 为 null
            return null; // 指示 ExcelUtil 不需要进一步处理此字段
        }

        String nickName = cellValue.toString().trim();

        List<SysUser> userList = getUsersFromCache();

        if (CollectionUtils.isEmpty(userList)) {
            log.warn("用户列表为空或无法获取。无法按昵称 '{}' 查找用户。", nickName);
            // 根据业务需求决定是抛异常还是允许导入（ID为null）
            // 此处选择抛异常，强制数据质量
            throw new RuntimeException("用户基础数据加载失败或为空，无法匹配用户: " + nickName);
        }

        // 在缓存列表中按昵称查找用户 (忽略大小写可能更健壮)
        Optional<SysUser> foundUser = userList.stream()
                .filter(user -> nickName.equalsIgnoreCase(user.getNickName())) // 使用 equalsIgnoreCase
                .findFirst();

        if (foundUser.isPresent()) {
            SysUser user = foundUser.get();
            Long userId = user.getUserId();
            // 使用从数据库/缓存中获取到的准确昵称，而不是用户输入的（可能大小写不同）
            String actualNickName = user.getNickName();
            log.trace("找到昵称 '{}' 对应的用户 ID {}，实际昵称 '{}'。正在设置到实体。", nickName, userId, actualNickName);

            // *** 直接设置实体类的两个字段 ***
            targetEntity.setMarketerId(userId);          // 设置 ID
            targetEntity.setMarketer(actualNickName);    // 设置从系统获取的准确 Name

            return null; // 返回 null，表示 handler 已处理，ExcelUtil 无需再设置此字段
        } else {
            // 未找到对应的用户
            log.warn("导入警告：在缓存的用户列表中未找到昵称为 '{}' 的用户记录。", nickName);
            // 根据业务决定如何处理：抛异常或允许导入但ID为null
            // 抛出异常，中断导入 (推荐)
            throw new RuntimeException("未找到用户: " + nickName);
            /*
            // 或者：允许导入，但标记问题（ID为null，Name为输入值）
            targetEntity.setMarketerId(null);
            targetEntity.setMarketer(nickName + " (未找到)"); // 可以附加标记
            return null; // 仍然返回 null 表示已处理
            */
        }
    }

    /**
     * 导出时，将用户 ID (UserId) 转换为对应的用户昵称 (NickName)。
     *
     * @param value 字段的原始值 (应该是 UserId, Object 类型)
     * @param args  注解中指定的args参数 (当前未使用)
     * @return 查找到的 NickName (String 类型)，如果找不到则返回原始 ID 的字符串形式
     */
    @Override
    public Object format(Object value, String[] args) {
        if (value == null) {
            return ""; // 导出空字符串而非 null
        }

        Long userIdToFind;
        try {
            // 尝试将值转换为 Long
            if (value instanceof Long) {
                userIdToFind = (Long) value;
            } else if (value instanceof Number) {
                userIdToFind = ((Number) value).longValue();
            }
            else {
                userIdToFind = Long.valueOf(String.valueOf(value).trim());
            }
        } catch (NumberFormatException e) {
            log.warn("值 '{}' 无法转换为 Long 类型以进行用户 ID 查找。返回原始值。", value);
            return String.valueOf(value); // 返回原始值的字符串形式
        }

        List<SysUser> userList = getUsersFromCache();

        if (CollectionUtils.isEmpty(userList)) {
            log.warn("用户列表为空或无法获取，无法格式化用户 ID '{}'。返回原始 ID。", userIdToFind);
            return String.valueOf(userIdToFind); // 返回原始 ID 的字符串形式
        }

        // 在缓存列表中按用户 ID 查找用户
        Optional<SysUser> foundUser = userList.stream()
                .filter(user -> userIdToFind.equals(user.getUserId())) // 按用户 ID 匹配
                .findFirst();

        if (foundUser.isPresent()) {
            String nickName = foundUser.get().getNickName();
            log.trace("找到用户 ID {} 对应的昵称 '{}'", userIdToFind, nickName);
            return nickName; // 返回昵称
        } else {
            log.warn("导出警告：在缓存的用户列表中未找到 ID 为 '{}' 的用户记录。将导出原始 ID。", userIdToFind);
            return String.valueOf(userIdToFind); // 返回原始 ID 的字符串形式
        }
    }
}
