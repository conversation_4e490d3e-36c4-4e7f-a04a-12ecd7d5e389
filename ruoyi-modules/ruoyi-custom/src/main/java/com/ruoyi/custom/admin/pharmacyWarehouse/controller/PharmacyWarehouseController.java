package com.ruoyi.custom.admin.pharmacyWarehouse.controller;


import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyWarehouse;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyWarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 药品仓库Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/pharmacy/warehouse")
@Api(tags = "药品仓库")
public class PharmacyWarehouseController extends BaseController {
    @Autowired
    private IPharmacyWarehouseService pharmacyWarehouseService;

    /**
     * 查询药品仓库列表
     */
    // @RequiresPermissions("custom:warehouse:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(PharmacyWarehouse pharmacyWarehouse) {
        startPage();
        List<PharmacyWarehouse> list = pharmacyWarehouseService.selectPharmacyWarehouseList(pharmacyWarehouse);
        return getDataTable(list);
    }

    /**
     * 导出药品仓库列表
     */
    // @RequiresPermissions("custom:warehouse:export")
    @Log(title = "药品仓库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PharmacyWarehouse pharmacyWarehouse) {
        List<PharmacyWarehouse> list = pharmacyWarehouseService.selectPharmacyWarehouseList(pharmacyWarehouse);
        ExcelUtil<PharmacyWarehouse> util = new ExcelUtil<PharmacyWarehouse>(PharmacyWarehouse.class);
        util.exportExcel(response, list, "药品仓库数据");
    }

    /**
     * 获取药品仓库详细信息
     */
    // @RequiresPermissions("custom:warehouse:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取详情")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(pharmacyWarehouseService.selectPharmacyWarehouseById(id));
    }

    /**
     * 新增药品仓库
     */
    // @RequiresPermissions("custom:warehouse:add")
    @Log(title = "药品仓库", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增")
    public AjaxResult add(@RequestBody PharmacyWarehouse pharmacyWarehouse) {
        return toAjax(pharmacyWarehouseService.insertPharmacyWarehouse(pharmacyWarehouse));
    }

    /**
     * 修改药品仓库
     */
    // @RequiresPermissions("custom:warehouse:edit")
    @Log(title = "药品仓库", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改")
    public AjaxResult edit(@RequestBody PharmacyWarehouse pharmacyWarehouse) {
        return toAjax(pharmacyWarehouseService.updatePharmacyWarehouse(pharmacyWarehouse));
    }

    /**
     * 删除药品仓库
     */
    // @RequiresPermissions("custom:warehouse:remove")
    @Log(title = "药品仓库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(pharmacyWarehouseService.deletePharmacyWarehouseByIds(ids));
    }
}

