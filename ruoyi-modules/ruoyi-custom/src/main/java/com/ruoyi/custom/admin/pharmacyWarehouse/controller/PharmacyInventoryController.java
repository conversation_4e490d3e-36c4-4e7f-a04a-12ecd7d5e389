package com.ruoyi.custom.admin.pharmacyWarehouse.controller;


import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventory;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 药品库存Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/pharmacy/inventory")
@Api(tags = "药品库存")
public class PharmacyInventoryController extends BaseController {
    @Autowired
    private IPharmacyInventoryService pharmacyInventoryService;

    /**
     * 详情列表
     */
    // @RequiresPermissions("custom:inventory:list")
    @GetMapping("/subList")
    @ApiOperation(value = "获取详情列表")
    public TableDataInfo subList(PharmacyInventory pharmacyInventory) {
        startPage();
        List<PharmacyInventory> list = pharmacyInventoryService.selectPharmacyInventoryList(pharmacyInventory);
        return getDataTable(list);
    }

    /**
     * 列表
     */
    // @RequiresPermissions("custom:inventory:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(PharmacyInventory pharmacyInventory) {
        startPage();
        List<PharmacyInventory> list = pharmacyInventoryService.selectPharmacyInventoryList2(pharmacyInventory);
        return getDataTable(list);
    }

    /**
     * 查询库存中存在的仓库列表
     */
    @GetMapping("/getWarehouseListInInventory")
    @ApiOperation(value = "查询库存中存在的仓库列表")
    public AjaxResult getWarehouseList() {
        return AjaxResult.success(pharmacyInventoryService.getWarehouseListInInventory());
    }

    /**
     * 根据仓库id获取库存中存在的药品列表
     */
    @GetMapping("/getPharmacyListInInventory")
    @ApiOperation(value = "根据仓库id获取库存中存在的药品列表")
    public AjaxResult getPharmacyListInInventory(@ApiParam(name = "warehouseId", value = "仓库id", required = true) Long warehouseId) {
        return AjaxResult.success(pharmacyInventoryService.getPharmacyListInInventory(warehouseId));
    }

    /**
     * 根据仓库id、药品id获取对应的批次列表
     */
    @GetMapping("/getBatchList")
    @ApiOperation(value = "根据仓库id、药品id获取对应的批次列表")
    public AjaxResult getBatchList(@ApiParam(name = "warehouseId", value = "仓库id", required = true) Long warehouseId,
                                   @ApiParam(name = "pharmacyId", value = "药品id", required = true) Long pharmacyId) {
        return AjaxResult.success(pharmacyInventoryService.getBatchList(warehouseId, pharmacyId));
    }

    /**
     * 根据仓库id、药品id、批次查询库存数据
     */
    @GetMapping("/getInventoryData")
    @ApiOperation(value = "根据仓库id、药品id、批次查询库存数据")
    public AjaxResult getInventoryData(@ApiParam(name = "warehouseId", value = "仓库id") Long warehouseId,
                                       @ApiParam(name = "pharmacyId", value = "药品id") Long pharmacyId,
                                       @ApiParam(name = "batch", value = "批次") Integer batch) {
        return AjaxResult.success(pharmacyInventoryService.selectPharmacyInventoryByUnionId(warehouseId, pharmacyId, batch));
    }

    // /**
    //  * 导出药品库存列表
    //  */
    // // @RequiresPermissions("custom:inventory:export")
    // @Log(title = "药品库存", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, PharmacyInventory pharmacyInventory) {
    //     List<PharmacyInventory> list = pharmacyInventoryService.selectPharmacyInventoryList(pharmacyInventory);
    //     ExcelUtil<PharmacyInventory> util = new ExcelUtil<PharmacyInventory>(PharmacyInventory.class);
    //     util.exportExcel(response, list, "药品库存数据");
    // }
    //
    // /**
    //  * 获取药品库存详细信息
    //  */
    // // @RequiresPermissions("custom:inventory:query")
    // @GetMapping(value = "/{id}")
    // public AjaxResult getInfo(@PathVariable("id") Long id) {
    //     return AjaxResult.success(pharmacyInventoryService.selectPharmacyInventoryById(id));
    // }

    // /**
    //  * 新增药品库存
    //  */
    // // @RequiresPermissions("custom:inventory:add")
    // @Log(title = "药品库存", businessType = BusinessType.INSERT)
    // @PostMapping
    // public AjaxResult add(@RequestBody PharmacyInventory pharmacyInventory) {
    //     return toAjax(pharmacyInventoryService.insertPharmacyInventory(pharmacyInventory));
    // }
    //
    // /**
    //  * 修改药品库存
    //  */
    // // @RequiresPermissions("custom:inventory:edit")
    // @Log(title = "药品库存", businessType = BusinessType.UPDATE)
    // @PutMapping
    // public AjaxResult edit(@RequestBody PharmacyInventory pharmacyInventory) {
    //     return toAjax(pharmacyInventoryService.updatePharmacyInventory(pharmacyInventory));
    // }
    //
    // /**
    //  * 删除药品库存
    //  */
    // // @RequiresPermissions("custom:inventory:remove")
    // @Log(title = "药品库存", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{ids}")
    // public AjaxResult remove(@PathVariable Long[] ids) {
    //     return toAjax(pharmacyInventoryService.deletePharmacyInventoryByIds(ids));
    // }
}

