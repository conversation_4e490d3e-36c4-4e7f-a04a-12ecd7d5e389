package com.ruoyi.custom.admin.storiedBuilding.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeIndexInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeBaseInfo;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeBaseInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 房间类型Controller
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@RestController
@RequestMapping("/roomTypeBaseInfo")
@Api(value = "房间类型Controller", tags = "房间类型")
public class RoomTypeBaseInfoController extends BaseController {
    @Autowired
    private IRoomTypeBaseInfoService roomTypeBaseInfoService;


    @Autowired
    private IRoomTypeIndexInfoService roomTypeIndexInfoService;

    /**
     * 查询房间类型列表
     */
    //@RequiresPermissions("storiedBuilding:roomTypeBaseInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询房间类型列表")
    @ApiImplicitParams({
            @ApiImplicitParam(dataTypeClass = String.class, name = "name", value = "房间类型名称", paramType = "query", required = false),
            @ApiImplicitParam(dataTypeClass = String.class, name = "version", value = "版本号", paramType = "query", required = false),
            @ApiImplicitParam(dataTypeClass = String.class, name = "status", value = "状态", paramType = "query", required = false),
    })
    public TableDataInfo list(@ApiIgnore RoomTypeBaseInfo roomTypeBaseInfo) {
        startPage();
        List<RoomTypeBaseInfo> list = roomTypeBaseInfoService.selectRoomTypeBaseInfoList(roomTypeBaseInfo);
        return getDataTable(list);
    }

    /**
     * 导出房间类型列表
     */
    //@RequiresPermissions("storiedBuilding:roomTypeBaseInfo:export")
    @Log(platform = "1", title = "房间类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出房间类型列表")
    public void export(HttpServletResponse response, RoomTypeBaseInfo roomTypeBaseInfo) {
        List<RoomTypeBaseInfo> list = roomTypeBaseInfoService.selectRoomTypeBaseInfoList(roomTypeBaseInfo);
        ExcelUtil<RoomTypeBaseInfo> util = new ExcelUtil<RoomTypeBaseInfo>(RoomTypeBaseInfo.class);
        util.exportExcel(response, list, "房间类型数据");
    }

    /**
     * 获取房间类型详细信息
     */
    //@RequiresPermissions("storiedBuilding:roomTypeBaseInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取房间类型详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(roomTypeBaseInfoService.selectRoomTypeBaseInfoById(id));
    }

    /**
     * 获取房间类型的key和value值
     */
    @GetMapping(value = "getRoomTypeLabelAndValue")
    @ApiOperation(value = "获取房间类型的key和value值")
    public AjaxResult getRoomTypeLabelAndValue() {
        return AjaxResult.success().put("data", roomTypeBaseInfoService.getRoomTypeLabelAndValue());
    }

    /**
     * 新增房间类型
     */
    //@RequiresPermissions("storiedBuilding:roomTypeBaseInfo:add")
    @Log(platform = "1", title = "房间类型", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增房间类型")
    public AjaxResult add(@RequestBody RoomTypeBaseInfo roomTypeBaseInfo) {
        return toAjax(roomTypeBaseInfoService.insertRoomTypeBaseInfo(roomTypeBaseInfo));
    }

    /**
     * 修改房间类型
     */
    //@RequiresPermissions("storiedBuilding:roomTypeBaseInfo:edit")
    @Log(platform = "1", title = "房间类型", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改房间类型")
    public AjaxResult edit(@RequestBody RoomTypeBaseInfo roomTypeBaseInfo) {
        return toAjax(roomTypeBaseInfoService.updateRoomTypeBaseInfo(roomTypeBaseInfo));
    }

    /**
     * 删除房间类型
     */
    //@RequiresPermissions("storiedBuilding:roomTypeBaseInfo:remove")
    @Log(platform = "1", title = "房间类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除房间类型")
    public AjaxResult remove(@PathVariable Long[] ids) {
        for (Long id : ids) {
            Boolean result = roomTypeIndexInfoService.hasRoomTypeId(id);
            if (result) {
                return AjaxResult.error("该房间类型正在使用，不可删除！");
            }
        }
        return toAjax(roomTypeBaseInfoService.deleteRoomTypeBaseInfoByIds(ids));
    }
}
