package com.ruoyi.custom.admin.pharmacyWarehouse.domain;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@Data
@ApiModel(value = "药品报废明细")
public class PharmacyInventoryScrap {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "编号，格式：YPBF-20230202-0001")
    private String serialNumber;

    @ApiModelProperty(value = "库存id，对应t_pharmacy_inventory")
    private Long inventoryId;

    @ApiModelProperty(value = "仓库id，对应t_pharmacy_warehouse")
    private Long warehouseId;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "药品id，对应t_pharmacy_management")
    private Long pharmacyId;

    @ApiModelProperty(value = "药品名称")
    private String pharmacyName;

    @ApiModelProperty(value = "批次")
    private Integer batch;

    @ApiModelProperty(value = "报废数量")
    private Integer scrapQuantity;

    @ApiModelProperty(value = "经办人id，对应sys_user")
    private Long operatorId;

    @ApiModelProperty(value = "经办人")
    private String operatorName;

    @ApiModelProperty(value = "原因，字典：custom_pharmacy_inventory_scrap_reason")
    private String reason;

    @ApiModelProperty(value = "报废时间")
    private Date scrapTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}

