package com.ruoyi.custom.attendance;


import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.custom.admin.attendance.domain.BaseHolidays;
import com.ruoyi.custom.admin.attendance.domain.EmployeeGroup;
import com.ruoyi.custom.admin.attendance.domain.SchedulingGroup;
import com.ruoyi.custom.admin.attendance.domain.ShiftInfo;
import com.ruoyi.custom.admin.attendance.mapper.SchedulingGroupMapper;
import com.ruoyi.custom.admin.attendance.service.SchedulingGroupService;
import com.ruoyi.custom.admin.attendance.service.ShiftInfoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
public class SchedulingGroupTestService {

    @Autowired
    private SchedulingGroupService schedulingGroupService;

    @Autowired
    private SchedulingGroupMapper schedulingGroupMapper;

    @Autowired
    private ShiftInfoService shiftInfoService;

    @Test
    public void myScheduling() {
        Date dateParam = new Date();
        Long userId = 45L;


        List<Map<String, Object>> returnData = new ArrayList<>();

        // 获取当前用户所属的排班分组
        EmployeeGroup employeeGroupParams = new EmployeeGroup();
        employeeGroupParams.setEmployeeId(userId);
        List<EmployeeGroup> employeeGroups = schedulingGroupMapper.selectEmployeeGroup(employeeGroupParams);
        if (CollectionUtil.isEmpty(employeeGroups)) {
            return; // 无排班分组直接返回
        }

        for (EmployeeGroup employeeGroup : employeeGroups) {
            // 获取排班分组信息
            SchedulingGroup schedulingGroup = schedulingGroupMapper.selectSchedulingGroupById(employeeGroup.getGroupId());
            List<ShiftInfo> shiftInfos = getShiftInfoList(schedulingGroup.getShiftIds()); // 获取排班表信息

            List<Map<String, Object>> groupData = generateSchedulingData(dateParam, schedulingGroup, shiftInfos);

            // 设置休息日和法定节假日
            setRestDays(schedulingGroup, groupData);
            setLegalHolidays(schedulingGroup, groupData);

            returnData.addAll(groupData);
        }

        // 合并同一天的排班信息
        Object r = mergeSchedulingData(returnData);
        return;
    }

    private List<ShiftInfo> getShiftInfoList(String shiftIds) {
        // 根据排班表ID获取排班信息，并按原顺序排列
        List<ShiftInfo> shiftInfos = shiftInfoService.listByIds(Arrays.asList(shiftIds.split(",")));
        Map<Integer, ShiftInfo> shiftInfoMap = shiftInfos.stream()
                .collect(Collectors.toMap(ShiftInfo::getId, shiftInfo -> shiftInfo));
        return Arrays.stream(shiftIds.split(","))
                .map(id -> shiftInfoMap.get(Integer.parseInt(id)))
                .collect(Collectors.toList());
    }

    private List<Map<String, Object>> generateSchedulingData(Date dateParam, SchedulingGroup schedulingGroup, List<ShiftInfo> shiftInfos) {
        List<Map<String, Object>> data = new ArrayList<>();
        LocalDate firstScheduleDate = toLocalDate(schedulingGroup.getFirstScheduleDate());
        LocalDate firstDayOfMonth = toLocalDate(dateParam).withDayOfMonth(1);
        int cycleDays = shiftInfos.size();
        boolean isSameMonth = isSameMonth(firstScheduleDate, firstDayOfMonth);

        if (firstScheduleDate.isAfter(firstDayOfMonth) && !isSameMonth) {
            return data; // 首次排班日期晚于当前月的第一天且不在同一月份，直接返回空
        }

        int startDayOffset = (int) ChronoUnit.DAYS.between(firstScheduleDate, firstDayOfMonth);
        int daysToGenerate = isSameMonth ? (int) ChronoUnit.DAYS.between(firstScheduleDate, firstScheduleDate.withDayOfMonth(firstScheduleDate.lengthOfMonth())) + 1
                : firstDayOfMonth.lengthOfMonth();

        for (int i = 0; i < daysToGenerate; i++) {
            int index = isSameMonth ? i % cycleDays : (startDayOffset + i) % cycleDays;
            Map<String, Object> schedule = new HashMap<>();
            schedule.put("date", isSameMonth ? firstScheduleDate.plusDays(i) : firstDayOfMonth.plusDays(i));
            schedule.put("value", shiftInfos.get(index).getShiftName());
            data.add(schedule);
        }

        return data;
    }

    private void setRestDays(SchedulingGroup schedulingGroup, List<Map<String, Object>> data) {
        if (schedulingGroup.getRestDayMode() == 2) {
            return; // 无固定休息日，直接返回
        }

        int restDayMode = schedulingGroup.getRestDayMode(); // 0：按周，1：按月
        String restDays = schedulingGroup.getRestDays();
        data.forEach(schedule -> {
            LocalDate date = (LocalDate) schedule.get("date");
            int dayValue = restDayMode == 1 ? date.getDayOfMonth() : date.getDayOfWeek().getValue();
            if (restDays.contains(String.valueOf(dayValue))) {
                schedule.put("value", "休");
            }
        });
    }

    private void setLegalHolidays(SchedulingGroup schedulingGroup, List<Map<String, Object>> data) {
        if (schedulingGroup.getLegalHolidayAutoRest() == 1) {
            return; // 法定节假日自动排休关闭
        }

        List<BaseHolidays> legalHolidays = schedulingGroupMapper.selectLegalHoliday();
        Set<LocalDate> holidayDates = legalHolidays.stream()
                .map(holiday -> toLocalDate(holiday.getHolidays()))
                .collect(Collectors.toSet());

        data.forEach(schedule -> {
            LocalDate date = (LocalDate) schedule.get("date");
            if (holidayDates.contains(date)) {
                schedule.put("value", "假");
            }
        });
    }

    private Object mergeSchedulingData(List<Map<String, Object>> data) {
        Map<LocalDate, List<String>> groupedData = data.stream()
                .collect(Collectors.groupingBy(
                        schedule -> (LocalDate) schedule.get("date"),
                        Collectors.mapping(schedule -> (String) schedule.get("value"), Collectors.toList())
                ));

        return groupedData.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> merged = new HashMap<>();
                    merged.put("date", entry.getKey());
                    merged.put("value", String.join(",", entry.getValue()));
                    return merged;
                })
                .collect(Collectors.toList());
    }

    private LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    private boolean isSameMonth(LocalDate date1, LocalDate date2) {
        return date1.getMonthValue() == date2.getMonthValue() && date1.getYear() == date2.getYear();
    }
}
