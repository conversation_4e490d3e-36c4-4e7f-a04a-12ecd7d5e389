package com.ruoyi.custom.admin.marketing.service;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecord;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecordImage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 缴费变更单镜像表关联功能测试
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class PaymentChangeRecordImageAssociationTest {

    @Autowired
    private IPaymentChangeRecordService paymentChangeRecordService;

    @Autowired
    private IPaymentChangeRecordImageService paymentChangeRecordImageService;

    /**
     * 测试生成变更信息并保存镜像记录
     */
    @Test
    public void testGenerateChangeInfoAndSaveImage() {
        // 使用有效的合同编号进行测试
        String contractNumber = "TEST_CONTRACT_001";
        String type = "1"; // 护理套餐、床位变更

        try {
            // 生成变更信息
            PaymentChangeRecord changeRecord = paymentChangeRecordService.generatePaymentChangeInfo(contractNumber, type);

            // 验证返回结果
            assertNotNull(changeRecord, "生成的变更记录不能为空");
            assertNotNull(changeRecord.getTempGenerationId(), "tempGenerationId不能为空");
            assertTrue(StrUtil.isNotBlank(changeRecord.getTempGenerationId()), "tempGenerationId不能为空字符串");

            // 验证镜像记录已保存
            PaymentChangeRecordImage imageRecord = paymentChangeRecordImageService
                    .selectPaymentChangeRecordImageByTempGenerationId(changeRecord.getTempGenerationId());
            assertNotNull(imageRecord, "镜像记录应该已保存");
            assertEquals(changeRecord.getTempGenerationId(), imageRecord.getTempGenerationId(), "tempGenerationId应该匹配");
            assertNull(imageRecord.getOriginalId(), "镜像记录的originalId应该为空");
            assertEquals("0", imageRecord.getPaymentStatus(), "镜像记录状态应该为暂存");

            System.out.println("✅ 生成变更信息并保存镜像记录测试通过");
        } catch (Exception e) {
            System.out.println("⚠️ 测试跳过：" + e.getMessage());
            // 如果是因为测试数据不存在导致的异常，跳过测试
            assertTrue(e.getMessage().contains("未入住") || e.getMessage().contains("没有缴费记录"));
        }
    }

    /**
     * 测试基于镜像数据创建暂存变更单
     */
    @Test
    public void testCreateDraftFromImage() {
        // 创建模拟的镜像记录
        PaymentChangeRecordImage mockImage = createMockImageRecord();
        paymentChangeRecordImageService.insertPaymentChangeRecordImage(mockImage);

        // 创建暂存请求
        PaymentChangeRecord draftRequest = new PaymentChangeRecord();
        draftRequest.setTempGenerationId(mockImage.getTempGenerationId());
        draftRequest.setRemark("测试备注");
        draftRequest.setAccountAddCost(new BigDecimal("100.00"));

        // 创建暂存记录
        String draftId = paymentChangeRecordService.draftPaymentChangeRecord(draftRequest);

        // 验证结果
        assertNotNull(draftId, "暂存记录ID不能为空");
        assertTrue(StrUtil.isNotBlank(draftId), "暂存记录ID不能为空字符串");

        // 验证镜像记录的originalId已更新
        PaymentChangeRecordImage updatedImage = paymentChangeRecordImageService
                .selectPaymentChangeRecordImageByTempGenerationId(mockImage.getTempGenerationId());
        assertEquals(draftId, updatedImage.getOriginalId(), "镜像记录的originalId应该已更新");

        // 验证主表记录
        PaymentChangeRecord savedRecord = paymentChangeRecordService.selectPaymentChangeRecordById(draftId);
        assertNotNull(savedRecord, "主表记录应该已保存");
        assertEquals("0", savedRecord.getPaymentStatus(), "主表记录状态应该为暂存");
        assertEquals("测试备注", savedRecord.getRemark(), "备注应该已更新");

        System.out.println("✅ 基于镜像数据创建暂存变更单测试通过");
    }

    /**
     * 测试修改暂存变更单基于原始镜像数据
     */
    @Test
    public void testUpdateDraftBasedOnOriginalImage() {
        // 创建模拟的镜像记录和暂存记录
        PaymentChangeRecordImage mockImage = createMockImageRecord();
        paymentChangeRecordImageService.insertPaymentChangeRecordImage(mockImage);

        PaymentChangeRecord draftRequest = new PaymentChangeRecord();
        draftRequest.setTempGenerationId(mockImage.getTempGenerationId());
        draftRequest.setRemark("初始备注");
        String draftId = paymentChangeRecordService.draftPaymentChangeRecord(draftRequest);

        // 修改暂存记录
        PaymentChangeRecord updateRequest = new PaymentChangeRecord();
        updateRequest.setId(draftId);
        updateRequest.setRemark("修改后的备注");
        updateRequest.setAccountAddCost(new BigDecimal("200.00"));

        String updatedId = paymentChangeRecordService.updateDraftPaymentChangeRecord(updateRequest);

        // 验证结果
        assertEquals(draftId, updatedId, "更新后的ID应该保持不变");

        PaymentChangeRecord updatedRecord = paymentChangeRecordService.selectPaymentChangeRecordById(draftId);
        assertEquals("修改后的备注", updatedRecord.getRemark(), "备注应该已更新");
        assertEquals(new BigDecimal("200.00"), updatedRecord.getAccountAddCost(), "账户变动金额应该已更新");

        // 验证其他字段基于镜像数据
        assertEquals(mockImage.getContractNumber(), updatedRecord.getContractNumber(), "合同编号应该基于镜像数据");
        assertEquals(mockImage.getElderlyName(), updatedRecord.getElderlyName(), "老人姓名应该基于镜像数据");

        System.out.println("✅ 修改暂存变更单基于原始镜像数据测试通过");
    }

    /**
     * 测试无效tempGenerationId的异常处理
     */
    @Test
    public void testInvalidTempGenerationIdException() {
        PaymentChangeRecord draftRequest = new PaymentChangeRecord();
        draftRequest.setTempGenerationId("INVALID_TEMP_ID");

        // 验证异常抛出
        Exception exception = assertThrows(Exception.class, () -> {
            paymentChangeRecordService.draftPaymentChangeRecord(draftRequest);
        });

        assertTrue(exception.getMessage().contains("未找到对应的变更信息"), "应该抛出未找到变更信息的异常");

        System.out.println("✅ 无效tempGenerationId异常处理测试通过");
    }

    /**
     * 测试镜像记录查询功能
     */
    @Test
    public void testImageRecordQuery() {
        // 创建模拟的镜像记录
        PaymentChangeRecordImage mockImage = createMockImageRecord();
        paymentChangeRecordImageService.insertPaymentChangeRecordImage(mockImage);

        // 测试根据tempGenerationId查询
        PaymentChangeRecordImage foundByTempId = paymentChangeRecordImageService
                .selectPaymentChangeRecordImageByTempGenerationId(mockImage.getTempGenerationId());
        assertNotNull(foundByTempId, "应该能根据tempGenerationId查询到记录");
        assertEquals(mockImage.getId(), foundByTempId.getId(), "查询到的记录ID应该匹配");

        // 设置originalId并测试查询
        mockImage.setOriginalId("TEST_ORIGINAL_ID");
        paymentChangeRecordImageService.updatePaymentChangeRecordImage(mockImage);

        PaymentChangeRecordImage foundByOriginalId = paymentChangeRecordImageService
                .selectPaymentChangeRecordImageByOriginalId("TEST_ORIGINAL_ID");
        assertNotNull(foundByOriginalId, "应该能根据originalId查询到记录");
        assertEquals(mockImage.getId(), foundByOriginalId.getId(), "查询到的记录ID应该匹配");

        System.out.println("✅ 镜像记录查询功能测试通过");
    }

    /**
     * 创建模拟的镜像记录
     */
    private PaymentChangeRecordImage createMockImageRecord() {
        PaymentChangeRecordImage mockImage = new PaymentChangeRecordImage();
        mockImage.setId("TEST_IMAGE_" + System.currentTimeMillis());
        mockImage.setTempGenerationId("TEMP_" + System.currentTimeMillis());
        mockImage.setContractNumber("TEST_CONTRACT_001");
        mockImage.setElderlyId("TEST_ELDERLY_001");
        mockImage.setElderlyName("测试老人");
        mockImage.setPaymentStatus("0");
        mockImage.setAccountAddCost(BigDecimal.ZERO);
        mockImage.setDetails(new ArrayList<>());
        return mockImage;
    }
}
