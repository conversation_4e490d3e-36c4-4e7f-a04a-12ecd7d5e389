-- ----------------------------
-- 缴费变更单镜像表建表SQL
-- 作者: zkx
-- 日期: 2025-08-08
-- 描述: 创建缴费变更单镜像表，用于保存调用 /change/info 接口时生成的初始状态快照
-- ----------------------------

CREATE TABLE `t_payment_change_record_image` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `temp_generation_id` varchar(64) DEFAULT NULL COMMENT '临时生成ID，用于关联 /change/info 与 暂存操作',
  `original_id` varchar(64) DEFAULT NULL COMMENT '对应 t_payment_change_record.id，用于反向追溯',
  `contract_number` varchar(100) NOT NULL COMMENT '合同编号，管理表：t_contract_info',
  `live_id` varchar(64) DEFAULT NULL COMMENT '居住id，关联表：t_live_base_info',
  `elderly_id` varchar(64) NOT NULL COMMENT '老人ID',
  `elderly_name` varchar(50) DEFAULT NULL COMMENT '老人姓名',
  `contract_start_date` date DEFAULT NULL COMMENT '合同开始日期',
  `contract_end_date` date DEFAULT NULL COMMENT '合同结束日期',
  `contract_cycle` int DEFAULT NULL COMMENT '合同周期（月数）',
  `care_level` varchar(50) DEFAULT NULL COMMENT '护理级别',
  `bed_name` varchar(50) DEFAULT NULL COMMENT '房间号',
  `account_add_cost` decimal(10,2) DEFAULT NULL COMMENT '本次账户变动金额',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `payment_status` varchar(2) DEFAULT NULL COMMENT '缴费状态，字典：custom_payment_record_status；0：暂存，1：已确认',
  `details` json DEFAULT NULL COMMENT '变更详情（List<Detail> JSON）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_temp_generation_id` (`temp_generation_id`),
  KEY `idx_original_id` (`original_id`),
  KEY `idx_contract_number` (`contract_number`),
  KEY `idx_elderly_id` (`elderly_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='缴费变更单镜像表';
