# 缴费变更单镜像表关联功能说明

## 功能概述

为保障缴费变更单数据的可追溯性与一致性，系统新增镜像表 `t_payment_change_record_image`，用于保存用户在调用 `/change/info` 接口时生成的**缴费变更单初始状态快照**。

后续用户编辑并暂存变更单时，才在主表 `t_payment_change_record` 中创建正式记录。  
要求：**无论用户后续如何修改暂存单，每次编辑都应基于最原始的变更单内容进行**。

## 核心机制

### 1. 临时生成标识（tempGenerationId）

- 在调用 `/change/info` 时生成
- 用于关联镜像表记录与后续的暂存操作
- 前端需要缓存此标识，在暂存时传递给后端

### 2. 镜像表关联字段

| 字段名 | 说明 |
|--------|------|
| `temp_generation_id` | 临时生成标识，UUID，用于关联生成与暂存操作 |
| `original_id` | 对应主表 `t_payment_change_record.id`，暂存时回填 |

### 3. 数据流程

```
[前端] 调用 /change/info
    ↓
[后端] 生成变更信息 + tempGenerationId
    ↓
[后端] 保存镜像数据到 t_payment_change_record_image
    ↓
[前端] 接收 PaymentChangeRecord（含 tempGenerationId）
    ↓
[前端] 用户编辑后调用 /draft（携带 tempGenerationId）
    ↓
[后端] 根据 tempGenerationId 查询镜像数据
    ↓
[后端] 基于镜像数据创建主表记录
    ↓
[后端] 更新镜像表的 original_id 字段
```

## API 接口变更

### 1. 生成变更信息接口

**接口**: `GET /change/info`

**返回数据结构变更**:
```json
{
  "code": 200,
  "data": {
    "id": null,
    "tempGenerationId": "a1b2c3d4e5f6...",
    "contractNumber": "...",
    "elderlyName": "...",
    "details": [...]
  }
}
```

### 2. 暂存变更单接口

**接口**: `POST /draft`

**请求参数变更**:
```json
{
  "tempGenerationId": "a1b2c3d4e5f6...",
  "remark": "用户备注",
  "accountAddCost": 100.00,
  "details": [...]
}
```

### 3. 修改暂存变更单接口

**接口**: `PUT /draft/{id}`

**说明**: 修改时会自动基于原始镜像数据进行，确保数据一致性

## 实现细节

### 1. 核心类和接口

- `PaymentChangeRecordImage`: 镜像表实体类
- `PaymentChangeRecordImageMapper`: 数据访问层
- `IPaymentChangeRecordImageService`: 业务接口
- `PaymentChangeRecordImageServiceImpl`: 业务实现

### 2. 关键方法修改

#### generatePaymentChangeInfo 方法
```java
// 生成临时生成标识
String tempGenerationId = IdUtils.fastSimpleUUID();

// 创建镜像记录
PaymentChangeRecordImage imageRecord = BeanUtil.copyProperties(paymentChangeRecordRes, PaymentChangeRecordImage.class);
imageRecord.setId(IdUtils.fastSimpleUUID());
imageRecord.setTempGenerationId(tempGenerationId);
imageRecord.setOriginalId(null);
imageRecord.setPaymentStatus("0");

// 保存镜像记录
paymentChangeRecordImageService.insertPaymentChangeRecordImage(imageRecord);

// 在返回结果中设置tempGenerationId
paymentChangeRecordRes.setTempGenerationId(tempGenerationId);
```

#### draftPaymentChangeRecord 方法
```java
// 必须提供tempGenerationId
if (StrUtil.isBlank(paymentChangeRecord.getTempGenerationId())) {
    throw new ServiceException("tempGenerationId不能为空，请先调用生成变更信息接口");
}

// 根据tempGenerationId查询镜像数据
PaymentChangeRecordImage imageRecord = paymentChangeRecordImageService
    .selectPaymentChangeRecordImageByTempGenerationId(paymentChangeRecord.getTempGenerationId());

// 基于镜像数据构建主表记录
PaymentChangeRecord draftRecord = BeanUtil.copyProperties(imageRecord, PaymentChangeRecord.class);

// 保存主表记录并更新镜像表的originalId
```

#### updateDraftPaymentChangeRecord 方法
```java
// 查询对应的镜像记录
PaymentChangeRecordImage imageRecord = paymentChangeRecordImageService
    .selectPaymentChangeRecordImageByOriginalId(paymentChangeRecord.getId());

// 基于镜像数据重新构建记录
PaymentChangeRecord updatedRecord = BeanUtil.copyProperties(imageRecord, PaymentChangeRecord.class);
updatedRecord.setId(paymentChangeRecord.getId()); // 保持原ID

// 只允许修改特定字段
if (StrUtil.isNotBlank(paymentChangeRecord.getRemark())) {
    updatedRecord.setRemark(paymentChangeRecord.getRemark());
}
```

## 数据库变更

### 1. 新增镜像表

```sql
CREATE TABLE `t_payment_change_record_image` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `temp_generation_id` varchar(64) DEFAULT NULL COMMENT '临时生成ID，用于关联 /change/info 与 暂存操作',
  `original_id` varchar(64) DEFAULT NULL COMMENT '对应 t_payment_change_record.id，用于反向追溯',
  `contract_number` varchar(100) NOT NULL COMMENT '合同编号，管理表：t_contract_info',
  `live_id` varchar(64) DEFAULT NULL COMMENT '居住id，关联表：t_live_base_info',
  `elderly_id` varchar(64) NOT NULL COMMENT '老人ID',
  `elderly_name` varchar(50) DEFAULT NULL COMMENT '老人姓名',
  `contract_start_date` date DEFAULT NULL COMMENT '合同开始日期',
  `contract_end_date` date DEFAULT NULL COMMENT '合同结束日期',
  `contract_cycle` int DEFAULT NULL COMMENT '合同周期（月数）',
  `care_level` varchar(50) DEFAULT NULL COMMENT '护理级别',
  `bed_name` varchar(50) DEFAULT NULL COMMENT '房间号',
  `account_add_cost` decimal(10,2) DEFAULT NULL COMMENT '本次账户变动金额',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `payment_status` varchar(2) DEFAULT NULL COMMENT '缴费状态，字典：custom_payment_record_status；0：暂存，1：已确认',
  `details` json DEFAULT NULL COMMENT '变更详情（List<Detail> JSON）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_temp_generation_id` (`temp_generation_id`),
  KEY `idx_original_id` (`original_id`),
  KEY `idx_contract_number` (`contract_number`),
  KEY `idx_elderly_id` (`elderly_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='缴费变更单镜像表';
```

### 2. 主表新增字段（仅用于传参）

```java
// PaymentChangeRecord.java 中新增字段（不存储到数据库）
@ApiModelProperty(value = "临时生成ID，用于关联生成接口与暂存操作")
private String tempGenerationId;
```

## 兼容性说明

### 向后兼容

- 系统要求必须传递 `tempGenerationId`，不再支持旧的直接创建暂存单的方式
- 现有的暂存单编辑功能基于镜像数据，确保数据一致性
- 不影响已确认的变更记录

### 数据迁移

- 新增字段均为可空，不影响现有数据
- 镜像表为新增表，不影响主表结构

## 测试用例

系统提供了完整的单元测试用例，覆盖以下场景：

1. 生成变更信息并保存镜像记录
2. 基于镜像数据创建暂存变更单
3. 修改暂存变更单基于原始镜像数据
4. 无效tempGenerationId的异常处理
5. 镜像记录查询功能

测试文件位置：`src/test/java/com/ruoyi/custom/admin/marketing/service/PaymentChangeRecordImageAssociationTest.java`

## 注意事项

1. **前端必须缓存 tempGenerationId**：在调用生成接口后，前端需要保存返回的 tempGenerationId，并在后续暂存操作中传递
2. **镜像数据不可修改**：镜像表中的数据代表原始状态，不应被修改
3. **编辑基于原始数据**：每次编辑暂存单时，都会基于最初的镜像数据进行，避免数据漂移
4. **异常处理**：如果传递了无效的 tempGenerationId，系统会抛出异常
5. **性能考虑**：镜像表已添加必要的索引，查询性能良好

## 故障排查

### 常见问题

1. **tempGenerationId为空**：确保前端正确缓存并传递了生成接口返回的 tempGenerationId
2. **未找到镜像记录**：检查 tempGenerationId 是否正确，或者镜像记录是否已被意外删除
3. **无法修改暂存单**：确保暂存单状态为"0"，且存在对应的镜像记录

### 日志关键字

- `tempGenerationId不能为空`
- `未找到对应的变更信息`
- `未找到对应的镜像记录`
